# PostgreSQL 14 + Redis + MinIO 部署指南

## 快速启动

### 1. 环境配置

复制环境变量文件：
```bash
cp .env.postgres .env
```

编辑 `.env` 文件，修改密码等配置：
```bash
# PostgreSQL 配置
POSTGRES_PASSWORD=your_secure_password
POSTGRES_PORT=5432

# Redis 配置
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# MinIO 配置
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=your_secure_minio_password
MINIO_API_PORT=9000
REDIS_PASSWORD=your_redis_password
```

### 2. 启动服务

```bash
# 启动 PostgreSQL、Redis 和 MinIO
docker-compose -f docker-compose.postgres.yml --env-file .env up -d

# 查看服务状态
docker-compose -f docker-compose.postgres.yml ps

# 查看日志
docker-compose -f docker-compose.postgres.yml logs -f
```

### 3. 连接数据库

**使用 psql 连接：**
```bash
# 进入 PostgreSQL 容器
docker exec -it erp_postgres psql -U postgres -d erp_finance

# 或者从宿主机连接
psql -h localhost -p 5432 -U postgres -d erp_finance
```

**连接信息：**
- 主机：localhost
- 端口：5432
- 数据库：erp_finance
- 用户名：postgres
- 密码：Postgres!23（或你设置的密码）

### 4. Redis 连接

```bash
# 进入 Redis 容器（需要密码认证）
docker exec -it erp_redis redis-cli -a Redis!23

# 或者从宿主机连接
redis-cli -h localhost -p 6379 -a Redis!23
```

**Redis连接信息：**
- 主机：localhost
- 端口：6379
- 密码：Redis!23（或你设置的密码）

### 5. MinIO 对象存储

**MinIO Web界面：**
```bash
# 访问 MinIO Web界面
http://localhost:9001
```

**MinIO 连接信息：**
- Web界面：http://localhost:9001
- API端点：http://localhost:9000
- 用户名：admin
- 密码：MinIO!2024@ERP

**使用 MinIO 客户端：**
```bash
# 安装 MinIO 客户端
curl https://dl.min.io/client/mc/release/linux-amd64/mc -o mc
chmod +x mc

# 配置连接
./mc alias set local http://localhost:9000 admin MinIO!2024@ERP

# 创建存储桶
./mc mb local/erp-finance

# 列出存储桶
./mc ls local/
```

## 📊 服务信息

- **PostgreSQL**: 端口5432，数据库名`erp_finance`
- **Redis**: 端口6379，配置了持久化和密码认证
- **MinIO**: API端口9000，Web界面端口9001，对象存储服务
- **网络**: 独立网络`erp_network`
- **数据卷**: 自动创建持久化存储

## 服务管理

### 停止服务
```bash
docker-compose -f docker-compose.postgres.yml down
```

### 重启服务
```bash
docker-compose -f docker-compose.postgres.yml restart
```

### 删除数据（谨慎操作）
```bash
# 停止并删除容器和数据卷
docker-compose -f docker-compose.postgres.yml down -v
```

## 数据备份与恢复

### 备份数据库
```bash
# 备份到文件
docker exec erp_postgres pg_dump -U postgres erp_finance > backup.sql

# 或者使用 docker-compose
docker-compose -f docker-compose.postgres.yml exec postgres pg_dump -U postgres erp_finance > backup.sql
```

### 恢复数据库
```bash
# 从备份文件恢复
docker exec -i erp_postgres psql -U postgres erp_finance < backup.sql
```

## 监控和维护

### 查看数据库状态
```bash
# 进入数据库查看连接数
docker exec -it erp_postgres psql -U postgres -d erp_finance -c "SELECT count(*) FROM pg_stat_activity;"

# 查看数据库大小
docker exec -it erp_postgres psql -U postgres -d erp_finance -c "SELECT pg_size_pretty(pg_database_size('erp_finance'));"
```

### 查看 Redis 状态
```bash
# 查看 Redis 信息（需要密码）
docker exec -it erp_redis redis-cli -a Redis!23 info

# 查看内存使用
docker exec -it erp_redis redis-cli -a Redis!23 info memory
```

### 查看 MinIO 状态
```bash
# 查看 MinIO 服务状态
curl -f http://localhost:9000/minio/health/live

# 访问 MinIO Web界面
open http://localhost:9001
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 修改 `.env` 文件中的端口配置
   - 确保端口未被其他服务占用

2. **权限问题**
   - 确保 Docker 有足够权限
   - 检查数据卷权限

3. **连接失败**
   - 检查防火墙设置
   - 确认服务已正常启动
   - 查看容器日志排查问题

### 查看日志
```bash
# 查看 PostgreSQL 日志
docker-compose -f docker-compose.postgres.yml logs postgres

# 查看 Redis 日志
docker-compose -f docker-compose.postgres.yml logs redis
```

## 生产环境建议

1. **安全配置**
   - 修改默认密码
   - 限制网络访问
   - 启用 SSL/TLS

2. **性能优化**
   - 根据服务器配置调整 PostgreSQL 参数
   - 配置适当的内存限制

3. **备份策略**
   - 定期自动备份
   - 异地备份存储
   - 定期测试恢复流程

4. **监控告警**
   - 配置资源监控
   - 设置告警阈值
   - 日志收集和分析
