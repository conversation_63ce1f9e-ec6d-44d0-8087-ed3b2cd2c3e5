# 业财一体化系统 - 后端服务

## 项目介绍

业财一体化系统后端服务，基于Django 4.2 + Django REST Framework开发，提供完整的企业业务流程和财务管理API接口。

## 技术栈

- **框架**: Django 4.2 + Django REST Framework
- **数据库**: PostgreSQL 14+
- **缓存**: Redis 6+
- **任务队列**: Celery
- **API文档**: drf-yasg (Swagger)
- **部署**: Docker + Docker Compose

## 功能模块

1. **客户管理模块** - 客户信息、联系记录、发票信息管理
2. **项目管理模块** - 项目、任务、里程碑、成员管理
3. **合同管理模块** - 销售合同、采购合同、变更管理
4. **供应商管理模块** - 供应商档案、评估、合作记录
5. **文件管理模块** - 文档上传、分类、权限控制
6. **仪表盘统计模块** - 业务数据统计和可视化

## 快速开始

### 环境要求

- Python 3.10+
- PostgreSQL 14+
- Redis 6+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd backend
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息
```

5. **数据库迁移**
```bash
python manage.py makemigrations
python manage.py migrate
```

6. **启动开发服务器**
```bash
python manage.py runserver
```

### Docker部署

1. **使用Docker Compose启动**
```bash
docker-compose up -d
```

2. **执行数据库迁移**
```bash
docker-compose exec web python manage.py migrate
```

## API文档

启动服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/swagger/
- ReDoc: http://localhost:8000/redoc/

## 项目结构

```
backend/
├── application/          # Django项目配置
│   ├── __init__.py
│   ├── settings.py      # 项目设置
│   ├── urls.py          # 主路由配置
│   ├── wsgi.py          # WSGI配置
│   ├── asgi.py          # ASGI配置
│   └── celery.py        # Celery配置
├── conf/                # 配置文件
│   └── env.py           # 环境变量配置
├── erp/                 # 主应用
│   ├── models/          # 数据模型
│   ├── views/           # 视图
│   ├── serializers/     # 序列化器
│   ├── utils/           # 工具类
│   ├── management/      # 管理命令
│   ├── migrations/      # 数据库迁移
│   └── swagger/         # API文档
├── docs/                # 项目文档
├── test/                # 测试文件
├── requirements.txt     # Python依赖
├── manage.py           # Django管理脚本
├── docker-compose.yml  # Docker编排文件
├── Dockerfile          # Docker镜像构建文件
└── README.md           # 项目说明
```

## 开发规范

### 代码规范

- 遵循PEP 8代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档字符串

### API设计规范

- 使用RESTful API设计原则
- 统一的响应格式
- 完善的错误处理
- 详细的API文档

### 数据库规范

- 使用UUID作为主键
- 软删除机制
- 创建和更新时间戳
- 合理的索引设计

## 部署说明

### 生产环境部署

1. **环境配置**
   - 设置 `DEBUG=false`
   - 配置生产数据库
   - 设置安全的SECRET_KEY

2. **静态文件处理**
```bash
python manage.py collectstatic
```

3. **数据库迁移**
```bash
python manage.py migrate
```

4. **启动服务**
```bash
# 使用gunicorn启动
gunicorn application.wsgi:application --bind 0.0.0.0:8000
```

## 监控和日志

- 应用日志输出到stdout（Docker最佳实践）
- 使用Django的logging配置
- 生产环境建议配置日志收集系统（如ELK、Fluentd等）

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License
