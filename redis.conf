# Redis 配置文件
# 绑定地址
bind 0.0.0.0

# 端口
port 6379

# 后台运行
daemonize no

# 密码认证
requirepass Redis!23

# 数据持久化
save 900 1
save 300 10
save 60 10000

# RDB文件名
dbfilename dump.rdb

# 工作目录
dir /data

# 日志级别
loglevel notice

# 最大内存
maxmemory 512mb

# 内存淘汰策略
maxmemory-policy allkeys-lru

# 启用AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 禁用保护模式（仅在Docker内部网络使用）
protected-mode no

# 超时时间
timeout 300

# TCP keepalive
tcp-keepalive 300

# 客户端连接数限制
maxclients 10000
