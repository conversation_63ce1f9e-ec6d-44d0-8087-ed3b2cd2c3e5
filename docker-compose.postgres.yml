version: '3.8'

services:
  # PostgreSQL 14 数据库
  postgres:
    image: postgres:14
    container_name: erp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: erp_finance
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-Postgres!23}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TZ: Asia/Shanghai
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d erp_finance"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 缓存
  redis:
    image: redis:6-alpine
    container_name: erp_redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
      REDIS_PASSWORD: ${REDIS_PASSWORD:-Redis!23}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - erp_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-Redis!23}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # MinIO 对象存储 (使用稳定版本)
  minio:
    image: minio/minio:RELEASE.2023-05-27T05-56-19Z
    container_name: erp_minio
    restart: unless-stopped
    environment:
      MINIO_ACCESS_KEY: ${MINIO_ROOT_USER:-admin}
      MINIO_SECRET_KEY: ${MINIO_ROOT_PASSWORD:-MinIO!2024@ERP}
      TZ: Asia/Shanghai
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - erp_network
    command: server /data --address ":9000" --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/ready"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

  # RUSTFS 对象存储
  rustfs:
    image: rustfs/rustfs:1.0.0-alpha.33
    container_name: erp_rustfs
    restart: unless-stopped
    environment:
      RUSTFS_ACCESS_KEY: ${RUSTFS_ACCESS_KEY:-admin}
      RUSTFS_SECRET_KEY: ${RUSTFS_SECRET_KEY:-Ul5-s9Nt4}
      RUSTFS_CONSOLE_ENABLE: ${RUSTFS_CONSOLE_ENABLE:-true}
      TZ: Asia/Shanghai
    ports:
      - "${RUSTFS_PORT:-9002}:9000"
    volumes:
      - rustfs_data:/data
    networks:
      - erp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  rustfs_data:
    driver: local

networks:
  erp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
