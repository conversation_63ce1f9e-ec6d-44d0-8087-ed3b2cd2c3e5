FROM python:3.12-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    # PostgreSQL客户端
    postgresql-client \
    # Redis客户端
    redis-tools \
    # 编译工具
    build-essential \
    gcc \
    # PostgreSQL开发库
    libpq-dev \
    # 其他必要工具
    curl \
    wget \
    git \
    # 时区数据
    tzdata \
    vim \
    # 进程管理工具
    procps \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 升级pip并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# 复制启动脚本并设置权限
COPY startup.sh /app/startup.sh
RUN chmod +x /app/startup.sh

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/static /app/media /app/logs

# 创建非root用户（安全最佳实践）
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# 启动命令
CMD ["/app/startup.sh"]
