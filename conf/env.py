import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent

# ================================================= #
# *************** 数据库 配置  *************** #
# ================================================= #

# PostgreSQL数据库配置
DATABASE_NAME = os.getenv('DATABASE_NAME', 'erp_finance')
DATABASE_HOST = os.getenv('DATABASE_HOST', '************')
DATABASE_PORT = os.getenv('DATABASE_PORT', '5432')
DATABASE_USER = os.getenv('DATABASE_USER', 'postgres')
DATABASE_PASSWORD = os.getenv('DATABASE_PASSWORD', 'Postgres!23')

# 表前缀
TABLE_PREFIX = "erp_"

# ================================================= #
# ******** redis配置，无redis 可不进行配置  ******** #
# ================================================= #
REDIS_HOST = os.getenv('REDIS_HOST', '************')
REDIS_PORT = os.getenv('REDIS_PORT', '6379')
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', 'Redis!23')
REDIS_DB = os.getenv('REDIS_DB', '0')

# ================================================= #
# ****************** 功能 启停  ******************* #
# ================================================= #
DEBUG = os.getenv('DEBUG', 'true')  # true or false
if DEBUG == "True" or DEBUG == "true":
    DEBUG = True
else:
    DEBUG = False

# 启动登录详细概略获取(通过调用api获取ip详细地址。如果是内网，关闭即可)
ENABLE_LOGIN_ANALYSIS_LOG = True
# 登录接口 /api/token/ 是否需要验证码认证，用于测试，正式环境建议取消
LOGIN_NO_CAPTCHA_AUTH = True

# ================================================= #
# ****************** 存储配置  ******************* #
# ================================================= #

# 本地文件存储配置
LOCAL_FILE_STORAGE_PATH = os.getenv('LOCAL_FILE_STORAGE_PATH',
                                   os.path.join(os.path.dirname(os.path.dirname(__file__)), 'media', 'attachments'))

# 外部存储配置已移除，现在使用本地文件系统存储

# ================================================= #
# ****************** 其他 配置  ******************* #
# ================================================= #

ALLOWED_HOSTS = ["*"]
