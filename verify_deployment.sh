#!/bin/bash

# 业财一体化系统部署验证脚本
# 用于验证 PostgreSQL + Redis + MinIO 服务是否正常运行

echo "🚀 开始验证部署状态..."
echo "============================================================"

# 检查 Docker 和 Docker Compose
echo "📋 检查环境依赖..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装或未在 PATH 中"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装或未在 PATH 中"
    exit 1
fi

echo "✅ Docker 和 Docker Compose 已安装"

# 检查服务状态
echo ""
echo "📊 检查服务状态..."
docker-compose -f docker-compose.postgres.yml ps

# 检查各服务健康状态
echo ""
echo "🔍 检查服务健康状态..."

# PostgreSQL
echo "检查 PostgreSQL..."
if docker exec erp_postgres pg_isready -U postgres -d erp_finance &> /dev/null; then
    echo "✅ PostgreSQL 服务正常"
else
    echo "❌ PostgreSQL 服务异常"
fi

# Redis
echo "检查 Redis..."
if docker exec erp_redis redis-cli -a Redis!23 ping &> /dev/null; then
    echo "✅ Redis 服务正常"
else
    echo "❌ Redis 服务异常"
fi

# MinIO
echo "检查 MinIO..."
if curl -f http://localhost:9000/minio/health/live &> /dev/null; then
    echo "✅ MinIO 服务正常"
else
    echo "❌ MinIO 服务异常"
fi

# 检查端口占用
echo ""
echo "🔌 检查端口占用..."
ports=(5432 6379 9000 9001)
for port in "${ports[@]}"; do
    if netstat -tuln | grep ":$port " &> /dev/null; then
        echo "✅ 端口 $port 已被占用（服务正在运行）"
    else
        echo "❌ 端口 $port 未被占用（服务可能未启动）"
    fi
done

# 检查数据卷
echo ""
echo "💾 检查数据卷..."
volumes=$(docker volume ls | grep -E "(postgres_data|redis_data|minio_data)" | wc -l)
if [ "$volumes" -eq 3 ]; then
    echo "✅ 所有数据卷已创建"
else
    echo "⚠️  数据卷创建不完整，发现 $volumes/3 个"
fi

# 显示服务访问信息
echo ""
echo "🌐 服务访问信息:"
echo "   PostgreSQL: localhost:5432"
echo "   Redis: localhost:6379"
echo "   MinIO API: http://localhost:9000"
echo "   MinIO Web界面: http://localhost:9001"

echo ""
echo "🔑 默认认证信息:"
echo "   PostgreSQL: postgres / Postgres!23"
echo "   Redis: Redis!23"
echo "   MinIO: admin / MinIO!2024@ERP"

echo ""
echo "============================================================"
echo "✅ 部署验证完成！"
echo "💡 如需详细测试，请运行: python test_connections.py"
