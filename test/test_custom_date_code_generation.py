#!/usr/bin/env python
"""
测试自定义日期编码生成功能
"""

import os
import sys
import django
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from erp.models import Customer, Supplier, Project, Contract
from erp.serializers.customer import CustomerCreateUpdateSerializer
from erp.serializers.supplier import SupplierCreateUpdateSerializer


def test_customer_custom_date_code():
    """测试客户自定义日期编码生成"""
    print("=" * 60)
    print("测试客户自定义日期编码生成")
    print("=" * 60)
    
    # 清理测试数据（使用硬删除方式）
    from django.utils import timezone

    # 按照依赖关系顺序删除
    # 先删除合同（依赖于项目和客户/供应商）
    Contract.objects.filter(code__contains='C230315').delete()
    Contract.objects.filter(code__contains='C230101').delete()

    # 再删除项目（依赖于客户）
    Project.objects.filter(code__contains='C230315').delete()
    Project.objects.filter(code__contains='C230101').delete()

    # 最后删除客户和供应商
    Customer.objects.filter(code__contains='230315').delete()
    Customer.objects.filter(code__contains='230101').delete()
    Supplier.objects.filter(code__contains='230615').delete()
    
    # 测试1: 使用自定义日期创建客户
    print("\n1. 测试使用自定义日期创建客户")
    custom_date = '2023-01-01'
    
    customer_data = {
        'name': '历史客户1',
        'type': 'C',
        'industry': 'IT',
        'contact_person': '张三',
        'phone': '13800138001',
        'code_date': custom_date
    }
    
    serializer = CustomerCreateUpdateSerializer(data=customer_data)
    if serializer.is_valid():
        customer = serializer.save()
        print(f"创建客户成功: {customer.code} - {customer.name}")
        # 验证编码是否使用了自定义日期
        expected_prefix = 'C230101'  # 2023年1月1日对应的编码前缀
        if customer.code.startswith(expected_prefix):
            print(f"✓ 编码正确使用了自定义日期: {customer.code}")
        else:
            print(f"✗ 编码未使用自定义日期，期望前缀: {expected_prefix}, 实际: {customer.code}")
    else:
        print(f"创建客户失败: {serializer.errors}")
    
    # 测试2: 同一天创建多个客户，验证序号递增
    print("\n2. 测试同一天创建多个客户，验证序号递增")
    customer_data2 = {
        'name': '历史客户2',
        'type': 'C',
        'industry': 'FINANCE',
        'contact_person': '李四',
        'phone': '13800138002',
        'code_date': custom_date
    }
    
    serializer2 = CustomerCreateUpdateSerializer(data=customer_data2)
    if serializer2.is_valid():
        customer2 = serializer2.save()
        print(f"创建客户成功: {customer2.code} - {customer2.name}")
        # 验证序号是否递增
        if customer2.code.endswith('002'):
            print(f"✓ 序号正确递增: {customer2.code}")
        else:
            print(f"✗ 序号未正确递增: {customer2.code}")
    else:
        print(f"创建客户失败: {serializer2.errors}")
    
    # 测试3: 不提供自定义日期，使用当前日期
    print("\n3. 测试不提供自定义日期，使用当前日期")
    customer_data3 = {
        'name': '当前客户',
        'type': 'G',
        'industry': 'GOVT',
        'contact_person': '王五',
        'phone': '13800138003'
    }
    
    serializer3 = CustomerCreateUpdateSerializer(data=customer_data3)
    if serializer3.is_valid():
        customer3 = serializer3.save()
        print(f"创建客户成功: {customer3.code} - {customer3.name}")
        # 验证编码是否使用了当前日期
        today_prefix = f"G{datetime.now().strftime('%y%m%d')}"
        if customer3.code.startswith(today_prefix):
            print(f"✓ 编码正确使用了当前日期: {customer3.code}")
        else:
            print(f"✗ 编码未使用当前日期，期望前缀: {today_prefix}, 实际: {customer3.code}")
    else:
        print(f"创建客户失败: {serializer3.errors}")


def test_supplier_custom_date_code():
    """测试供应商自定义日期编码生成"""
    print("\n" + "=" * 60)
    print("测试供应商自定义日期编码生成")
    print("=" * 60)
    
    # 清理测试数据（使用软删除方式）
    from django.utils import timezone
    Supplier.objects.update(delete_datetime=timezone.now())
    
    # 测试1: 使用自定义日期创建供应商
    print("\n1. 测试使用自定义日期创建供应商")
    custom_date = '2023-06-15'
    
    supplier_data = {
        'name': '历史供应商1',
        'type': 'ORIGINAL',
        'status': 'ACTIVE',
        'industry': 'HARDWARE',
        'contact_person': '赵六',
        'phone': '13800138004',
        'owner_name': '负责人1',
        'code_date': custom_date
    }
    
    serializer = SupplierCreateUpdateSerializer(data=supplier_data)
    if serializer.is_valid():
        supplier = serializer.save()
        print(f"创建供应商成功: {supplier.code} - {supplier.name}")
        # 验证编码是否使用了自定义日期
        expected_prefix = 'V230615'  # 2023年6月15日对应的编码前缀
        if supplier.code.startswith(expected_prefix):
            print(f"✓ 编码正确使用了自定义日期: {supplier.code}")
        else:
            print(f"✗ 编码未使用自定义日期，期望前缀: {expected_prefix}, 实际: {supplier.code}")
    else:
        print(f"创建供应商失败: {serializer.errors}")
    
    # 测试2: 同一天创建多个供应商，验证序号递增
    print("\n2. 测试同一天创建多个供应商，验证序号递增")
    supplier_data2 = {
        'name': '历史供应商2',
        'type': 'CHANNEL',
        'status': 'ACTIVE',
        'industry': 'SOFTWARE',
        'contact_person': '孙七',
        'phone': '13800138005',
        'owner_name': '负责人2',
        'code_date': custom_date
    }
    
    serializer2 = SupplierCreateUpdateSerializer(data=supplier_data2)
    if serializer2.is_valid():
        supplier2 = serializer2.save()
        print(f"创建供应商成功: {supplier2.code} - {supplier2.name}")
        # 验证序号是否递增
        if supplier2.code.endswith('002'):
            print(f"✓ 序号正确递增: {supplier2.code}")
        else:
            print(f"✗ 序号未正确递增: {supplier2.code}")
    else:
        print(f"创建供应商失败: {serializer2.errors}")


def test_project_contract_code_inheritance():
    """测试项目和合同编码继承客户编码"""
    print("\n" + "=" * 60)
    print("测试项目和合同编码继承客户编码")
    print("=" * 60)
    
    # 清理测试数据（使用硬删除方式）
    from django.utils import timezone

    # 按照依赖关系顺序删除
    # 先删除合同（依赖于项目和客户/供应商）
    Contract.objects.filter(code__contains='C230315').delete()

    # 再删除项目（依赖于客户）
    Project.objects.filter(code__contains='C230315').delete()

    # 最后删除客户
    Customer.objects.filter(code__contains='230315').delete()
    
    # 创建使用自定义日期的客户
    print("\n1. 创建使用自定义日期的客户")
    custom_date = '2023-03-15'
    
    customer_data = {
        'name': '历史客户项目测试',
        'type': 'C',
        'industry': 'IT',
        'contact_person': '项目经理',
        'phone': '13800138006',
        'code_date': custom_date
    }
    
    serializer = CustomerCreateUpdateSerializer(data=customer_data)
    if serializer.is_valid():
        customer = serializer.save()
        print(f"创建客户成功: {customer.code} - {customer.name}")
        
        # 创建项目
        print("\n2. 创建关联项目")
        project = Project.objects.create(
            name='历史项目测试',
            type='system_integration',
            reason='signed',
            customer=customer,
            start_date=date(2023, 3, 20),
            end_date=date(2023, 6, 20),
            budget=100000,
            expected_profit_rate=20,
            progress=0
        )
        print(f"创建项目成功: {project.code} - {project.name}")
        
        # 验证项目编码是否包含客户编码
        if customer.code in project.code:
            print(f"✓ 项目编码正确包含客户编码: {project.code}")
        else:
            print(f"✗ 项目编码未包含客户编码: {project.code}")
        
        # 创建销售合同
        print("\n3. 创建销售合同")
        sales_contract = Contract.objects.create(
            name='历史销售合同测试',
            category='sales',
            project=project,
            customer=customer,
            amount=100000,
            sign_date=date(2023, 3, 25)
        )
        print(f"创建销售合同成功: {sales_contract.code} - {sales_contract.name}")
        
        # 验证合同编码是否包含项目编码
        if project.code in sales_contract.code:
            print(f"✓ 合同编码正确包含项目编码: {sales_contract.code}")
        else:
            print(f"✗ 合同编码未包含项目编码: {sales_contract.code}")
            
    else:
        print(f"创建客户失败: {serializer.errors}")


def test_invalid_date_format():
    """测试无效日期格式"""
    print("\n" + "=" * 60)
    print("测试无效日期格式")
    print("=" * 60)
    
    # 测试无效日期格式
    print("\n1. 测试无效日期格式")
    customer_data = {
        'name': '测试客户',
        'type': 'C',
        'industry': 'IT',
        'contact_person': '测试人员',
        'phone': '13800138007',
        'code_date': '2023/01/01'  # 错误格式
    }
    
    serializer = CustomerCreateUpdateSerializer(data=customer_data)
    if serializer.is_valid():
        try:
            customer = serializer.save()
            print(f"✗ 应该失败但成功了: {customer.code}")
        except ValueError as e:
            print(f"✓ 正确捕获了日期格式错误: {e}")
    else:
        print(f"序列化器验证失败: {serializer.errors}")


if __name__ == '__main__':
    print("开始测试自定义日期编码生成功能...")
    
    try:
        test_customer_custom_date_code()
        test_supplier_custom_date_code()
        test_project_contract_code_inheritance()
        test_invalid_date_format()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
