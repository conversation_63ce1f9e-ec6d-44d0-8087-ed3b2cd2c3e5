#!/usr/bin/env python
"""
项目编码生成测试脚本
用于验证项目编码是否按照正确格式生成：P + 3位序号 + "-" + 客户编号
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from erp.models import Project, Customer


def test_project_code_generation():
    """测试项目编码生成"""
    print("=" * 60)
    print("项目编码生成测试")
    print("=" * 60)
    
    # 清理测试数据
    Project.objects.all().delete()
    Customer.objects.all().delete()
    
    # 创建测试客户
    customer1 = Customer.objects.create(
        name="测试客户1",
        code="C250606001",
        type="C",
        contact_person="张三",
        phone="13800138001",
        address="北京市朝阳区",
        tax_id="91110000000000001X"
    )

    customer2 = Customer.objects.create(
        name="政府客户1",
        code="G250607002",
        type="G",
        contact_person="李四",
        phone="13800138002",
        address="上海市浦东新区",
        tax_id="91310000000000002Y"
    )
    
    print(f"创建客户1: {customer1.code} - {customer1.name}")
    print(f"创建客户2: {customer2.code} - {customer2.name}")
    print()
    
    # 创建测试项目
    projects_data = [
        {
            "name": "系统集成项目1",
            "customer": customer1,
            "type": "system_integration",
            "status": "preparing",
            "reason": "signed",
            "end_user_name": "最终用户1",
            "sales_manager_id": "MGR001",
            "sales_manager_name": "销售经理1",
            "expected_profit_rate": 25.0,
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        },
        {
            "name": "软件开发项目1",
            "customer": customer2,
            "type": "software_development",
            "status": "preparing",
            "reason": "bidding",
            "end_user_name": "最终用户2",
            "sales_manager_id": "MGR002",
            "sales_manager_name": "销售经理2",
            "expected_profit_rate": 30.0,
            "start_date": "2024-02-01",
            "end_date": "2024-11-30"
        },
        {
            "name": "产品销售项目1",
            "customer": customer1,
            "type": "product_own",
            "status": "preparing",
            "reason": "poc",
            "end_user_name": "最终用户3",
            "sales_manager_id": "MGR003",
            "sales_manager_name": "销售经理3",
            "expected_profit_rate": 20.0,
            "start_date": "2024-03-01",
            "end_date": "2024-10-31"
        }
    ]
    
    created_projects = []
    for i, data in enumerate(projects_data, 1):
        project = Project.objects.create(**data)
        created_projects.append(project)
        print(f"项目{i}: {project.code} - {project.name}")
        print(f"  客户: {project.customer_code} - {project.customer_name}")
        print(f"  格式验证: {'✓' if validate_project_code_format(project.code) else '✗'}")
        print()
    
    # 验证编码格式
    print("编码格式验证:")
    print("-" * 40)
    
    expected_codes = [
        "P001-C250606001",
        "P002-G250607002", 
        "P003-C250606001"
    ]
    
    all_valid = True
    for i, project in enumerate(created_projects):
        expected = expected_codes[i]
        actual = project.code
        is_valid = actual == expected
        all_valid = all_valid and is_valid
        
        print(f"项目{i+1}:")
        print(f"  期望: {expected}")
        print(f"  实际: {actual}")
        print(f"  结果: {'✓ 正确' if is_valid else '✗ 错误'}")
        print()
    
    # 测试序号递增
    print("序号递增测试:")
    print("-" * 40)
    
    # 再创建一个项目，验证序号是否正确递增
    project4 = Project.objects.create(
        name="测试序号递增项目",
        customer=customer2,
        type="service_own",
        status="preparing",
        reason="signed",
        end_user_name="最终用户4",
        sales_manager_id="MGR004",
        sales_manager_name="销售经理4",
        expected_profit_rate=15.0,
        start_date="2024-04-01",
        end_date="2024-09-30"
    )
    
    expected_code4 = "P004-G250607002"
    is_valid4 = project4.code == expected_code4
    
    print(f"第4个项目:")
    print(f"  期望: {expected_code4}")
    print(f"  实际: {project4.code}")
    print(f"  结果: {'✓ 正确' if is_valid4 else '✗ 错误'}")
    print()
    
    # 总结
    final_result = all_valid and is_valid4
    print("=" * 60)
    print(f"测试结果: {'✓ 全部通过' if final_result else '✗ 存在错误'}")
    print("=" * 60)
    
    return final_result


def validate_project_code_format(code):
    """验证项目编码格式"""
    import re
    # 格式：P + 3位数字 + "-" + 客户编号
    pattern = r'^P\d{3}-[A-Z]\d{9}$'
    return bool(re.match(pattern, code))


if __name__ == "__main__":
    try:
        success = test_project_code_generation()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
