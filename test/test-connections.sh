#!/bin/bash

# 业财一体化系统 - 连接测试脚本
# 用于测试数据库和Redis连接配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试PostgreSQL连接
test_postgres() {
    log_step "测试PostgreSQL连接..."
    
    # 获取数据库配置
    DB_HOST=${DATABASE_HOST:-localhost}
    DB_PORT=${DATABASE_PORT:-5432}
    DB_NAME=${DATABASE_NAME:-erp_finance}
    DB_USER=${DATABASE_USER:-postgres}
    DB_PASSWORD=${DATABASE_PASSWORD:-}
    
    log_info "数据库配置:"
    log_info "  主机: $DB_HOST"
    log_info "  端口: $DB_PORT"
    log_info "  数据库: $DB_NAME"
    log_info "  用户: $DB_USER"
    log_info "  密码: ${DB_PASSWORD:+已设置}"
    
    # 设置密码环境变量
    if [ -n "$DB_PASSWORD" ]; then
        export PGPASSWORD="$DB_PASSWORD"
    fi
    
    # 测试连接
    if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        log_info "✅ PostgreSQL连接成功"
        
        # 测试实际查询
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" >/dev/null 2>&1; then
            log_info "✅ PostgreSQL查询测试成功"
        else
            log_warn "⚠️  PostgreSQL查询测试失败"
        fi
    else
        log_error "❌ PostgreSQL连接失败"
        return 1
    fi
    
    # 清除密码环境变量
    unset PGPASSWORD
}

# 测试Redis连接
test_redis() {
    log_step "测试Redis连接..."
    
    REDIS_HOST=${REDIS_HOST:-localhost}
    REDIS_PORT=${REDIS_PORT:-6379}
    REDIS_PASSWORD=${REDIS_PASSWORD:-}
    
    log_info "Redis配置:"
    log_info "  主机: $REDIS_HOST"
    log_info "  端口: $REDIS_PORT"
    log_info "  密码: ${REDIS_PASSWORD:+已设置}"
    
    if [ -n "$REDIS_PASSWORD" ]; then
        # 有密码的情况
        if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
            log_info "✅ Redis连接成功（带密码）"
            
            # 测试基本操作
            if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" set test_key "test_value" >/dev/null 2>&1; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" del test_key >/dev/null 2>&1
                log_info "✅ Redis读写测试成功"
            else
                log_warn "⚠️  Redis读写测试失败"
            fi
        else
            log_error "❌ Redis连接失败（带密码）"
            return 1
        fi
    else
        # 无密码的情况
        if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
            log_info "✅ Redis连接成功（无密码）"
            
            # 测试基本操作
            if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" set test_key "test_value" >/dev/null 2>&1; then
                redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" del test_key >/dev/null 2>&1
                log_info "✅ Redis读写测试成功"
            else
                log_warn "⚠️  Redis读写测试失败"
            fi
        else
            log_error "❌ Redis连接失败（无密码）"
            return 1
        fi
    fi
}

# 测试Django数据库配置
test_django_db() {
    log_step "测试Django数据库配置..."
    
    if python manage.py check --database default >/dev/null 2>&1; then
        log_info "✅ Django数据库配置正确"
    else
        log_error "❌ Django数据库配置错误"
        log_info "详细错误信息:"
        python manage.py check --database default
        return 1
    fi
}

# 主函数
main() {
    log_info "========================================"
    log_info "业财一体化系统 - 连接测试"
    log_info "========================================"
    
    # 显示环境信息
    log_info "当前环境变量:"
    log_info "  DATABASE_HOST: ${DATABASE_HOST:-未设置}"
    log_info "  DATABASE_PORT: ${DATABASE_PORT:-未设置}"
    log_info "  DATABASE_NAME: ${DATABASE_NAME:-未设置}"
    log_info "  DATABASE_USER: ${DATABASE_USER:-未设置}"
    log_info "  DATABASE_PASSWORD: ${DATABASE_PASSWORD:+已设置}"
    log_info "  REDIS_HOST: ${REDIS_HOST:-未设置}"
    log_info "  REDIS_PORT: ${REDIS_PORT:-未设置}"
    log_info "  REDIS_PASSWORD: ${REDIS_PASSWORD:+已设置}"
    echo
    
    # 执行测试
    FAILED=0
    
    # 测试PostgreSQL
    if ! test_postgres; then
        FAILED=$((FAILED + 1))
    fi
    echo
    
    # 测试Redis
    if ! test_redis; then
        FAILED=$((FAILED + 1))
    fi
    echo
    
    # 测试Django配置
    if ! test_django_db; then
        FAILED=$((FAILED + 1))
    fi
    echo
    
    # 总结
    if [ $FAILED -eq 0 ]; then
        log_info "🎉 所有连接测试通过！"
        exit 0
    else
        log_error "❌ $FAILED 个测试失败"
        exit 1
    fi
}

# 检查必要的命令
check_commands() {
    local missing=0
    
    if ! command -v pg_isready >/dev/null 2>&1; then
        log_error "pg_isready 命令未找到，请安装 postgresql-client"
        missing=$((missing + 1))
    fi
    
    if ! command -v psql >/dev/null 2>&1; then
        log_error "psql 命令未找到，请安装 postgresql-client"
        missing=$((missing + 1))
    fi
    
    if ! command -v redis-cli >/dev/null 2>&1; then
        log_error "redis-cli 命令未找到，请安装 redis-tools"
        missing=$((missing + 1))
    fi
    
    if ! command -v python >/dev/null 2>&1; then
        log_error "python 命令未找到"
        missing=$((missing + 1))
    fi
    
    if [ $missing -gt 0 ]; then
        log_error "缺少必要的命令，请先安装相关工具"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "业财一体化系统 - 连接测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo
    echo "环境变量:"
    echo "  DATABASE_HOST     数据库主机 (默认: localhost)"
    echo "  DATABASE_PORT     数据库端口 (默认: 5432)"
    echo "  DATABASE_NAME     数据库名称 (默认: erp_finance)"
    echo "  DATABASE_USER     数据库用户 (默认: postgres)"
    echo "  DATABASE_PASSWORD 数据库密码"
    echo "  REDIS_HOST        Redis主机 (默认: localhost)"
    echo "  REDIS_PORT        Redis端口 (默认: 6379)"
    echo "  REDIS_PASSWORD    Redis密码"
    echo
    echo "示例:"
    echo "  # 使用环境变量"
    echo "  export DATABASE_PASSWORD=your_password"
    echo "  export REDIS_PASSWORD=your_redis_password"
    echo "  $0"
    echo
    echo "  # 使用.env文件"
    echo "  source .env"
    echo "  $0"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行检查和测试
check_commands
main
