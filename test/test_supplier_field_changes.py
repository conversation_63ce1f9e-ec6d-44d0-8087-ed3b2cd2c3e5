#!/usr/bin/env python
"""
测试供应商字段选项更改
"""

import os
import sys
import django
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from erp.models import Supplier
from erp.serializers.supplier import SupplierCreateUpdateSerializer


def test_supplier_field_choices():
    """测试供应商字段选项"""
    print("=" * 60)
    print("测试供应商字段选项更改")
    print("=" * 60)
    
    # 清理测试数据
    from django.utils import timezone
    Supplier.objects.filter(name__contains='测试供应商字段').delete()
    
    # 测试1: 验证新的供应商类型选项
    print("\n1. 测试新的供应商类型选项")
    
    # 测试原厂类型
    supplier_data_original = {
        'name': '测试供应商字段-原厂',
        'type': 'ORIGINAL',
        'status': 'ACTIVE',
        'industry': 'HARDWARE',
        'contact_person': '张三',
        'phone': '13800138001',
        'owner_name': '负责人1'
    }
    
    serializer = SupplierCreateUpdateSerializer(data=supplier_data_original)
    if serializer.is_valid():
        supplier = serializer.save()
        print(f"✓ 原厂类型供应商创建成功: {supplier.code} - {supplier.name}")
        print(f"  类型: {supplier.type} ({supplier.get_type_display()})")
        print(f"  行业: {supplier.industry} ({supplier.get_industry_display()})")
    else:
        print(f"✗ 原厂类型供应商创建失败: {serializer.errors}")
    
    # 测试渠道类型
    supplier_data_channel = {
        'name': '测试供应商字段-渠道',
        'type': 'CHANNEL',
        'status': 'ACTIVE',
        'industry': 'SOFTWARE',
        'contact_person': '李四',
        'phone': '13800138002',
        'owner_name': '负责人2'
    }
    
    serializer2 = SupplierCreateUpdateSerializer(data=supplier_data_channel)
    if serializer2.is_valid():
        supplier2 = serializer2.save()
        print(f"✓ 渠道类型供应商创建成功: {supplier2.code} - {supplier2.name}")
        print(f"  类型: {supplier2.type} ({supplier2.get_type_display()})")
        print(f"  行业: {supplier2.industry} ({supplier2.get_industry_display()})")
    else:
        print(f"✗ 渠道类型供应商创建失败: {serializer2.errors}")
    
    # 测试2: 验证新的行业选项
    print("\n2. 测试新的行业选项")
    
    # 测试集成/服务供应商
    supplier_data_integration = {
        'name': '测试供应商字段-集成服务',
        'type': 'ORIGINAL',
        'status': 'ACTIVE',
        'industry': 'INTEGRATION',
        'contact_person': '王五',
        'phone': '13800138003',
        'owner_name': '负责人3'
    }
    
    serializer3 = SupplierCreateUpdateSerializer(data=supplier_data_integration)
    if serializer3.is_valid():
        supplier3 = serializer3.save()
        print(f"✓ 集成/服务供应商创建成功: {supplier3.code} - {supplier3.name}")
        print(f"  类型: {supplier3.type} ({supplier3.get_type_display()})")
        print(f"  行业: {supplier3.industry} ({supplier3.get_industry_display()})")
    else:
        print(f"✗ 集成/服务供应商创建失败: {serializer3.errors}")
    
    # 测试3: 验证旧选项不再有效
    print("\n3. 测试旧选项不再有效")
    
    # 测试旧的类型选项
    supplier_data_old_type = {
        'name': '测试供应商字段-旧类型',
        'type': 'C',  # 旧的企业供应商类型
        'status': 'ACTIVE',
        'industry': 'HARDWARE',
        'contact_person': '赵六',
        'phone': '13800138004',
        'owner_name': '负责人4'
    }
    
    serializer4 = SupplierCreateUpdateSerializer(data=supplier_data_old_type)
    if serializer4.is_valid():
        print(f"✗ 旧类型选项应该无效，但创建成功了")
    else:
        print(f"✓ 旧类型选项正确被拒绝: {serializer4.errors}")
    
    # 测试旧的行业选项
    supplier_data_old_industry = {
        'name': '测试供应商字段-旧行业',
        'type': 'ORIGINAL',
        'status': 'ACTIVE',
        'industry': 'IT',  # 旧的IT行业选项
        'contact_person': '孙七',
        'phone': '13800138005',
        'owner_name': '负责人5'
    }
    
    serializer5 = SupplierCreateUpdateSerializer(data=supplier_data_old_industry)
    if serializer5.is_valid():
        print(f"✗ 旧行业选项应该无效，但创建成功了")
    else:
        print(f"✓ 旧行业选项正确被拒绝: {serializer5.errors}")


def test_supplier_model_choices():
    """测试供应商模型的选项定义"""
    print("\n" + "=" * 60)
    print("测试供应商模型选项定义")
    print("=" * 60)
    
    # 验证类型选项
    print("\n1. 验证供应商类型选项")
    type_choices = dict(Supplier.SUPPLIER_TYPE_CHOICES)
    print(f"类型选项: {type_choices}")
    
    expected_types = {'ORIGINAL': '原厂', 'CHANNEL': '渠道'}
    if type_choices == expected_types:
        print("✓ 供应商类型选项正确")
    else:
        print(f"✗ 供应商类型选项不正确，期望: {expected_types}")
    
    # 验证行业选项
    print("\n2. 验证供应商行业选项")
    industry_choices = dict(Supplier.INDUSTRY_TYPE_CHOICES)
    print(f"行业选项: {industry_choices}")
    
    expected_industries = {
        'HARDWARE': '硬件产品供应商',
        'SOFTWARE': '软件产品供应商',
        'INTEGRATION': '集成/服务供应商'
    }
    if industry_choices == expected_industries:
        print("✓ 供应商行业选项正确")
    else:
        print(f"✗ 供应商行业选项不正确，期望: {expected_industries}")


if __name__ == '__main__':
    print("开始测试供应商字段选项更改...")
    
    try:
        test_supplier_model_choices()
        test_supplier_field_choices()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
