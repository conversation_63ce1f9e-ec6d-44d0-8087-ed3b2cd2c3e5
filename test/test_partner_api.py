#!/usr/bin/env python3
"""
相对方API测试脚本
测试新的Partner模型和API接口功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def test_create_customer_partner():
    """测试创建客户类型的相对方"""
    print("=== 测试创建客户类型的相对方 ===")
    
    data = {
        "name": "测试企业客户",
        "type": "C",
        "industry": "IT",
        "contact_person": "张三",
        "phone": "13800138000",
        "owner_name": "李四"
    }
    
    response = requests.post(f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    print(f"状态码: {response.status_code}")

    try:
        response_data = response.json()
        print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

        if response.status_code == 201:
            return response_data['data']['id']
    except json.JSONDecodeError:
        print(f"响应内容: {response.text}")

    return None

def test_create_supplier_partner():
    """测试创建供应商类型的相对方"""
    print("\n=== 测试创建供应商类型的相对方 ===")
    
    data = {
        "name": "测试原厂供应商",
        "type": "ORIGINAL",
        "industry": "HARDWARE",
        "contact_person": "王五",
        "phone": "13900139000",
        "owner_name": "赵六"
    }
    
    response = requests.post(f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    print(f"状态码: {response.status_code}")

    try:
        response_data = response.json()
        print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

        if response.status_code == 201:
            return response_data['data']['id']
    except json.JSONDecodeError:
        print(f"响应内容: {response.text}")

    return None

def test_list_partners():
    """测试获取相对方列表"""
    print("\n=== 测试获取相对方列表 ===")
    
    response = requests.get(f"{BASE_URL}/partners/", headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_get_partner_detail(partner_id):
    """测试获取相对方详情"""
    print(f"\n=== 测试获取相对方详情 (ID: {partner_id}) ===")
    
    response = requests.get(f"{BASE_URL}/partners/{partner_id}/", headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_add_payment_info(supplier_id):
    """测试为供应商添加收款信息"""
    print(f"\n=== 测试为供应商添加收款信息 (ID: {supplier_id}) ===")
    
    data = {
        "bank_name": "中国工商银行",
        "account_number": "1234567890123456789",
        "is_default": True,
        "remark": "主要收款账户"
    }
    
    response = requests.post(f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data, headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_get_payment_infos(supplier_id):
    """测试获取供应商收款信息列表"""
    print(f"\n=== 测试获取供应商收款信息列表 (ID: {supplier_id}) ===")
    
    response = requests.get(f"{BASE_URL}/partners/{supplier_id}/payment_infos/", headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_update_partner_status(partner_id):
    """测试更新相对方状态"""
    print(f"\n=== 测试更新相对方状态 (ID: {partner_id}) ===")
    
    data = {
        "status": "SUSPENDED"
    }
    
    response = requests.patch(f"{BASE_URL}/partners/{partner_id}/update_status/", json=data, headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_simple_list():
    """测试获取相对方简单列表"""
    print("\n=== 测试获取相对方简单列表 ===")
    
    response = requests.get(f"{BASE_URL}/partners/simple_list/", headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def test_search_partners():
    """测试搜索相对方"""
    print("\n=== 测试搜索相对方 ===")
    
    params = {
        "search": "测试",
        "type": "C,ORIGINAL"
    }
    
    response = requests.get(f"{BASE_URL}/partners/", params=params, headers=HEADERS)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

def main():
    """主测试函数"""
    print("开始测试相对方API...")
    
    try:
        # 测试创建客户
        customer_id = test_create_customer_partner()
        
        # 测试创建供应商
        supplier_id = test_create_supplier_partner()
        
        # 测试列表
        test_list_partners()
        
        # 测试详情
        if customer_id:
            test_get_partner_detail(customer_id)
        
        # 测试供应商收款信息
        if supplier_id:
            test_add_payment_info(supplier_id)
            test_get_payment_infos(supplier_id)
        
        # 测试状态更新
        if customer_id:
            test_update_partner_status(customer_id)
        
        # 测试简单列表
        test_simple_list()
        
        # 测试搜索
        test_search_partners()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
