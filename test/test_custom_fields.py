#!/usr/bin/env python3
"""
自定义字段功能测试脚本
测试表头设置功能的完整流程
"""

import requests
import json
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def safe_request(method, url, **kwargs):
    """安全的请求方法，处理JSON解析错误"""
    try:
        response = getattr(requests, method.lower())(url, **kwargs)
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response.status_code, response_data
        except json.JSONDecodeError:
            print(f"响应内容: {response.text}")
            return response.status_code, None
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None

def test_create_custom_fields():
    """测试创建自定义字段"""
    print("=== 测试创建自定义字段 ===")

    # 先获取现有字段，如果存在就使用现有的
    status_code, response_data = safe_request("GET", f"{BASE_URL}/custom-fields/?target_model=partner", headers=HEADERS)
    existing_fields = {}
    if status_code == 200 and response_data:
        for field in response_data['data']['items']:
            existing_fields[field['field_name']] = field['id']

    # 创建文本类型字段
    text_field_id = existing_fields.get("所属税务局")
    if not text_field_id:
        text_field_data = {
            "field_name": "所属税务局",
            "field_type": "text",
            "target_model": "partner"
        }

        status_code, response_data = safe_request("POST", f"{BASE_URL}/custom-fields/", json=text_field_data, headers=HEADERS)
        if status_code == 201 and response_data:
            text_field_id = response_data['data']['id']
    
    # 创建单选类型字段
    select_field_id = existing_fields.get("企业规模")
    if not select_field_id:
        select_field_data = {
            "field_name": "企业规模",
            "field_type": "select",
            "target_model": "partner",
            "field_options": json.dumps([
                {"value": "small", "label": "小型企业"},
                {"value": "medium", "label": "中型企业"},
                {"value": "large", "label": "大型企业"}
            ])
        }

        status_code, response_data = safe_request("POST", f"{BASE_URL}/custom-fields/", json=select_field_data, headers=HEADERS)
        if status_code == 201 and response_data:
            select_field_id = response_data['data']['id']

    # 创建数字类型字段
    number_field_id = existing_fields.get("员工人数")
    if not number_field_id:
        number_field_data = {
            "field_name": "员工人数",
            "field_type": "number",
            "target_model": "partner"
        }

        status_code, response_data = safe_request("POST", f"{BASE_URL}/custom-fields/", json=number_field_data, headers=HEADERS)
        if status_code == 201 and response_data:
            number_field_id = response_data['data']['id']

    # 创建日期类型字段
    date_field_id = existing_fields.get("成立日期")
    if not date_field_id:
        date_field_data = {
            "field_name": "成立日期",
            "field_type": "date",
            "target_model": "partner"
        }

        status_code, response_data = safe_request("POST", f"{BASE_URL}/custom-fields/", json=date_field_data, headers=HEADERS)
        if status_code == 201 and response_data:
            date_field_id = response_data['data']['id']
    
    return text_field_id, select_field_id, number_field_id, date_field_id

def test_list_custom_fields():
    """测试获取自定义字段列表"""
    print("\n=== 测试获取自定义字段列表 ===")
    
    safe_request("GET", f"{BASE_URL}/custom-fields/?target_model=partner", headers=HEADERS)

def test_create_partner_with_custom_fields(text_field_id, select_field_id, number_field_id, date_field_id):
    """测试创建包含自定义字段的相对方"""
    print("\n=== 测试创建包含自定义字段的相对方 ===")
    
    timestamp = int(time.time())
    
    data = {
        "name": f"测试自定义字段公司{timestamp}",
        "type": "C",
        "industry": "IT",
        "contact_person": "张三",
        "phone": "13800138000",
        "owner_name": "李四",
        "custom_fields": {}
    }
    
    # 添加自定义字段值
    if text_field_id:
        data["custom_fields"][text_field_id] = "深圳市南山区税务局"
    if select_field_id:
        data["custom_fields"][select_field_id] = "medium"
    if number_field_id:
        data["custom_fields"][number_field_id] = 150
    if date_field_id:
        data["custom_fields"][date_field_id] = "2020-01-15"
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def test_get_partner_with_custom_fields(partner_id):
    """测试获取包含自定义字段的相对方详情"""
    print(f"\n=== 测试获取包含自定义字段的相对方详情 (ID: {partner_id}) ===")
    
    safe_request("GET", f"{BASE_URL}/partners/{partner_id}/", headers=HEADERS)

def test_update_partner_custom_fields(partner_id, text_field_id, select_field_id):
    """测试更新相对方的自定义字段"""
    print(f"\n=== 测试更新相对方的自定义字段 (ID: {partner_id}) ===")
    
    data = {
        "custom_fields": {}
    }
    
    # 更新自定义字段值
    if text_field_id:
        data["custom_fields"][text_field_id] = "深圳市福田区税务局"
    if select_field_id:
        data["custom_fields"][select_field_id] = "large"
    
    safe_request("PATCH", f"{BASE_URL}/partners/{partner_id}/", json=data, headers=HEADERS)

def test_update_custom_field_name(field_id):
    """测试更新自定义字段名称"""
    print(f"\n=== 测试更新自定义字段名称 (ID: {field_id}) ===")
    
    data = {
        "field_name": "所属税务机关"
    }
    
    safe_request("PATCH", f"{BASE_URL}/custom-fields/{field_id}/", json=data, headers=HEADERS)

def test_set_custom_field_active_status(field_id, is_active=False):
    """测试设置自定义字段启用状态"""
    action_text = "禁用" if not is_active else "启用"
    print(f"\n=== 测试{action_text}自定义字段 (ID: {field_id}) ===")

    data = {"is_active": is_active}
    safe_request("PATCH", f"{BASE_URL}/custom-fields/{field_id}/set_active_status/",
                 headers=HEADERS, json=data)

def test_validation_errors():
    """测试各种验证错误"""
    print("\n=== 测试验证错误 ===")
    
    # 测试重复字段名
    print("\n--- 测试重复字段名 ---")
    data = {
        "field_name": "所属税务局",  # 已存在的字段名
        "field_type": "text",
        "target_model": "partner"
    }
    safe_request("POST", f"{BASE_URL}/custom-fields/", json=data, headers=HEADERS)
    
    # 测试单选字段没有选项
    print("\n--- 测试单选字段没有选项 ---")
    data = {
        "field_name": "测试单选",
        "field_type": "select",
        "target_model": "partner"
    }
    safe_request("POST", f"{BASE_URL}/custom-fields/", json=data, headers=HEADERS)
    
    # 测试无效的选项格式
    print("\n--- 测试无效的选项格式 ---")
    data = {
        "field_name": "测试单选2",
        "field_type": "select",
        "target_model": "partner",
        "field_options": "invalid json"
    }
    safe_request("POST", f"{BASE_URL}/custom-fields/", json=data, headers=HEADERS)

def test_simple_list():
    """测试获取自定义字段简单列表"""
    print("\n=== 测试获取自定义字段简单列表 ===")
    
    safe_request("GET", f"{BASE_URL}/custom-fields/simple_list/?target_model=partner", headers=HEADERS)

def main():
    """主测试函数"""
    print("开始测试自定义字段功能...")
    
    try:
        # 测试创建自定义字段
        text_field_id, select_field_id, number_field_id, date_field_id = test_create_custom_fields()
        
        # 测试获取字段列表
        test_list_custom_fields()
        
        # 测试创建包含自定义字段的相对方
        partner_id = test_create_partner_with_custom_fields(text_field_id, select_field_id, number_field_id, date_field_id)
        
        if partner_id:
            # 测试获取包含自定义字段的相对方详情
            test_get_partner_with_custom_fields(partner_id)
            
            # 测试更新自定义字段值
            test_update_partner_custom_fields(partner_id, text_field_id, select_field_id)
            
            # 再次获取详情查看更新结果
            test_get_partner_with_custom_fields(partner_id)
        
        # 测试更新字段名称
        if text_field_id:
            test_update_custom_field_name(text_field_id)
        
        # 测试设置字段启用状态
        if number_field_id:
            test_set_custom_field_active_status(number_field_id, False)  # 禁用
            test_set_custom_field_active_status(number_field_id, True)   # 启用
        
        # 测试简单列表
        test_simple_list()
        
        # 测试验证错误
        test_validation_errors()
        
        print("\n=== 所有自定义字段功能测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
