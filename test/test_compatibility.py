#!/usr/bin/env python3
"""
兼容性测试脚本
测试Customer和Supplier API的向后兼容性
"""

import requests
import json
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def safe_request(method, url, **kwargs):
    """安全的请求方法，处理JSON解析错误"""
    try:
        response = getattr(requests, method.lower())(url, **kwargs)
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response.status_code, response_data
        except json.JSONDecodeError:
            print(f"响应内容: {response.text}")
            return response.status_code, None
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None

def test_customer_list():
    """测试客户列表API"""
    print("=== 测试客户列表API ===")
    safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)

def test_customer_create():
    """测试创建客户API"""
    print("\n=== 测试创建客户API ===")
    
    timestamp = int(time.time())
    data = {
        "name": f"兼容性测试客户{timestamp}",
        "type": "C",
        "contact_person": "测试联系人",
        "phone": "13800138999",
        "owner_name": "测试负责人"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/customers/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def test_customer_detail(customer_id):
    """测试客户详情API"""
    print(f"\n=== 测试客户详情API (ID: {customer_id}) ===")
    safe_request("GET", f"{BASE_URL}/customers/{customer_id}/", headers=HEADERS)

def test_customer_update(customer_id):
    """测试更新客户API"""
    print(f"\n=== 测试更新客户API (ID: {customer_id}) ===")
    
    data = {
        "name": "兼容性测试客户-已更新",
        "type": "G",  # 改为政府客户
        "contact_person": "更新后的联系人",
        "phone": "13800138888",
        "owner_name": "更新后的负责人"
    }
    
    safe_request("PATCH", f"{BASE_URL}/customers/{customer_id}/", json=data, headers=HEADERS)

def test_supplier_list():
    """测试供应商列表API"""
    print("\n=== 测试供应商列表API ===")
    safe_request("GET", f"{BASE_URL}/suppliers/", headers=HEADERS)

def test_partner_list():
    """测试相对方列表API"""
    print("\n=== 测试相对方列表API ===")
    safe_request("GET", f"{BASE_URL}/partners/", headers=HEADERS)

def test_partner_create():
    """测试创建相对方API"""
    print("\n=== 测试创建相对方API ===")
    
    timestamp = int(time.time())
    data = {
        "name": f"新相对方API测试{timestamp}",
        "type": "C",
        "contact_person": "新API联系人",
        "phone": "13800139000",
        "owner_name": "新API负责人"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 测试数据一致性 ===")
    
    # 获取客户列表
    print("--- 客户API返回的数据 ---")
    status_code, customer_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    customer_count = customer_data['data']['total'] if customer_data else 0
    
    # 获取相对方列表（客户类型）
    print("--- 相对方API返回的客户类型数据 ---")
    status_code, partner_data = safe_request("GET", f"{BASE_URL}/partners/?type=C,G", headers=HEADERS)
    partner_customer_count = partner_data['data']['total'] if partner_data else 0
    
    print(f"\n数据一致性检查:")
    print(f"客户API返回数量: {customer_count}")
    print(f"相对方API客户类型数量: {partner_customer_count}")
    print(f"数据一致性: {'✓ 一致' if customer_count == partner_customer_count else '✗ 不一致'}")

def main():
    """主测试函数"""
    print("开始兼容性测试...")
    
    try:
        # 测试客户API
        test_customer_list()
        
        # 测试创建客户
        customer_id = test_customer_create()
        
        if customer_id:
            # 测试客户详情
            test_customer_detail(customer_id)
            
            # 测试更新客户
            test_customer_update(customer_id)
            
            # 再次查看详情确认更新
            test_customer_detail(customer_id)
        
        # 测试供应商API
        test_supplier_list()
        
        # 测试相对方API
        test_partner_list()
        
        # 测试创建相对方
        partner_id = test_partner_create()
        
        # 测试数据一致性
        test_data_consistency()
        
        print("\n=== 兼容性测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
