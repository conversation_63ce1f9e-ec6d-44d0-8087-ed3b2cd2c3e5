#!/usr/bin/env python
"""
测试 PostgreSQL、Redis 和 MinIO 连接
"""
import os
import sys
import django
from pathlib import Path

# 添加项目路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.db import connection
from django.core.cache import cache
import redis
import requests


def test_postgresql():
    """测试 PostgreSQL 连接"""
    print("🔍 测试 PostgreSQL 连接...")
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ PostgreSQL 连接成功!")
            print(f"   版本: {version}")
            
            # 测试数据库信息
            cursor.execute("SELECT current_database(), current_user;")
            db_info = cursor.fetchone()
            print(f"   数据库: {db_info[0]}")
            print(f"   用户: {db_info[1]}")
            
            return True
    except Exception as e:
        print(f"❌ PostgreSQL 连接失败: {e}")
        return False


def test_redis():
    """测试 Redis 连接"""
    print("\n🔍 测试 Redis 连接...")
    try:
        # 测试 Django Cache
        cache.set('test_key', 'test_value', 30)
        value = cache.get('test_key')
        if value == 'test_value':
            print("✅ Django Redis Cache 连接成功!")
        else:
            print("❌ Django Redis Cache 测试失败")
            return False
            
        # 测试直接 Redis 连接
        from conf.env import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
        r = redis.Redis(
            host=REDIS_HOST,
            port=int(REDIS_PORT),
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        
        # 测试连接
        pong = r.ping()
        if pong:
            print("✅ 直接 Redis 连接成功!")
            
            # 获取 Redis 信息
            info = r.info()
            print(f"   Redis 版本: {info.get('redis_version', 'Unknown')}")
            print(f"   内存使用: {info.get('used_memory_human', 'Unknown')}")
            print(f"   连接数: {info.get('connected_clients', 'Unknown')}")
            
            return True
        else:
            print("❌ Redis ping 失败")
            return False
            
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False


def test_minio():
    """测试 MinIO 连接"""
    print("\n🔍 测试 MinIO 连接...")
    try:
        from conf.env import MINIO_HOST, MINIO_API_PORT, MINIO_STORAGE_ACCESS_KEY, MINIO_STORAGE_SECRET_KEY
        
        # 测试 MinIO API 健康检查
        minio_url = f"http://{MINIO_HOST}:{MINIO_API_PORT}/minio/health/live"
        response = requests.get(minio_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ MinIO API 连接成功!")
            print(f"   API 端点: http://{MINIO_HOST}:{MINIO_API_PORT}")
            print(f"   控制台: http://{MINIO_HOST}:9001")
            print(f"   访问密钥: {MINIO_STORAGE_ACCESS_KEY}")
            
            # 测试 MinIO Python 客户端（如果安装了）
            try:
                from minio import Minio
                client = Minio(
                    f"{MINIO_HOST}:{MINIO_API_PORT}",
                    access_key=MINIO_STORAGE_ACCESS_KEY,
                    secret_key=MINIO_STORAGE_SECRET_KEY,
                    secure=False
                )
                
                # 测试列出存储桶
                buckets = list(client.list_buckets())
                print(f"   存储桶数量: {len(buckets)}")
                for bucket in buckets:
                    print(f"   - {bucket.name} (创建时间: {bucket.creation_date})")
                    
            except ImportError:
                print("   ⚠️  minio Python 客户端未安装，跳过详细测试")
            except Exception as e:
                print(f"   ⚠️  MinIO 客户端测试失败: {e}")
                
            return True
        else:
            print(f"❌ MinIO API 响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ MinIO 连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ MinIO 测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试数据库连接...")
    print("=" * 50)
    
    # 显示配置信息
    from conf.env import (
        DATABASE_HOST, DATABASE_PORT, DATABASE_NAME, DATABASE_USER,
        REDIS_HOST, REDIS_PORT, MINIO_HOST, MINIO_API_PORT
    )
    
    print(f"📊 配置信息:")
    print(f"   PostgreSQL: {DATABASE_USER}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_NAME}")
    print(f"   Redis: {REDIS_HOST}:{REDIS_PORT}")
    print(f"   MinIO: {MINIO_HOST}:{MINIO_API_PORT}")
    print("=" * 50)
    
    # 测试连接
    pg_success = test_postgresql()
    redis_success = test_redis()
    minio_success = test_minio()
    
    print("\n" + "=" * 50)
    if pg_success and redis_success and minio_success:
        print("🎉 所有连接测试通过!")
        print("💡 提示: 如果是首次连接，请运行数据库迁移:")
        print("   python manage.py makemigrations")
        print("   python manage.py migrate")
        print("💡 MinIO 提示: 请确保创建了 'erp-finance' 存储桶")
    else:
        print("❌ 部分连接测试失败，请检查配置和服务状态")
        failed_services = []
        if not pg_success:
            failed_services.append("PostgreSQL")
        if not redis_success:
            failed_services.append("Redis")
        if not minio_success:
            failed_services.append("MinIO")
        print(f"   失败的服务: {', '.join(failed_services)}")
        
    print("=" * 50)
