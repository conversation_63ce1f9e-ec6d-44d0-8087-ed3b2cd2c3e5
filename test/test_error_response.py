#!/usr/bin/env python
"""
错误响应格式测试脚本
用于验证API错误响应是否按照统一格式返回
"""

import os
import sys
import django
import json
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.test import TestCase, Client
from django.urls import reverse
from rest_framework.test import APIClient
from erp.models import Customer, Project


def test_validation_error_format():
    """测试验证错误的响应格式"""
    print("=" * 60)
    print("验证错误响应格式测试")
    print("=" * 60)
    
    client = APIClient()
    
    # 测试1: 创建项目时缺少必填字段
    print("测试1: 创建项目时缺少必填字段")
    print("-" * 40)
    
    invalid_data = {
        "name": "",  # 空名称
        "customer_id": "invalid-uuid",  # 无效UUID
        "budget": "invalid-number",  # 无效数字
        "expected_profit_rate": "invalid-rate",  # 无效毛利率
        "start_date": "2024-01-01",
        "end_date": "2023-12-31"  # 结束日期早于开始日期
    }
    
    response = client.post('/api/v1/projects/', invalid_data, format='json')
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.data, ensure_ascii=False, indent=2)}")
    
    # 验证响应格式
    if response.status_code == 400:
        data = response.data
        if 'code' in data and 'msg' in data:
            print("✓ 响应格式正确，包含code和msg字段")
            
            # 检查msg是否为字典格式（验证错误详情）
            if isinstance(data['msg'], dict):
                print("✓ msg字段为字典格式，包含详细验证错误")
                for field, errors in data['msg'].items():
                    print(f"  {field}: {errors}")
            else:
                print("✗ msg字段不是字典格式")
                return False
        else:
            print("✗ 响应格式错误，缺少code或msg字段")
            return False
    else:
        print(f"✗ 期望状态码400，实际状态码{response.status_code}")
        return False
    
    print()
    
    # 测试2: 创建客户时重复名称
    print("测试2: 创建客户时重复名称")
    print("-" * 40)
    
    # 先清理数据（先删除项目，再删除客户）
    Project.objects.all().delete()
    Customer.objects.all().delete()
    customer_data = {
        "name": "测试客户",
        "type": "C",
        "contact_person": "张三",
        "phone": "13800138001"
    }
    
    # 第一次创建应该成功
    response1 = client.post('/api/v1/customers/', customer_data, format='json')
    print(f"第一次创建状态码: {response1.status_code}")
    
    # 第二次创建相同名称应该失败
    response2 = client.post('/api/v1/customers/', customer_data, format='json')
    print(f"第二次创建状态码: {response2.status_code}")
    print(f"响应内容: {json.dumps(response2.data, ensure_ascii=False, indent=2)}")
    
    # 验证响应格式
    if response2.status_code == 400:
        data = response2.data
        if 'code' in data and 'msg' in data:
            print("✓ 响应格式正确，包含code和msg字段")
            
            # 检查msg是否为字典格式
            if isinstance(data['msg'], dict):
                print("✓ msg字段为字典格式，包含详细验证错误")
                for field, errors in data['msg'].items():
                    print(f"  {field}: {errors}")
            else:
                print("✗ msg字段不是字典格式")
                return False
        else:
            print("✗ 响应格式错误，缺少code或msg字段")
            return False
    else:
        print(f"✗ 期望状态码400，实际状态码{response2.status_code}")
        return False
    
    print()
    
    # 测试3: 访问不存在的资源
    print("测试3: 访问不存在的资源")
    print("-" * 40)
    
    response3 = client.get('/api/v1/projects/00000000-0000-0000-0000-000000000000/')
    print(f"状态码: {response3.status_code}")
    print(f"响应内容: {json.dumps(response3.data, ensure_ascii=False, indent=2)}")
    
    # 验证响应格式
    if response3.status_code == 404:
        data = response3.data
        if 'code' in data and 'msg' in data:
            print("✓ 响应格式正确，包含code和msg字段")
            print(f"  code: {data['code']}")
            print(f"  msg: {data['msg']}")
        else:
            print("✗ 响应格式错误，缺少code或msg字段")
            return False
    else:
        print(f"✗ 期望状态码404，实际状态码{response3.status_code}")
        return False
    
    print()
    print("=" * 60)
    print("✓ 所有错误响应格式测试通过")
    print("=" * 60)
    
    return True


def test_success_response_format():
    """测试成功响应的格式"""
    print("=" * 60)
    print("成功响应格式测试")
    print("=" * 60)
    
    client = APIClient()
    
    # 清理数据（先删除项目，再删除客户）
    Project.objects.all().delete()
    Customer.objects.all().delete()
    
    # 创建客户
    customer_data = {
        "name": "成功测试客户",
        "type": "C",
        "contact_person": "李四",
        "phone": "13800138002"
    }
    
    response = client.post('/api/v1/customers/', customer_data, format='json')
    print(f"创建客户状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.data, ensure_ascii=False, indent=2)}")
    
    if response.status_code == 201:
        data = response.data
        if 'code' in data and 'msg' in data and 'data' in data:
            print("✓ 成功响应格式正确，包含code、msg和data字段")
            print(f"  code: {data['code']}")
            print(f"  msg: {data['msg']}")
            print(f"  data包含客户信息: {'id' in data['data']}")
        else:
            print("✗ 成功响应格式错误")
            return False
    else:
        print(f"✗ 期望状态码201，实际状态码{response.status_code}")
        return False
    
    print()
    print("=" * 60)
    print("✓ 成功响应格式测试通过")
    print("=" * 60)
    
    return True


if __name__ == "__main__":
    try:
        # 测试错误响应格式
        error_test_passed = test_validation_error_format()
        
        # 测试成功响应格式
        success_test_passed = test_success_response_format()
        
        # 总结
        if error_test_passed and success_test_passed:
            print("\n🎉 所有响应格式测试通过！")
            sys.exit(0)
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
