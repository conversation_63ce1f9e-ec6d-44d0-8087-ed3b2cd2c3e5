#!/usr/bin/env python3
"""
JWT集成测试脚本
测试JWT中间件和用户信息自动设置功能
"""

import os
import sys
import django
import json
import base64
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.test import RequestFactory
from erp.utils.jwt_helper import JWTHelper, JWTMiddleware
from erp.models import Customer


def create_mock_jwt_token():
    """创建模拟的JWT token"""
    payload = {
        'user_code': 'test_user_001',
        'user_name': '测试用户',
        'user_role': ['erp.admin'],
        'enterprise_code': 'ENT001',
        'enterprise_name': '测试企业',
        'enterprise_level': 2,
        'department_code': 'DEPT001',
        'department_name': '测试部门',
        'exp': int(datetime.now().timestamp()) + 3600  # 1小时后过期
    }
    
    # 简单的base64编码（实际JWT会有签名）
    header = base64.urlsafe_b64encode(json.dumps({'typ': 'JWT', 'alg': 'HS256'}).encode()).decode().rstrip('=')
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    signature = base64.urlsafe_b64encode(b'mock_signature').decode().rstrip('=')
    
    return f"{header}.{payload_encoded}.{signature}"


def test_jwt_middleware():
    """测试JWT中间件"""
    print("=== 测试JWT中间件 ===")
    
    # 创建请求工厂
    factory = RequestFactory()
    
    # 创建模拟JWT token
    jwt_token = create_mock_jwt_token()
    print(f"模拟JWT Token: {jwt_token[:50]}...")
    
    # 创建带有JWT的请求
    request = factory.get('/api/customers/', HTTP_AUTHORIZATION=jwt_token)
    
    # 创建中间件实例
    def mock_get_response(request):
        return None
    
    middleware = JWTMiddleware(mock_get_response)
    
    # 处理请求
    try:
        middleware(request)
        
        # 检查用户信息是否正确设置
        user_id = JWTHelper.get_current_user_id()
        user_name = JWTHelper.get_current_user_name()
        enterprise_id = JWTHelper.get_current_enterprise_id()
        
        print(f"当前用户ID: {user_id}")
        print(f"当前用户名: {user_name}")
        print(f"当前企业ID: {enterprise_id}")
        
        if user_id == 'test_user_001' and user_name == '测试用户':
            print("✅ JWT中间件测试通过")
            return True
        else:
            print("❌ JWT中间件测试失败")
            return False
            
    except Exception as e:
        print(f"❌ JWT中间件测试出错: {str(e)}")
        return False


def test_model_auto_user_setting():
    """测试模型自动设置用户信息"""
    print("\n=== 测试模型自动设置用户信息 ===")
    
    try:
        # 先设置当前用户信息
        JWTHelper.set_current_user(
            user_code='test_user_002',
            user_name='测试用户2',
            enterprise_code='ENT002'
        )
        
        # 创建客户记录（使用时间戳确保唯一性）
        import time
        timestamp = int(time.time())
        customer = Customer(
            name=f'测试客户公司_{timestamp}',
            type='C',
            industry='IT',
            contact_person='张三',
            phone='13800138000'
        )
        
        # 保存客户（应该自动设置creator和updater）
        customer.save()
        
        print(f"客户ID: {customer.id}")
        print(f"创建者: {customer.creator}")
        print(f"更新者: {customer.updater}")
        print(f"创建时间: {customer.create_datetime}")
        
        if customer.creator == 'test_user_002' and customer.updater == 'test_user_002':
            print("✅ 模型自动设置用户信息测试通过")
            
            # 清理测试数据
            customer.hard_delete()
            return True
        else:
            print("❌ 模型自动设置用户信息测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 模型自动设置用户信息测试出错: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("开始JWT集成测试...\n")
    
    test_results = []
    
    # 测试JWT中间件
    test_results.append(test_jwt_middleware())
    
    # 测试模型自动设置用户信息
    test_results.append(test_model_auto_user_setting())
    
    # 输出测试结果
    print(f"\n=== 测试结果 ===")
    passed = sum(test_results)
    total = len(test_results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
