#!/usr/bin/env python
"""
测试供应商API与新字段选项
"""

import os
import sys
import django
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from erp.models import Supplier


def test_supplier_api_with_new_fields():
    """测试供应商API与新字段选项"""
    print("=" * 60)
    print("测试供应商API与新字段选项")
    print("=" * 60)
    
    # 清理测试数据
    Supplier.objects.filter(name__contains='API测试供应商').delete()
    
    client = Client()
    
    # 测试1: 创建供应商API
    print("\n1. 测试创建供应商API")
    
    supplier_data = {
        'name': 'API测试供应商-原厂',
        'type': 'ORIGINAL',
        'status': 'ACTIVE',
        'industry': 'HARDWARE',
        'contact_person': '张三',
        'phone': '13800138001',
        'owner_name': '负责人1'
    }
    
    # 注意：这里没有JWT认证，所以可能会失败，但我们主要测试字段验证
    try:
        response = client.post('/api/suppliers/', 
                             data=json.dumps(supplier_data),
                             content_type='application/json')
        
        if response.status_code == 201:
            print("✓ 供应商创建API成功")
            response_data = response.json()
            print(f"  创建的供应商: {response_data.get('data', {}).get('name')}")
        elif response.status_code == 401:
            print("ℹ 供应商创建API需要认证（预期行为）")
        else:
            print(f"✗ 供应商创建API失败: {response.status_code}")
            print(f"  响应: {response.content.decode()}")
    except Exception as e:
        print(f"✗ 供应商创建API测试异常: {e}")
    
    # 测试2: 直接创建供应商用于过滤测试
    print("\n2. 创建测试数据用于过滤测试")
    
    # 创建不同类型和行业的供应商
    suppliers = [
        {
            'name': 'API测试供应商-原厂硬件',
            'type': 'ORIGINAL',
            'industry': 'HARDWARE',
            'status': 'ACTIVE',
            'contact_person': '张三',
            'phone': '13800138001',
            'owner_name': '负责人1'
        },
        {
            'name': 'API测试供应商-渠道软件',
            'type': 'CHANNEL',
            'industry': 'SOFTWARE',
            'status': 'ACTIVE',
            'contact_person': '李四',
            'phone': '13800138002',
            'owner_name': '负责人2'
        },
        {
            'name': 'API测试供应商-原厂集成',
            'type': 'ORIGINAL',
            'industry': 'INTEGRATION',
            'status': 'ACTIVE',
            'contact_person': '王五',
            'phone': '13800138003',
            'owner_name': '负责人3'
        }
    ]
    
    created_suppliers = []
    for supplier_data in suppliers:
        supplier = Supplier.objects.create(**supplier_data)
        created_suppliers.append(supplier)
        print(f"✓ 创建供应商: {supplier.code} - {supplier.name}")
    
    # 测试3: 过滤器测试
    print("\n3. 测试过滤器功能")
    
    # 测试按类型过滤
    try:
        response = client.get('/api/suppliers/?type=ORIGINAL')
        if response.status_code == 200:
            print("✓ 按类型过滤API成功")
        elif response.status_code == 401:
            print("ℹ 过滤API需要认证（预期行为）")
        else:
            print(f"✗ 按类型过滤API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 按类型过滤API测试异常: {e}")
    
    # 测试按行业过滤
    try:
        response = client.get('/api/suppliers/?industry=HARDWARE,SOFTWARE')
        if response.status_code == 200:
            print("✓ 按行业过滤API成功")
        elif response.status_code == 401:
            print("ℹ 按行业过滤API需要认证（预期行为）")
        else:
            print(f"✗ 按行业过滤API失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 按行业过滤API测试异常: {e}")
    
    # 测试4: 验证数据库中的数据
    print("\n4. 验证数据库中的供应商数据")
    
    # 按类型统计
    original_count = Supplier.objects.filter(type='ORIGINAL', name__contains='API测试供应商').count()
    channel_count = Supplier.objects.filter(type='CHANNEL', name__contains='API测试供应商').count()
    print(f"原厂供应商数量: {original_count}")
    print(f"渠道供应商数量: {channel_count}")
    
    # 按行业统计
    hardware_count = Supplier.objects.filter(industry='HARDWARE', name__contains='API测试供应商').count()
    software_count = Supplier.objects.filter(industry='SOFTWARE', name__contains='API测试供应商').count()
    integration_count = Supplier.objects.filter(industry='INTEGRATION', name__contains='API测试供应商').count()
    print(f"硬件产品供应商数量: {hardware_count}")
    print(f"软件产品供应商数量: {software_count}")
    print(f"集成/服务供应商数量: {integration_count}")
    
    # 验证显示名称
    print("\n5. 验证字段显示名称")
    for supplier in created_suppliers:
        print(f"供应商: {supplier.name}")
        print(f"  类型: {supplier.type} -> {supplier.get_type_display()}")
        print(f"  行业: {supplier.industry} -> {supplier.get_industry_display()}")


if __name__ == '__main__':
    print("开始测试供应商API与新字段选项...")
    
    try:
        test_supplier_api_with_new_fields()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
