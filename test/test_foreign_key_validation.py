#!/usr/bin/env python3
"""
外键关系验证脚本
验证项目和合同模型的外键关系是否正确更新到Partner模型
"""

import requests
import json
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def safe_request(method, url, **kwargs):
    """安全的请求方法"""
    try:
        response = getattr(requests, method.lower())(url, **kwargs)
        if response.status_code < 400:
            return response.status_code, response.json()
        else:
            print(f"错误状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误响应: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print(f"错误内容: {response.text}")
            return response.status_code, None
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None

def test_create_project_with_partner():
    """测试使用Partner创建项目"""
    print("=== 测试使用Partner创建项目 ===")
    
    # 获取一个客户类型的相对方
    status_code, response_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    
    if status_code == 200 and response_data and response_data['data']['items']:
        customer = response_data['data']['items'][0]
        customer_id = customer['id']
        print(f"使用客户: {customer['name']} (ID: {customer_id})")
        
        timestamp = int(time.time())
        project_data = {
            "name": f"外键测试项目{timestamp}",
            "customer": customer_id,  # 使用Partner ID
            "type": ["WEB"],
            "status": "preparing",
            "end_user_name": "测试最终用户",
            "sales_manager_id": "admin",
            "sales_manager_name": "管理员",
            "start_date": "2025-07-30",
            "end_date": "2025-08-30"
        }
        
        status_code, response_data = safe_request("POST", f"{BASE_URL}/projects/", json=project_data, headers=HEADERS)
        
        if status_code == 201 and response_data:
            project_id = response_data['data']['id']
            print(f"✅ 项目创建成功: {response_data['data']['code']}")
            return project_id, customer_id
        else:
            print("❌ 项目创建失败")
    
    return None, None

def test_create_contract_with_partner(project_id, customer_id):
    """测试使用Partner创建合同"""
    print("\n=== 测试使用Partner创建合同 ===")
    
    if not project_id or not customer_id:
        print("❌ 缺少项目ID或客户ID")
        return None
    
    contract_data = {
        "name": f"外键测试合同{int(time.time())}",
        "category": "sales",
        "project_id": project_id,
        "customer_id": customer_id,  # 使用Partner ID
        "amount": 100000.00,
        "sign_date": "2025-07-30"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/contracts/", json=contract_data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        contract_id = response_data['data']['id']
        print(f"✅ 合同创建成功: {response_data['data']['code']}")
        return contract_id
    else:
        print("❌ 合同创建失败")
    
    return None

def test_invalid_partner_types():
    """测试无效的相对方类型"""
    print("\n=== 测试无效的相对方类型 ===")
    
    # 获取一个供应商类型的相对方
    status_code, response_data = safe_request("GET", f"{BASE_URL}/suppliers/", headers=HEADERS)
    
    if status_code == 200 and response_data and response_data['data']['items']:
        supplier = response_data['data']['items'][0]
        supplier_id = supplier['id']
        print(f"尝试使用供应商创建项目: {supplier['name']} (ID: {supplier_id})")
        
        project_data = {
            "name": f"无效测试项目{int(time.time())}",
            "customer": supplier_id,  # 使用供应商ID作为客户
            "type": ["WEB"],
            "status": "preparing",
            "end_user_name": "测试最终用户",
            "sales_manager_id": "admin",
            "sales_manager_name": "管理员",
            "start_date": "2025-07-30",
            "end_date": "2025-08-30"
        }
        
        status_code, response_data = safe_request("POST", f"{BASE_URL}/projects/", json=project_data, headers=HEADERS)
        
        if status_code != 201:
            print("✅ 正确拒绝了使用供应商作为项目客户")
        else:
            print("❌ 错误地允许了使用供应商作为项目客户")

def test_partner_statistics():
    """测试相对方统计功能"""
    print("\n=== 测试相对方统计功能 ===")
    
    # 获取有项目的客户
    status_code, response_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    
    if status_code == 200 and response_data:
        for customer in response_data['data']['items']:
            project_count = customer.get('project_count', 0)
            sales_contract_count = customer.get('sales_contract_count', 0)
            sales_contract_total_amount = customer.get('sales_contract_total_amount', 0)
            
            if project_count > 0 or sales_contract_count > 0:
                print(f"客户: {customer['name']}")
                print(f"  项目数: {project_count}")
                print(f"  销售合同数: {sales_contract_count}")
                print(f"  销售合同总金额: {sales_contract_total_amount}")
                print("✅ 统计功能正常")
                return
        
        print("ℹ️ 没有找到有项目或合同的客户")

def main():
    """主测试函数"""
    print("开始外键关系验证测试...")
    
    try:
        # 测试创建项目
        project_id, customer_id = test_create_project_with_partner()
        
        # 测试创建合同
        if project_id and customer_id:
            contract_id = test_create_contract_with_partner(project_id, customer_id)
        
        # 测试无效的相对方类型
        test_invalid_partner_types()
        
        # 测试统计功能
        test_partner_statistics()
        
        print("\n=== 外键关系验证测试完成 ===")
        print("✅ 项目和合同模型已成功更新为使用Partner模型")
        print("✅ 外键约束正确限制了相对方类型")
        print("✅ API创建功能正常工作")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
