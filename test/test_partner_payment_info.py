#!/usr/bin/env python3
"""
相对方收款信息管理测试脚本
测试供应商类型相对方的收款信息管理功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def safe_request(method, url, **kwargs):
    """安全的请求方法，处理JSON解析错误"""
    try:
        response = getattr(requests, method.lower())(url, **kwargs)
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response.status_code, response_data
        except json.JSONDecodeError:
            print(f"响应内容: {response.text}")
            return response.status_code, None
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None

def create_supplier_partner():
    """创建一个供应商类型的相对方用于测试"""
    print("=== 创建测试供应商 ===")
    
    import time
    timestamp = int(time.time())

    data = {
        "name": f"测试收款信息供应商{timestamp}",
        "type": "CHANNEL",
        "industry": "SOFTWARE",
        "contact_person": "测试联系人",
        "phone": "13800138001",
        "owner_name": "测试负责人"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def create_customer_partner():
    """创建一个客户类型的相对方用于测试"""
    print("\n=== 创建测试客户 ===")
    
    import time
    timestamp = int(time.time())

    data = {
        "name": f"测试收款信息客户{timestamp}",
        "type": "C",
        "industry": "IT",
        "contact_person": "测试客户联系人",
        "phone": "***********",
        "owner_name": "测试客户负责人"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def test_add_payment_info_to_supplier(supplier_id):
    """测试为供应商添加收款信息"""
    print(f"\n=== 测试为供应商添加收款信息 (ID: {supplier_id}) ===")
    
    data = {
        "bank_name": "中国工商银行",
        "account_number": "*********0*********",
        "is_default": True,
        "remark": "主要收款账户"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data, headers=HEADERS)
    
    if status_code == 201 and response_data:
        return response_data['data']['id']
    return None

def test_add_payment_info_to_customer(customer_id):
    """测试为客户添加收款信息（应该失败）"""
    print(f"\n=== 测试为客户添加收款信息（应该失败） (ID: {customer_id}) ===")
    
    data = {
        "bank_name": "中国建设银行",
        "account_number": "9876543210987654321",
        "is_default": True,
        "remark": "测试账户"
    }
    
    safe_request("POST", f"{BASE_URL}/partners/{customer_id}/add_payment_info/", json=data, headers=HEADERS)

def test_get_payment_infos(partner_id):
    """测试获取收款信息列表"""
    print(f"\n=== 测试获取收款信息列表 (ID: {partner_id}) ===")
    
    safe_request("GET", f"{BASE_URL}/partners/{partner_id}/payment_infos/", headers=HEADERS)

def test_add_multiple_payment_infos(supplier_id):
    """测试添加多个收款信息"""
    print(f"\n=== 测试添加多个收款信息 (ID: {supplier_id}) ===")
    
    # 添加第二个收款账户
    data2 = {
        "bank_name": "中国农业银行",
        "account_number": "5555666677778888999",
        "is_default": False,
        "remark": "备用收款账户"
    }
    
    status_code, response_data = safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data2, headers=HEADERS)
    
    if status_code == 201 and response_data:
        payment_info_id = response_data['data']['id']
        
        # 添加第三个收款账户并设为默认（应该会将之前的默认账户改为非默认）
        data3 = {
            "bank_name": "中国银行",
            "account_number": "1111222233334444555",
            "is_default": True,
            "remark": "新的默认收款账户"
        }
        
        safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data3, headers=HEADERS)
        
        return payment_info_id
    return None

def test_update_payment_info(supplier_id, payment_info_id):
    """测试更新收款信息"""
    print(f"\n=== 测试更新收款信息 (供应商ID: {supplier_id}, 收款信息ID: {payment_info_id}) ===")

    data = {
        "payment_info_id": payment_info_id,
        "bank_name": "中国农业银行深圳分行",
        "account_number": "5555666677778888999",
        "is_default": False,
        "remark": "更新后的备用收款账户"
    }

    safe_request("PATCH", f"{BASE_URL}/partners/{supplier_id}/update_payment_info/", json=data, headers=HEADERS)

def test_delete_payment_info(supplier_id, payment_info_id):
    """测试删除收款信息"""
    print(f"\n=== 测试删除收款信息 (供应商ID: {supplier_id}, 收款信息ID: {payment_info_id}) ===")

    data = {
        "payment_info_id": payment_info_id
    }

    safe_request("DELETE", f"{BASE_URL}/partners/{supplier_id}/delete_payment_info/", json=data, headers=HEADERS)

def test_validation_errors(supplier_id):
    """测试各种验证错误"""
    print(f"\n=== 测试验证错误 (ID: {supplier_id}) ===")
    
    # 测试空银行名称
    print("\n--- 测试空银行名称 ---")
    data = {
        "bank_name": "",
        "account_number": "*********0*********",
        "is_default": False
    }
    safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data, headers=HEADERS)
    
    # 测试无效账号（太短）
    print("\n--- 测试无效账号（太短） ---")
    data = {
        "bank_name": "测试银行",
        "account_number": "*********",
        "is_default": False
    }
    safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data, headers=HEADERS)
    
    # 测试无效账号（包含字母）
    print("\n--- 测试无效账号（包含字母） ---")
    data = {
        "bank_name": "测试银行",
        "account_number": "*********0abcdefgh",
        "is_default": False
    }
    safe_request("POST", f"{BASE_URL}/partners/{supplier_id}/add_payment_info/", json=data, headers=HEADERS)

def main():
    """主测试函数"""
    print("开始测试相对方收款信息管理...")
    
    try:
        # 创建测试数据
        supplier_id = create_supplier_partner()
        customer_id = create_customer_partner()
        
        if not supplier_id:
            print("创建供应商失败，无法继续测试")
            return
            
        if not customer_id:
            print("创建客户失败，无法继续测试")
            return
        
        # 测试为供应商添加收款信息
        payment_info_id = test_add_payment_info_to_supplier(supplier_id)
        
        # 测试为客户添加收款信息（应该失败）
        test_add_payment_info_to_customer(customer_id)
        
        # 测试获取收款信息列表
        test_get_payment_infos(supplier_id)
        test_get_payment_infos(customer_id)  # 客户应该返回空列表
        
        # 测试添加多个收款信息
        second_payment_info_id = test_add_multiple_payment_infos(supplier_id)
        
        # 再次获取收款信息列表，查看默认账户变化
        test_get_payment_infos(supplier_id)
        
        # 测试更新收款信息
        if second_payment_info_id:
            test_update_payment_info(supplier_id, second_payment_info_id)
        
        # 测试删除收款信息
        if second_payment_info_id:
            test_delete_payment_info(supplier_id, second_payment_info_id)
        
        # 最后再次获取收款信息列表
        test_get_payment_infos(supplier_id)
        
        # 测试各种验证错误
        test_validation_errors(supplier_id)
        
        print("\n=== 所有收款信息管理测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
