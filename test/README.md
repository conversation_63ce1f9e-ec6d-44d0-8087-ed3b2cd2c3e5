# 测试脚本目录

本目录包含系统级别的测试脚本，用于验证系统功能和配置。

## 📁 文件说明

### 🔗 连接测试

- **`test_connections.py`** - Python版本的连接测试脚本
  - 测试PostgreSQL数据库连接
  - 测试Redis缓存连接  
  - 测试MinIO对象存储连接
  - 提供详细的连接状态报告

- **`test-connections.sh`** - Bash版本的连接测试脚本
  - 功能与Python版本类似
  - 适用于shell环境
  - 包含详细的错误诊断

### 🧪 功能测试

- **`test_project_code.py`** - 项目编码生成测试
  - 验证项目编码格式：`P001-C250606001`
  - 测试序号递增逻辑
  - 验证客户编号关联

- **`test_error_response.py`** - API错误响应格式测试
  - 验证错误响应格式：`{"code": xxxxx, "msg": {...}}`
  - 测试验证错误处理
  - 确保响应格式一致性

## 🚀 使用方法

### 连接测试

```bash
# Python版本（推荐）
cd /path/to/project
source venv/bin/activate
python test/test_connections.py

# Bash版本
cd /path/to/project
source .env
./test/test-connections.sh
```

### 功能测试

```bash
# 项目编码测试
cd /path/to/project
source venv/bin/activate
python test/test_project_code.py

# 错误响应格式测试
cd /path/to/project
source venv/bin/activate
python test/test_error_response.py
```

## 📋 测试场景

### 部署验证
- 新环境部署后运行连接测试
- 确保所有外部服务正常工作
- 验证配置文件正确性

### 开发验证
- 代码修改后运行功能测试
- 确保核心业务逻辑正确
- 验证API响应格式一致

### CI/CD集成
这些脚本可以集成到CI/CD流程中：

```yaml
# 示例：GitHub Actions
- name: Test Connections
  run: |
    source venv/bin/activate
    python test/test_connections.py

- name: Test Business Logic
  run: |
    source venv/bin/activate
    python test/test_project_code.py
    python test/test_error_response.py
```

## 🔧 维护说明

- 这些是**系统级测试脚本**，不是Django单元测试
- Django单元测试位于 `erp/tests/` 目录
- 修改核心业务逻辑时，请同步更新相关测试脚本
- 添加新的外部依赖时，请更新连接测试脚本

## 📝 注意事项

1. **环境依赖**：测试脚本需要完整的Django环境
2. **数据库权限**：连接测试需要数据库读写权限
3. **网络访问**：某些测试需要访问外部服务
4. **数据清理**：功能测试会创建和删除测试数据

## 🆘 故障排查

如果测试失败，请检查：

1. **环境变量**：确保 `.env` 文件配置正确
2. **服务状态**：确保PostgreSQL、Redis、MinIO服务运行正常
3. **网络连接**：确保服务地址可访问
4. **权限设置**：确保数据库用户有足够权限
5. **依赖安装**：确保所有Python依赖已安装
