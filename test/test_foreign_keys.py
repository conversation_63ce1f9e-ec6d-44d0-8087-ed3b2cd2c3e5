#!/usr/bin/env python3
"""
外键关系测试脚本
验证项目和合同模块的外键关系是否正确更新到Partner模型
"""

import requests
import json
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置
BASE_URL = "http://localhost:8000/api/v1"
JWT_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {JWT_TOKEN}",
    "Content-Type": "application/json"
}

def safe_request(method, url, **kwargs):
    """安全的请求方法，处理JSON解析错误"""
    try:
        response = getattr(requests, method.lower())(url, **kwargs)
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response.status_code, response_data
        except json.JSONDecodeError:
            print(f"响应内容: {response.text}")
            return response.status_code, None
    except Exception as e:
        print(f"请求失败: {e}")
        return None, None

def test_projects():
    """测试项目API"""
    print("=== 测试项目API ===")
    status_code, response_data = safe_request("GET", f"{BASE_URL}/projects/", headers=HEADERS)
    
    if status_code == 200 and response_data:
        total = response_data['data']['total']
        print(f"项目总数: {total}")
        
        if total > 0:
            items = response_data['data']['items']
            for item in items[:3]:
                print(f"项目: {item['code']}, 客户: {item.get('customer_name', 'None')}")

def test_contracts():
    """测试合同API"""
    print("\n=== 测试合同API ===")
    status_code, response_data = safe_request("GET", f"{BASE_URL}/contracts/", headers=HEADERS)
    
    if status_code == 200 and response_data:
        total = response_data['data']['total']
        print(f"合同总数: {total}")
        
        if total > 0:
            items = response_data['data']['items']
            for item in items[:3]:
                customer_name = item.get('customer_name', 'None')
                supplier_name = item.get('supplier_name', 'None')
                print(f"合同: {item['code']}, 客户: {customer_name}, 供应商: {supplier_name}")

def test_partners():
    """测试相对方API"""
    print("\n=== 测试相对方API ===")
    status_code, response_data = safe_request("GET", f"{BASE_URL}/partners/", headers=HEADERS)
    
    if status_code == 200 and response_data:
        total = response_data['data']['total']
        print(f"相对方总数: {total}")
        
        if total > 0:
            items = response_data['data']['items']
            for item in items[:3]:
                print(f"相对方: {item['name']}, 类型: {item['type']}")

def test_customer_statistics():
    """测试客户统计功能"""
    print("\n=== 测试客户统计功能 ===")
    status_code, response_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    
    if status_code == 200 and response_data:
        items = response_data['data']['items']
        for item in items[:3]:
            project_count = item.get('project_count', 0)
            sales_contract_count = item.get('sales_contract_count', 0)
            sales_contract_total_amount = item.get('sales_contract_total_amount', 0)
            print(f"客户: {item['name']}")
            print(f"  项目数: {project_count}")
            print(f"  销售合同数: {sales_contract_count}")
            print(f"  销售合同总金额: {sales_contract_total_amount}")

def test_create_project():
    """测试创建项目（使用Partner作为客户）"""
    print("\n=== 测试创建项目 ===")
    
    # 先获取一个客户类型的相对方
    status_code, response_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    
    if status_code == 200 and response_data and response_data['data']['items']:
        customer = response_data['data']['items'][0]
        customer_id = customer['id']
        
        timestamp = int(time.time())
        project_data = {
            "name": f"外键测试项目{timestamp}",
            "customer": customer_id,
            "sales_responsible_id": "admin",
            "type": ["WEB"]
        }
        
        status_code, response_data = safe_request("POST", f"{BASE_URL}/projects/", json=project_data, headers=HEADERS)
        
        if status_code == 201 and response_data:
            return response_data['data']['id']
    
    return None

def test_create_contract():
    """测试创建合同（使用Partner作为客户和供应商）"""
    print("\n=== 测试创建合同 ===")
    
    # 获取客户和供应商
    status_code, customer_data = safe_request("GET", f"{BASE_URL}/customers/", headers=HEADERS)
    status_code, supplier_data = safe_request("GET", f"{BASE_URL}/suppliers/", headers=HEADERS)
    
    if (customer_data and customer_data['data']['items'] and 
        supplier_data and supplier_data['data']['items']):
        
        customer = customer_data['data']['items'][0]
        supplier = supplier_data['data']['items'][0]
        
        timestamp = int(time.time())
        contract_data = {
            "category": "sales",
            "customer_id": customer['id'],
            "amount": 100000.00,
            "signing_date": "2025-07-30"
        }
        
        status_code, response_data = safe_request("POST", f"{BASE_URL}/contracts/", json=contract_data, headers=HEADERS)
        
        if status_code == 201 and response_data:
            return response_data['data']['id']
    
    return None

def main():
    """主测试函数"""
    print("开始外键关系测试...")
    
    try:
        # 测试现有数据
        test_projects()
        test_contracts()
        test_partners()
        
        # 测试统计功能
        test_customer_statistics()
        
        # 测试创建功能
        project_id = test_create_project()
        contract_id = test_create_contract()
        
        print("\n=== 外键关系测试完成 ===")
        print(f"创建的项目ID: {project_id}")
        print(f"创建的合同ID: {contract_id}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
