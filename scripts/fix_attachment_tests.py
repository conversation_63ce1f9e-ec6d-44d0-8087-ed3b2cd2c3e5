#!/usr/bin/env python3
"""
脚本用于修复测试文件中的 Attachment.objects.create 调用，添加必填的 category 字段
"""

import re
import os

def fix_attachment_create_calls(file_path):
    """修复文件中的 Attachment.objects.create 调用"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有 Attachment.objects.create 调用
    pattern = r'(Attachment\.objects\.create\(\s*)([\s\S]*?)(\s*\))'
    
    def replace_create_call(match):
        prefix = match.group(1)
        params = match.group(2)
        suffix = match.group(3)
        
        # 检查是否已经有 category 参数
        if 'category=' in params:
            return match.group(0)  # 已经有 category，不需要修改
        
        # 添加 category 参数
        # 根据文件路径或内容判断应该使用哪种类型
        if 'content_type=content_type' in params or 'ContentType.objects.get_for_model(Customer)' in params:
            category = "'project_other'"
        elif 'contract' in params.lower():
            category = "'contract_other'"
        else:
            category = "'project_other'"  # 默认值
        
        # 在 creator 参数之前插入 category
        if 'creator=' in params:
            params = re.sub(r'(\s+)(creator=)', r'\1category=' + category + ',\n\1\2', params)
        else:
            # 如果没有 creator，在最后添加
            params = params.rstrip() + ',\n            category=' + category
        
        return prefix + params + suffix
    
    # 替换所有匹配的调用
    new_content = re.sub(pattern, replace_create_call, content)
    
    # 只有内容发生变化时才写入文件
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 已修复: {file_path}")
        return True
    else:
        print(f"⏭️  跳过: {file_path} (无需修改)")
        return False

def main():
    """主函数"""
    test_file = 'erp/tests/test_attachment_api.py'
    
    if os.path.exists(test_file):
        print(f"🔧 修复测试文件: {test_file}")
        if fix_attachment_create_calls(test_file):
            print("✅ 修复完成")
        else:
            print("ℹ️  无需修复")
    else:
        print(f"❌ 文件不存在: {test_file}")

if __name__ == '__main__':
    main()
