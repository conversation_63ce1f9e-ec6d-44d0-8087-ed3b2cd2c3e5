#!/usr/bin/env python
"""
全国行政区划数据导入脚本
从网络获取最新的省市区划代码数据并导入数据库
"""

import os
import sys
import django
import requests
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from erp.models import AdministrativeDivision


class AdministrativeDivisionImporter:
    """行政区划数据导入器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def get_data_from_github(self):
        """从GitHub获取行政区划数据"""
        print("正在从GitHub获取行政区划数据...")

        # 使用多个数据源
        urls = [
            'https://raw.githubusercontent.com/modood/Administrative-divisions-of-China/master/dist/provinces.json',
            'https://raw.githubusercontent.com/modood/Administrative-divisions-of-China/master/dist/cities.json',
            'https://raw.githubusercontent.com/modood/Administrative-divisions-of-China/master/dist/areas.json'
        ]
        
        data = {
            'provinces': [],
            'cities': [],
            'areas': []
        }
        
        try:
            # 获取省级数据
            response = self.session.get(urls[0], timeout=30)
            if response.status_code == 200:
                data['provinces'] = response.json()
                print(f"✓ 获取到 {len(data['provinces'])} 个省级行政区")
            
            time.sleep(1)  # 避免请求过快
            
            # 获取市级数据
            response = self.session.get(urls[1], timeout=30)
            if response.status_code == 200:
                data['cities'] = response.json()
                print(f"✓ 获取到 {len(data['cities'])} 个市级行政区")
            
            time.sleep(1)
            
            # 获取区县数据
            response = self.session.get(urls[2], timeout=30)
            if response.status_code == 200:
                data['areas'] = response.json()
                print(f"✓ 获取到 {len(data['areas'])} 个区县级行政区")
                
        except Exception as e:
            print(f"从GitHub获取数据失败: {e}")
            return None
            
        return data
    
    def get_fallback_data(self):
        """备用数据源：完整的全国省市数据"""
        print("使用完整的内置数据源...")

        # 导入完整的城市数据
        try:
            from complete_cities_data import get_complete_cities_data
            complete_cities = get_complete_cities_data()
            print(f"✓ 加载完整城市数据: {len(complete_cities)} 个")
        except ImportError:
            print("⚠️  无法加载完整城市数据，使用基础数据")
            complete_cities = []

        return {
            'provinces': [
                {'code': '110000', 'name': '北京市'},
                {'code': '120000', 'name': '天津市'},
                {'code': '130000', 'name': '河北省'},
                {'code': '140000', 'name': '山西省'},
                {'code': '150000', 'name': '内蒙古自治区'},
                {'code': '210000', 'name': '辽宁省'},
                {'code': '220000', 'name': '吉林省'},
                {'code': '230000', 'name': '黑龙江省'},
                {'code': '310000', 'name': '上海市'},
                {'code': '320000', 'name': '江苏省'},
                {'code': '330000', 'name': '浙江省'},
                {'code': '340000', 'name': '安徽省'},
                {'code': '350000', 'name': '福建省'},
                {'code': '360000', 'name': '江西省'},
                {'code': '370000', 'name': '山东省'},
                {'code': '410000', 'name': '河南省'},
                {'code': '420000', 'name': '湖北省'},
                {'code': '430000', 'name': '湖南省'},
                {'code': '440000', 'name': '广东省'},
                {'code': '450000', 'name': '广西壮族自治区'},
                {'code': '460000', 'name': '海南省'},
                {'code': '500000', 'name': '重庆市'},
                {'code': '510000', 'name': '四川省'},
                {'code': '520000', 'name': '贵州省'},
                {'code': '530000', 'name': '云南省'},
                {'code': '540000', 'name': '西藏自治区'},
                {'code': '610000', 'name': '陕西省'},
                {'code': '620000', 'name': '甘肃省'},
                {'code': '630000', 'name': '青海省'},
                {'code': '640000', 'name': '宁夏回族自治区'},
                {'code': '650000', 'name': '新疆维吾尔自治区'},
                {'code': '710000', 'name': '台湾省'},
                {'code': '810000', 'name': '香港特别行政区'},
                {'code': '820000', 'name': '澳门特别行政区'},
            ],
            'cities': complete_cities if complete_cities else [
                # 如果无法加载完整数据，使用基础数据
                {'code': '110101', 'name': '东城区', 'provinceCode': '110000'},
                {'code': '110102', 'name': '西城区', 'provinceCode': '110000'},
                {'code': '310101', 'name': '黄浦区', 'provinceCode': '310000'},
                {'code': '440100', 'name': '广州市', 'provinceCode': '440000'},
                {'code': '440300', 'name': '深圳市', 'provinceCode': '440000'},
            ],
            'areas': []  # 暂时不导入区县数据
        }
    
    def import_data(self, data):
        """导入数据到数据库"""
        if not data:
            print("❌ 没有数据可导入")
            return False
            
        print("\n开始导入数据到数据库...")
        
        # 清理现有数据
        print("清理现有行政区划数据...")
        AdministrativeDivision.objects.all().delete()
        
        success_count = 0
        error_count = 0
        
        # 导入省级数据
        print("导入省级数据...")
        for province in data['provinces']:
            try:
                AdministrativeDivision.objects.create(
                    code=province['code'],
                    name=province['name'],
                    level='province',
                    parent_code=None
                )
                success_count += 1
            except Exception as e:
                print(f"导入省份失败 {province}: {e}")
                error_count += 1
        
        print(f"✓ 省级数据导入完成: {success_count} 成功, {error_count} 失败")
        
        # 导入市级数据
        if data['cities']:
            print("导入市级数据...")
            city_success = 0
            city_error = 0
            
            for city in data['cities']:
                try:
                    # 处理不同的数据格式
                    parent_code = city.get('provinceCode') or city.get('parent_code')
                    if not parent_code and len(city['code']) == 6:
                        # 根据代码推断省份代码
                        parent_code = city['code'][:2] + '0000'
                    
                    AdministrativeDivision.objects.create(
                        code=city['code'],
                        name=city['name'],
                        level='city',
                        parent_code=parent_code
                    )
                    city_success += 1
                except Exception as e:
                    print(f"导入城市失败 {city}: {e}")
                    city_error += 1
            
            print(f"✓ 市级数据导入完成: {city_success} 成功, {city_error} 失败")
        
        # 导入区县数据（如果有）
        if data['areas']:
            print("导入区县数据...")
            area_success = 0
            area_error = 0
            
            for area in data['areas']:
                try:
                    parent_code = area.get('cityCode') or area.get('parent_code')
                    if not parent_code and len(area['code']) == 6:
                        # 根据代码推断城市代码
                        parent_code = area['code'][:4] + '00'
                    
                    AdministrativeDivision.objects.create(
                        code=area['code'],
                        name=area['name'],
                        level='district',
                        parent_code=parent_code
                    )
                    area_success += 1
                except Exception as e:
                    print(f"导入区县失败 {area}: {e}")
                    area_error += 1
            
            print(f"✓ 区县数据导入完成: {area_success} 成功, {area_error} 失败")
        
        # 统计结果
        total_imported = AdministrativeDivision.objects.count()
        print(f"\n📊 导入统计:")
        print(f"总计导入: {total_imported} 条记录")
        print(f"省级: {AdministrativeDivision.objects.filter(level='province').count()} 条")
        print(f"市级: {AdministrativeDivision.objects.filter(level='city').count()} 条")
        print(f"区县: {AdministrativeDivision.objects.filter(level='district').count()} 条")
        
        return total_imported > 0
    
    def run(self):
        """执行导入"""
        print("🌐 全国行政区划数据导入工具")
        print("=" * 50)
        
        # 尝试从网络获取数据
        data = self.get_data_from_github()
        
        # 如果网络获取失败，使用备用数据
        if not data or not data['provinces']:
            print("网络数据获取失败，使用备用数据源...")
            data = self.get_fallback_data()
        
        # 导入数据
        if self.import_data(data):
            print("\n✅ 行政区划数据导入成功！")
            return True
        else:
            print("\n❌ 行政区划数据导入失败！")
            return False


def main():
    """主函数"""
    importer = AdministrativeDivisionImporter()
    success = importer.run()
    
    if success:
        print("\n🎉 可以开始使用省市查询API了：")
        print("- 省份列表: GET /api/provinces/")
        print("- 城市列表: GET /api/provinces/{province_code}/cities/")
        print("- 区县列表: GET /api/cities/{city_code}/districts/")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
