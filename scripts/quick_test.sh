#!/bin/bash

# 超快速测试脚本 - 使用内存数据库和优化设置
# 适用于不需要PostgreSQL特性的单元测试
# 使用方法：
# ./scripts/quick_test.sh                    # 运行所有测试
# ./scripts/quick_test.sh erp.tests.test_attachment_api  # 运行特定测试模块

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}⚡ 快速测试模式 (内存数据库)${NC}"

# 检查是否提供了特定测试路径
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}运行所有测试...${NC}"
    TEST_PATH=""
else
    echo -e "${YELLOW}运行测试: $1${NC}"
    TEST_PATH="$1"
fi

# 运行测试，使用优化的测试设置
echo -e "${YELLOW}执行命令: python manage.py test $TEST_PATH --settings=application.test_settings -v 2${NC}"

python manage.py test $TEST_PATH --settings=application.test_settings -v 2

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 测试通过！${NC}"
else
    echo -e "${RED}❌ 测试失败！${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 快速测试完成${NC}"
