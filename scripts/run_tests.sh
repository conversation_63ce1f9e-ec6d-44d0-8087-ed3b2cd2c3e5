#!/bin/bash

# 快速测试脚本 - 保留测试数据库以避免重建表结构
# 使用方法：
# ./scripts/run_tests.sh                    # 运行所有测试
# ./scripts/run_tests.sh erp.tests.test_attachment_api  # 运行特定测试模块
# ./scripts/run_tests.sh erp.tests.test_attachment_api.AttachmentModelTestCase.test_attachment_category_field  # 运行特定测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 运行测试 (保留测试数据库)${NC}"

# 检查是否提供了特定测试路径
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}运行所有测试...${NC}"
    TEST_PATH=""
else
    echo -e "${YELLOW}运行测试: $1${NC}"
    TEST_PATH="$1"
fi

# 运行测试，使用 --keepdb 保留测试数据库
echo -e "${YELLOW}执行命令: python manage.py test $TEST_PATH --keepdb -v 2${NC}"

python manage.py test $TEST_PATH --keepdb -v 2

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 测试通过！${NC}"
else
    echo -e "${RED}❌ 测试失败！${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 测试完成${NC}"
