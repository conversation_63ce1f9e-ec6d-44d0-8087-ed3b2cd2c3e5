version: '3.8'

services:
  # 业财一体化系统主应用
  erp-web:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: erp_web_prod
    restart: unless-stopped
    environment:
      # 数据库配置
      - DATABASE_NAME=erp_finance
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD:-Postgres!23}
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-Redis!23}
      - REDIS_DB=0
      
      # MinIO配置
      - MINIO_HOST=minio
      - MINIO_API_PORT=9000
      - MINIO_STORAGE_ACCESS_KEY=${MINIO_ROOT_USER:-admin}
      - MINIO_STORAGE_SECRET_KEY=${MINIO_ROOT_PASSWORD:-MinIO!2024@ERP}
      - MINIO_STORAGE_MEDIA_BUCKET_NAME=erp-finance
      - MINIO_STORAGE_MEDIA_URL=http://minio:9000/erp-finance/
      
      # 应用配置
      - DEBUG=false
      - USE_UVICORN=true
      - HOST=0.0.0.0
      - PORT=8000
      - WORKERS=4
      - ENABLE_CELERY=true
      - ENABLE_CELERY_BEAT=false
      
      # 其他配置
      - TZ=Asia/Shanghai
      - USER_CENTER_HOST=${USER_CENTER_HOST:-http://**********:8087}
      - NOTIFICATION_HOST=${NOTIFICATION_HOST:-http://**********:9103}
    
    ports:
      - "${ERP_PORT:-8000}:8000"
    
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
      - logs_volume:/app/logs
    
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    
    networks:
      - erp_network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL 14 数据库
  postgres:
    image: postgres:14
    container_name: erp_postgres_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: erp_finance
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-Postgres!23}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TZ: Asia/Shanghai
    
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    
    networks:
      - erp_network
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d erp_finance"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: erp_redis_prod
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-Redis!23} --appendonly yes
    environment:
      TZ: Asia/Shanghai
    
    ports:
      - "${REDIS_PORT:-6379}:6379"
    
    volumes:
      - redis_data:/data
    
    networks:
      - erp_network
    
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-Redis!23}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # MinIO 对象存储服务
  minio:
    image: minio/minio:RELEASE.2023-12-07T04-16-00Z
    container_name: erp_minio_prod
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-MinIO!2024@ERP}
      TZ: Asia/Shanghai
    
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    
    volumes:
      - minio_data:/data
    
    networks:
      - erp_network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: erp_nginx_prod
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - static_volume:/app/static:ro
      - media_volume:/app/media:ro
      - ./ssl:/etc/nginx/ssl:ro
    
    depends_on:
      - erp-web
    
    networks:
      - erp_network
    
    profiles:
      - nginx

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  logs_volume:
    driver: local

# 网络
networks:
  erp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
