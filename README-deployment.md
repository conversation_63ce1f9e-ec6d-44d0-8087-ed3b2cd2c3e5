# 业财一体化系统 - 部署指南

## 概述

本项目基于training_backend项目模式，提供了完整的Docker化部署方案，支持开发和生产环境。

## 技术栈

- **Python**: 3.12.8
- **Django**: 4.2.7 + Django REST Framework
- **数据库**: PostgreSQL 14+
- **缓存**: Redis 7+ (带密码认证)
- **任务队列**: Celery
- **ASGI服务器**: uvicorn (生产环境)
- **对象存储**: MinIO
- **容器化**: Docker + Docker Compose

## 文件说明

### 核心文件

- `startup.sh` - 应用启动脚本，处理数据库迁移、静态文件收集、Celery启动等
- `test-connections.sh` - 连接测试脚本，验证数据库和Redis配置
- `Dockerfile` - Docker镜像构建文件，基于Python 3.12
- `docker-compose.production.yml` - 生产环境Docker编排文件
- `.env.production` - 生产环境配置模板

### 配置特点

1. **启动脚本特性**:
   - 自动等待数据库和Redis服务就绪
   - 执行数据库迁移
   - 收集静态文件
   - 后台启动Celery Worker
   - 使用uvicorn启动ASGI服务器（4个工作进程）

2. **Docker镜像特性**:
   - 基于Python 3.12-slim
   - 包含PostgreSQL和Redis客户端
   - 非root用户运行（安全最佳实践）
   - 内置健康检查
   - 支持时区设置（Asia/Shanghai）

3. **安全配置**:
   - Redis密码认证
   - PostgreSQL密码保护
   - MinIO访问控制
   - 非root容器用户

## 快速部署

### 1. 生产环境部署

```bash
# 1. 复制环境配置
cp .env.production .env

# 2. 修改密码（重要！）
vim .env
# 修改以下密码：
# - POSTGRES_PASSWORD
# - REDIS_PASSWORD
# - MINIO_ROOT_PASSWORD

# 3. 测试连接配置（可选但推荐）
source .env
./test-connections.sh

# 4. 启动所有服务
docker-compose -f docker-compose.production.yml --env-file .env up -d

# 5. 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 6. 查看应用日志
docker-compose -f docker-compose.production.yml logs -f erp-web
```

### 2. 开发环境部署

```bash
# 使用现有的docker-compose.yml
docker-compose up -d
```

## 服务访问

- **应用主页**: http://localhost:8000
- **API文档**: http://localhost:8000/swagger/
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **MinIO控制台**: http://localhost:9001

## 环境变量说明

### 数据库配置
```bash
DATABASE_NAME=erp_finance          # 数据库名
DATABASE_HOST=postgres             # 数据库主机
DATABASE_USER=postgres             # 数据库用户
DATABASE_PASSWORD=your_password    # 数据库密码
```

### Redis配置
```bash
REDIS_HOST=redis                   # Redis主机
REDIS_PASSWORD=your_password       # Redis密码
REDIS_DB=0                         # Redis数据库编号
```

### 应用配置
```bash
DEBUG=false                        # 调试模式
USE_UVICORN=true                   # 使用uvicorn服务器
WORKERS=4                          # 工作进程数
ENABLE_CELERY=true                 # 启用Celery
```

## 运维命令

### 查看服务状态
```bash
docker-compose -f docker-compose.production.yml ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f erp-web
docker-compose -f docker-compose.production.yml logs -f postgres
```

### 执行管理命令
```bash
# 进入应用容器
docker-compose -f docker-compose.production.yml exec erp-web bash

# 执行Django命令
docker-compose -f docker-compose.production.yml exec erp-web python manage.py migrate
docker-compose -f docker-compose.production.yml exec erp-web python manage.py createsuperuser
```

### 数据备份
```bash
# 备份PostgreSQL数据库
docker-compose -f docker-compose.production.yml exec postgres pg_dump -U postgres erp_finance > backup.sql

# 恢复数据库
docker-compose -f docker-compose.production.yml exec -T postgres psql -U postgres erp_finance < backup.sql
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.production.yml restart

# 重启特定服务
docker-compose -f docker-compose.production.yml restart erp-web
```

### 停止服务
```bash
# 停止所有服务
docker-compose -f docker-compose.production.yml down

# 停止并删除数据卷（谨慎操作）
docker-compose -f docker-compose.production.yml down -v
```

## 故障排查

### 0. 连接测试（推荐首先执行）
```bash
# 测试所有连接配置
source .env
./test-connections.sh

# 或在容器内测试
docker-compose -f docker-compose.production.yml exec erp-web ./test-connections.sh
```

### 1. 应用无法启动
```bash
# 查看启动日志
docker-compose -f docker-compose.production.yml logs erp-web

# 检查数据库连接
docker-compose -f docker-compose.production.yml exec erp-web python manage.py dbshell
```

### 2. 数据库连接问题
```bash
# 检查PostgreSQL状态
docker-compose -f docker-compose.production.yml exec postgres pg_isready -U postgres

# 查看数据库日志
docker-compose -f docker-compose.production.yml logs postgres
```

### 3. Redis连接问题
```bash
# 测试Redis连接
docker-compose -f docker-compose.production.yml exec redis redis-cli -a your_password ping
```

## 性能优化

1. **调整工作进程数**: 修改环境变量 `WORKERS`
2. **数据库连接池**: 在Django settings中配置连接池
3. **静态文件**: 使用Nginx提供静态文件服务
4. **缓存策略**: 配置Redis缓存策略

## 安全建议

1. **修改默认密码**: 生产环境必须修改所有默认密码
2. **网络隔离**: 使用防火墙限制端口访问
3. **SSL证书**: 配置HTTPS证书
4. **定期备份**: 设置自动备份策略
5. **日志监控**: 配置日志收集和监控

## 扩展配置

### 添加Nginx反向代理
```bash
# 启用Nginx服务
docker-compose -f docker-compose.production.yml --profile nginx up -d
```

### 启用Celery Beat定时任务
```bash
# 修改环境变量
ENABLE_CELERY_BEAT=true
```
