#!/bin/bash

# 业财一体化系统启动脚本
# 基于training_backend项目模式，适配ERP系统

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 等待数据库就绪
wait_for_db() {
    log_step "等待数据库连接就绪..."
    
    # 获取数据库配置
    DB_HOST=${DATABASE_HOST:-localhost}
    DB_PORT=${DATABASE_PORT:-5432}
    DB_NAME=${DATABASE_NAME:-erp_finance}
    DB_USER=${DATABASE_USER:-postgres}
    DB_PASSWORD=${DATABASE_PASSWORD:-}
    
    # 最大等待时间（秒）
    MAX_WAIT=60
    WAIT_COUNT=0
    
    while [ $WAIT_COUNT -lt $MAX_WAIT ]; do
        # 设置密码环境变量（如果有密码）
        if [ -n "$DB_PASSWORD" ]; then
            export PGPASSWORD="$DB_PASSWORD"
        fi

        if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
            log_info "数据库连接成功"
            # 清除密码环境变量
            unset PGPASSWORD
            return 0
        fi
        
        log_warn "等待数据库连接... ($((WAIT_COUNT + 1))/$MAX_WAIT)"
        sleep 1
        WAIT_COUNT=$((WAIT_COUNT + 1))
    done

    # 清除密码环境变量
    unset PGPASSWORD
    log_error "数据库连接超时"
    exit 1
}

# 等待Redis就绪
wait_for_redis() {
    log_step "等待Redis连接就绪..."
    
    REDIS_HOST=${REDIS_HOST:-localhost}
    REDIS_PORT=${REDIS_PORT:-6379}
    REDIS_PASSWORD=${REDIS_PASSWORD:-}
    
    MAX_WAIT=30
    WAIT_COUNT=0
    
    while [ $WAIT_COUNT -lt $MAX_WAIT ]; do
        if [ -n "$REDIS_PASSWORD" ]; then
            # 有密码的情况
            if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
                log_info "Redis连接成功"
                return 0
            fi
        else
            # 无密码的情况
            if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping >/dev/null 2>&1; then
                log_info "Redis连接成功"
                return 0
            fi
        fi
        
        log_warn "等待Redis连接... ($((WAIT_COUNT + 1))/$MAX_WAIT)"
        sleep 1
        WAIT_COUNT=$((WAIT_COUNT + 1))
    done
    
    log_warn "Redis连接超时，继续启动（Redis为可选服务）"
}

# 收集静态文件
collect_static() {
    log_step "收集静态文件..."
    python manage.py collectstatic --noinput
    log_info "静态文件收集完成"
}

# 启动Celery Worker
start_celery() {
    log_step "启动Celery Worker..."
    
    # 检查是否需要启动Celery
    if [ "${ENABLE_CELERY:-true}" = "true" ]; then
        # 启动Celery Worker（后台运行）
        celery -A application worker -l info --detach
        log_info "Celery Worker已启动"
        
        # 可选：启动Celery Beat（定时任务）
        if [ "${ENABLE_CELERY_BEAT:-false}" = "true" ]; then
            celery -A application beat -l info --detach
            log_info "Celery Beat已启动"
        fi
    else
        log_warn "Celery已禁用，跳过启动"
    fi
}

# 启动Django应用
start_django() {
    log_step "启动Django应用..."
    
    # 获取服务器配置
    HOST=${HOST:-0.0.0.0}
    PORT=${PORT:-8000}
    WORKERS=${WORKERS:-4}
    
    # 根据环境选择启动方式
    if [ "${USE_UVICORN:-true}" = "true" ]; then
        log_info "使用uvicorn启动ASGI服务器..."
        log_info "服务地址: http://$HOST:$PORT"
        log_info "工作进程数: $WORKERS"
        
        # 使用uvicorn启动（生产环境推荐）
        exec uvicorn application.asgi:application \
            --host "$HOST" \
            --port "$PORT" \
            --workers "$WORKERS" \
            --access-log \
            --log-level info
    else
        log_info "使用Django开发服务器启动..."
        log_info "服务地址: http://$HOST:$PORT"
        
        # 使用Django开发服务器（开发环境）
        exec python manage.py runserver "$HOST:$PORT"
    fi
}

# 主函数
main() {
    log_info "========================================"
    log_info "业财一体化系统启动脚本"
    log_info "========================================"
    
    # 显示环境信息
    log_info "Python版本: $(python --version)"
    log_info "Django版本: $(python -c 'import django; print(django.get_version())')"
    log_info "工作目录: $(pwd)"
    
    # 等待依赖服务
    wait_for_db
    wait_for_redis
    
    # 收集静态文件
    collect_static
    
    # 启动后台服务
    start_celery
    
    # 启动主应用
    start_django
}

# 信号处理
cleanup() {
    log_info "正在关闭服务..."
    
    # 停止Celery进程
    pkill -f "celery.*application" || true
    
    log_info "服务已关闭"
    exit 0
}

# 注册信号处理器
trap cleanup SIGTERM SIGINT

# 执行主函数
main "$@"
