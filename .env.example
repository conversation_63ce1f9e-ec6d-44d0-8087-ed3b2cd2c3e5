# Docker Compose 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# ================================================= #
# ****************** 数据库配置 ******************* #
# ================================================= #

# PostgreSQL 配置
POSTGRES_PASSWORD=Postgres!23
POSTGRES_PORT=5432

# Redis 配置
REDIS_PASSWORD=Redis!23
REDIS_PORT=6379

# ================================================= #
# ****************** 存储配置 ******************** #
# ================================================= #

# MinIO 配置
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=MinIO!2024@ERP
MINIO_API_PORT=9000

# RUSTFS 配置
RUSTFS_ACCESS_KEY=admin
RUSTFS_SECRET_KEY=Ul5-s9Nt4
RUSTFS_CONSOLE_ENABLE=true
RUSTFS_PORT=9002

# ================================================= #
# ****************** 应用配置 ******************** #
# ================================================= #

# Django 应用配置
# 存储后端选择: 'minio' 或 'rustfs'
STORAGE_BACKEND=rustfs

# 数据库连接
DATABASE_URL=postgresql://postgres:Postgres!23@localhost:5432/erp_finance

# Redis 连接
REDIS_URL=redis://:Redis!23@localhost:6379/0

# 存储服务连接
MINIO_ENDPOINT=localhost:9000
RUSTFS_ENDPOINT=http://localhost:9002
