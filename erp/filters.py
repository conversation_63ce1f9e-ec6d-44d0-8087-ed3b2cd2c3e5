"""
自定义过滤器
"""
import django_filters
from django.db import models
from erp.models import Customer, Supplier, Partner, Project, Contract, CustomField, PaymentPeriod, Invoice


class MultiChoiceFilter(django_filters.CharFilter):
    """
    多选过滤器，支持逗号分隔的值
    
    使用方式：
    - 单个值：?type=C
    - 多个值：?type=C,G
    """
    
    def filter(self, qs, value):
        if not value:
            return qs
        
        # 分割逗号分隔的值
        values = [v.strip() for v in value.split(',') if v.strip()]
        if not values:
            return qs
        
        # 使用 __in 查询匹配多个值
        lookup = f'{self.field_name}__in'
        return qs.filter(**{lookup: values})


class CustomerFilter(django_filters.FilterSet):
    """客户过滤器（兼容性过滤器，使用Partner模型）"""
    
    # 支持多选的字段
    type = MultiChoiceFilter(
        field_name='type',
        help_text='客户类型，支持多选，使用逗号分隔多个值，如：C,G'
    )
    status = MultiChoiceFilter(
        field_name='status',
        help_text='客户状态，支持多选，使用逗号分隔多个值，如：ACTIVE,BLACKLISTED,DEACTIVATED'
    )
    industry = MultiChoiceFilter(
        field_name='industry',
        help_text='所属行业，支持多选，使用逗号分隔多个值，如：FINANCE,GOVT,IT'
    )
    
    # 其他常规过滤字段
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索关键词，支持客户名称、编码、联系人、电话、纳税人识别号等字段'
    )
    province = django_filters.CharFilter(
        field_name='province',
        lookup_expr='icontains',
        help_text='省份名称，支持模糊匹配'
    )
    city = django_filters.CharFilter(
        field_name='city',
        lookup_expr='icontains',
        help_text='城市名称，支持模糊匹配'
    )
    owner_name = django_filters.CharFilter(
        field_name='owner_name',
        lookup_expr='icontains',
        help_text='负责人姓名，支持模糊匹配'
    )
    
    class Meta:
        model = Partner
        fields = ['type', 'status', 'industry', 'search', 'province', 'city', 'owner_name']
    
    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value) |
            models.Q(contact_person__icontains=value) |
            models.Q(contact_phone__icontains=value) |
            models.Q(tax_id__icontains=value)
        )


class SupplierFilter(django_filters.FilterSet):
    """供应商过滤器"""
    
    # 支持多选的字段
    type = MultiChoiceFilter(
        field_name='type',
        help_text='供应商类型，支持多选，使用逗号分隔多个值，如：ORIGINAL,CHANNEL'
    )
    industry = MultiChoiceFilter(
        field_name='industry',
        help_text='所属行业，支持多选，使用逗号分隔多个值，如：HARDWARE,SOFTWARE,INTEGRATION'
    )
    status = MultiChoiceFilter(
        field_name='status',
        help_text='合作状态，支持多选，使用逗号分隔多个值，如：ACTIVE,SUSPENDED,BLACKLISTED'
    )

    # 其他常规过滤字段
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索关键词，支持供应商名称、编码、联系人、电话、纳税人识别号等字段'
    )
    province = django_filters.CharFilter(
        field_name='province',
        lookup_expr='icontains',
        help_text='省份名称，支持模糊匹配'
    )
    city = django_filters.CharFilter(
        field_name='city',
        lookup_expr='icontains',
        help_text='城市名称，支持模糊匹配'
    )
    owner_name = django_filters.CharFilter(
        field_name='owner_name',
        lookup_expr='icontains',
        help_text='负责人姓名，支持模糊匹配'
    )
    
    class Meta:
        model = Supplier
        fields = ['type', 'industry', 'status', 'search', 'province', 'city', 'owner_name']
    
    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value) |
            models.Q(contact_person__icontains=value) |
            models.Q(contact_phone__icontains=value) |
            models.Q(tax_id__icontains=value)
        )


class ProjectFilter(django_filters.FilterSet):
    """项目过滤器"""
    
    # 支持多选的字段
    type = MultiChoiceFilter(
        field_name='type',
        help_text='项目类型，支持多选，使用逗号分隔多个值，如：system_integration,software_development'
    )
    
    # 其他常规过滤字段
    status = django_filters.ChoiceFilter(
        field_name='status',
        choices=Project.PROJECT_STATUS_CHOICES,
        help_text='项目状态，可选值：preparing(准备中)、in_progress(进行中)、paused(暂停)、completed(已完成)、cancelled(已取消)'
    )
    reason = django_filters.ChoiceFilter(
        field_name='reason',
        choices=Project.PROJECT_REASON_CHOICES,
        help_text='立项理由，可选值：bidding(投标)、signed(已签约)、poc(概念验证)、other(其他)'
    )
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索关键词，支持项目名称、编码、客户名称等字段'
    )
    customer_id = django_filters.UUIDFilter(
        field_name='customer__id',
        help_text='客户ID，精确匹配指定客户的项目'
    )
    customer_name = django_filters.CharFilter(
        field_name='customer__name',
        lookup_expr='icontains',
        help_text='客户名称，支持模糊匹配'
    )
    sales_manager_name = django_filters.CharFilter(
        field_name='sales_manager_name',
        lookup_expr='icontains',
        help_text='销售负责人姓名，支持模糊匹配'
    )
    start_date_after = django_filters.DateFilter(
        field_name='start_date',
        lookup_expr='gte',
        help_text='项目开始日期范围筛选（起始日期），格式：YYYY-MM-DD'
    )
    start_date_before = django_filters.DateFilter(
        field_name='start_date',
        lookup_expr='lte',
        help_text='项目开始日期范围筛选（结束日期），格式：YYYY-MM-DD'
    )
    end_date_after = django_filters.DateFilter(
        field_name='end_date',
        lookup_expr='gte',
        help_text='项目结束日期范围筛选（起始日期），格式：YYYY-MM-DD'
    )
    end_date_before = django_filters.DateFilter(
        field_name='end_date',
        lookup_expr='lte',
        help_text='项目结束日期范围筛选（结束日期），格式：YYYY-MM-DD'
    )
    
    class Meta:
        model = Project
        fields = [
            'type', 'status', 'reason', 'search', 'customer_id', 'customer_name',
            'sales_manager_name', 'start_date_after', 'start_date_before',
            'end_date_after', 'end_date_before'
        ]
    
    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset
        
        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value) |
            models.Q(customer__name__icontains=value)
        )


class ContractFilter(django_filters.FilterSet):
    """合同过滤器"""

    # 支持多选的字段
    category = MultiChoiceFilter(
        field_name='category',
        help_text='合同类别，支持多选，使用逗号分隔多个值，如：sales,procurement'
    )

    # 其他常规过滤字段
    sign_status = django_filters.ChoiceFilter(
        field_name='sign_status',
        choices=Contract.SIGN_STATUS_CHOICES,
        help_text='签订状态，可选值：unsigned(未签约)、communicating(沟通中)、signing(签约中)、signed(已签约)、terminating(解约中)'
    )
    performance_status = django_filters.ChoiceFilter(
        field_name='performance_status',
        choices=Contract.PERFORMANCE_STATUS_CHOICES,
        help_text='履约状态，可选值：not_performed(未履约)、performing(履约中)、performed(已履约)'
    )
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索关键词，支持合同名称、编号等字段'
    )
    project_id = django_filters.UUIDFilter(
        field_name='project__id',
        help_text='项目ID，精确匹配指定项目的合同'
    )
    partner_id = django_filters.UUIDFilter(
        field_name='partners__id',
        help_text='相对方ID，精确匹配指定相对方的合同'
    )
    total_amount_min = django_filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='gte',
        help_text='最小金额，筛选大于等于指定金额的合同'
    )
    total_amount_max = django_filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='lte',
        help_text='最大金额，筛选小于等于指定金额的合同'
    )
    sign_date_after = django_filters.DateFilter(
        field_name='sign_date',
        lookup_expr='gte',
        help_text='签约日期范围筛选（起始日期），格式：YYYY-MM-DD'
    )
    sign_date_before = django_filters.DateFilter(
        field_name='sign_date',
        lookup_expr='lte',
        help_text='签约日期范围筛选（结束日期），格式：YYYY-MM-DD'
    )

    class Meta:
        model = Contract
        fields = ['category', 'sign_status', 'performance_status', 'search', 'project_id', 'partner_id',
                 'total_amount_min', 'total_amount_max', 'sign_date_after', 'sign_date_before']

    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value)
        )


class PartnerFilter(django_filters.FilterSet):
    """相对方过滤器"""

    # 支持多选的字段
    type = MultiChoiceFilter(
        field_name='type',
        help_text='相对方类型，支持多选，逗号分隔。可选值：C（企业客户）、G（政府客户）、ORIGINAL（原厂）、CHANNEL（渠道）'
    )

    partner_type = MultiChoiceFilter(
        field_name='partner_type',
        help_text='相对方类别，支持多选，逗号分隔。可选值：customer（客户）、supplier（供应商））'
    )

    status = MultiChoiceFilter(
        field_name='status',
        help_text='相对方状态，支持多选，逗号分隔。可选值：ACTIVE（活跃）、SUSPENDED（暂停）、BLACKLISTED（黑名单）、DEACTIVATED（已注销）'
    )

    industry = MultiChoiceFilter(
        field_name='industry',
        help_text='所属行业，支持多选，逗号分隔'
    )

    # 地区过滤
    province_code = django_filters.CharFilter(
        field_name='province_code',
        lookup_expr='exact',
        help_text='省份代码'
    )

    city_code = django_filters.CharFilter(
        field_name='city_code',
        lookup_expr='exact',
        help_text='城市代码'
    )

    # 时间范围过滤
    create_datetime_start = django_filters.DateTimeFilter(
        field_name='create_datetime',
        lookup_expr='gte',
        help_text='创建时间开始，格式：YYYY-MM-DD HH:MM:SS'
    )

    create_datetime_end = django_filters.DateTimeFilter(
        field_name='create_datetime',
        lookup_expr='lte',
        help_text='创建时间结束，格式：YYYY-MM-DD HH:MM:SS'
    )

    # 模糊搜索
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索，支持相对方名称、编码、联系人、电话、纳税人识别号'
    )

    class Meta:
        model = Partner
        fields = {
            'name': ['exact', 'icontains'],
            'code': ['exact', 'icontains'],
            'tax_id': ['exact', 'icontains'],
            'contact_person': ['exact', 'icontains'],
            'contact_phone': ['exact', 'icontains'],
            'contact_email': ['exact', 'icontains'],
            'owner_name': ['exact', 'icontains'],
        }

    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value) |
            models.Q(contact_person__icontains=value) |
            models.Q(contact_phone__icontains=value) |
            models.Q(tax_id__icontains=value)
        )


class CustomFieldFilter(django_filters.FilterSet):
    """自定义字段过滤器"""

    target_model = django_filters.CharFilter(
        field_name='target_model',
        lookup_expr='exact',
        help_text='目标模块，可选值：partner（相对方）、contract（合同）、supplier（供应商）、project（项目）'
    )

    field_type = django_filters.CharFilter(
        field_name='field_type',
        lookup_expr='exact',
        help_text='字段类型，可选值：text（文本）、number（数字）、date（日期）、select（单选）、multiselect（多选）、currency（货币）、boolean（布尔值）'
    )

    is_active = django_filters.BooleanFilter(
        field_name='is_active',
        help_text='是否启用'
    )

    # 模糊搜索
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='模糊搜索，支持字段名称'
    )

    class Meta:
        model = CustomField
        fields = {
            'field_name': ['exact', 'icontains'],
            'create_datetime': ['gte', 'lte'],
            'update_datetime': ['gte', 'lte'],
        }

    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(field_name__icontains=value)
        )


class PaymentPeriodFilter(django_filters.FilterSet):
    """期数过滤器（F003-02）"""

    # 搜索字段
    search = django_filters.CharFilter(
        method='filter_search',
        help_text="搜索期数说明"
    )

    # 状态过滤
    status = django_filters.ChoiceFilter(
        choices=PaymentPeriod.STATUS_CHOICES,
        help_text="期数状态"
    )

    # 时间范围过滤
    due_date_start = django_filters.DateFilter(
        field_name='due_date',
        lookup_expr='gte',
        help_text="计划时间开始（格式：YYYY-MM-DD）"
    )
    due_date_end = django_filters.DateFilter(
        field_name='due_date',
        lookup_expr='lte',
        help_text="计划时间结束（格式：YYYY-MM-DD）"
    )

    # 是否逾期过滤
    is_overdue = django_filters.BooleanFilter(
        method='filter_overdue',
        help_text="是否逾期（true/false）"
    )

    # 金额范围过滤
    amount_min = django_filters.NumberFilter(
        field_name='amount',
        lookup_expr='gte',
        help_text="最小金额"
    )
    amount_max = django_filters.NumberFilter(
        field_name='amount',
        lookup_expr='lte',
        help_text="最大金额"
    )

    class Meta:
        model = PaymentPeriod
        fields = []

    def filter_search(self, queryset, name, value):
        """搜索过滤方法"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(description__icontains=value) |
            models.Q(period__icontains=value)
        )

    def filter_overdue(self, queryset, name, value):
        """逾期过滤方法"""
        if value is None:
            return queryset

        from django.utils import timezone
        current_date = timezone.now().date()

        if value:
            # 查询逾期记录
            return queryset.filter(
                due_date__lt=current_date,
                status='pending'
            )
        else:
            # 查询未逾期记录
            return queryset.filter(
                models.Q(due_date__gte=current_date) |
                models.Q(status='completed')
            )


class ContractAccountFilter(django_filters.FilterSet):
    """合同账款过滤器（F005-05）"""

    # 搜索字段（合同名称、合同编号模糊搜索）
    search = django_filters.CharFilter(
        method='filter_search',
        help_text="搜索合同名称或合同编号"
    )

    # 合同类型过滤
    contract_type = django_filters.ChoiceFilter(
        field_name='category',
        choices=[
            ('sales', '销售合同'),
            ('procurement', '采购合同'),
        ],
        help_text="合同类型"
    )

    # 创建时间范围过滤
    create_date_start = django_filters.DateFilter(
        field_name='create_datetime',
        lookup_expr='date__gte',
        help_text="创建日期开始（格式：YYYY-MM-DD）"
    )
    create_date_end = django_filters.DateFilter(
        field_name='create_datetime',
        lookup_expr='date__lte',
        help_text="创建日期结束（格式：YYYY-MM-DD）"
    )

    # 项目ID过滤
    project_id = django_filters.UUIDFilter(
        field_name='project__id',
        help_text="项目ID"
    )

    # 相对方ID过滤
    partner_id = django_filters.UUIDFilter(
        field_name='partners__id',
        help_text="相对方ID"
    )

    class Meta:
        model = Contract
        fields = []

    def filter_search(self, queryset, name, value):
        """搜索过滤方法"""
        if not value:
            return queryset

        return queryset.filter(
            models.Q(name__icontains=value) |
            models.Q(code__icontains=value)
        ).distinct()

    @property
    def qs(self):
        """重写查询集，添加相关数据预加载"""
        parent = super().qs
        return parent.select_related(
            'project'
        ).prefetch_related(
            'partners',
            'payment_periods'
        )


class InvoiceFilter(django_filters.FilterSet):
    """发票过滤器"""

    # 合同相关过滤
    contract_id = django_filters.UUIDFilter(
        field_name='contract__id',
        help_text='按合同ID筛选'
    )
    contract_name = django_filters.CharFilter(
        field_name='contract__name',
        lookup_expr='icontains',
        help_text='按合同名称模糊搜索'
    )

    # 发票类型过滤
    invoice_type = django_filters.ChoiceFilter(
        choices=Invoice.INVOICE_TYPE_CHOICES,
        help_text='按发票类型筛选（ordinary/special）'
    )

    # 时间范围过滤
    invoice_date_start = django_filters.DateFilter(
        field_name='invoice_date',
        lookup_expr='gte',
        help_text='开票时间范围开始（YYYY-MM-DD）'
    )
    invoice_date_end = django_filters.DateFilter(
        field_name='invoice_date',
        lookup_expr='lte',
        help_text='开票时间范围结束（YYYY-MM-DD）'
    )

    # 金额范围过滤
    amount_min = django_filters.NumberFilter(
        field_name='invoice_amount',
        lookup_expr='gte',
        help_text='开票金额最小值'
    )
    amount_max = django_filters.NumberFilter(
        field_name='invoice_amount',
        lookup_expr='lte',
        help_text='开票金额最大值'
    )

    # 税率范围过滤
    tax_rate_min = django_filters.NumberFilter(
        field_name='tax_rate',
        lookup_expr='gte',
        help_text='税率最小值'
    )
    tax_rate_max = django_filters.NumberFilter(
        field_name='tax_rate',
        lookup_expr='lte',
        help_text='税率最大值'
    )

    # 收付款方过滤
    payee = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='按收款方名称模糊搜索'
    )
    payer = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='按付款方名称模糊搜索'
    )

    # 发票号码过滤
    invoice_number = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='按发票号码模糊搜索'
    )
    invoice_code = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text='按发票代码模糊搜索'
    )

    class Meta:
        model = Invoice
        fields = [
            'contract_id', 'contract_name', 'invoice_type',
            'invoice_date_start', 'invoice_date_end',
            'amount_min', 'amount_max', 'tax_rate_min', 'tax_rate_max',
            'payee', 'payer', 'invoice_number', 'invoice_code'
        ]

    @property
    def qs(self):
        """重写查询集，添加相关数据预加载"""
        parent = super().qs
        return parent.select_related(
            'contract',
            'source_file'
        ).prefetch_related(
            'contract__partners'
        )


class ContractDocumentFilter(django_filters.FilterSet):
    """合同文档管理过滤器"""

    # 搜索
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='按合同名称、编号模糊搜索'
    )

    # 合同类别过滤
    category = django_filters.ChoiceFilter(
        choices=Contract.CATEGORY_CHOICES,
        help_text='按合同类别筛选（sales/procurement）'
    )

    # 签订状态过滤
    sign_status = django_filters.ChoiceFilter(
        choices=Contract.SIGN_STATUS_CHOICES,
        help_text='按签订状态筛选'
    )

    # 履约状态过滤
    performance_status = django_filters.ChoiceFilter(
        choices=Contract.PERFORMANCE_STATUS_CHOICES,
        help_text='按履约状态筛选'
    )

    # 项目过滤
    project_id = django_filters.UUIDFilter(
        field_name='project__id',
        help_text='按项目ID筛选'
    )

    # 相对方过滤
    partner_id = django_filters.UUIDFilter(
        field_name='partners__id',
        help_text='按相对方ID筛选'
    )

    class Meta:
        model = Contract
        fields = ['category', 'sign_status', 'performance_status', 'project_id', 'partner_id']

    def filter_search(self, queryset, name, value):
        """搜索过滤"""
        if value:
            return queryset.filter(
                models.Q(name__icontains=value) |
                models.Q(code__icontains=value)
            )
        return queryset

    @property
    def qs(self):
        """重写查询集，添加相关数据预加载"""
        parent = super().qs
        return parent.select_related(
            'project'
        ).prefetch_related(
            'partners'
        )
