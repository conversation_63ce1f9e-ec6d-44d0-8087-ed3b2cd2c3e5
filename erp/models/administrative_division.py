from django.db import models
from .base import CoreModel


class AdministrativeDivision(CoreModel):
    """行政区划模型"""
    
    # 区划级别
    LEVEL_CHOICES = [
        ('province', '省/直辖市/自治区'),
        ('city', '市/区/县'),
        ('district', '区/县'),
    ]
    
    code = models.CharField(max_length=6, unique=True, verbose_name="行政区划代码", help_text="6位数字代码")
    name = models.CharField(max_length=100, verbose_name="区划名称")
    level = models.CharField(max_length=10, choices=LEVEL_CHOICES, verbose_name="区划级别")
    parent_code = models.CharField(max_length=6, null=True, blank=True, verbose_name="上级区划代码")
    
    # 添加索引以提高查询性能
    class Meta:
        db_table = 'erp_administrative_division'
        verbose_name = '行政区划'
        verbose_name_plural = '行政区划'
        ordering = ['code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['parent_code']),
            models.Index(fields=['level']),
            models.Index(fields=['name']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def is_province(self):
        """是否为省级"""
        return self.level == 'province'
    
    @property
    def is_city(self):
        """是否为市级"""
        return self.level == 'city'
    
    @property
    def is_district(self):
        """是否为区县级"""
        return self.level == 'district'
    
    def get_children(self):
        """获取下级区划"""
        return AdministrativeDivision.objects.filter(parent_code=self.code).order_by('code')
    
    def get_parent(self):
        """获取上级区划"""
        if self.parent_code:
            try:
                return AdministrativeDivision.objects.get(code=self.parent_code)
            except AdministrativeDivision.DoesNotExist:
                return None
        return None
    
    @classmethod
    def get_provinces(cls):
        """获取所有省份"""
        return cls.objects.filter(level='province').order_by('code')
    
    @classmethod
    def get_cities_by_province(cls, province_code):
        """根据省份代码获取城市列表"""
        return cls.objects.filter(
            parent_code=province_code,
            level='city'
        ).order_by('code')
    
    @classmethod
    def get_districts_by_city(cls, city_code):
        """根据城市代码获取区县列表"""
        return cls.objects.filter(
            parent_code=city_code,
            level='district'
        ).order_by('code')
    
    def save(self, *args, **kwargs):
        """保存时自动判断级别"""
        if not self.level:
            # 根据代码规则自动判断级别
            if self.code.endswith('0000'):
                self.level = 'province'
                self.parent_code = None
            elif self.code.endswith('00'):
                self.level = 'city'
                self.parent_code = self.code[:2] + '0000'
            else:
                self.level = 'district'
                self.parent_code = self.code[:4] + '00'
        
        super().save(*args, **kwargs)
