import json
from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel


class CustomField(CoreModel, SoftDeleteDatetimeModel):
    """自定义字段定义模型"""

    FIELD_TYPE_CHOICES = [
        ('text', '文本'),
        ('number', '数字'),
        ('date', '日期'),
        ('select', '单选'),
        ('multiselect', '多选'),
        ('currency', '货币'),
        ('boolean', '布尔值'),
    ]

    TARGET_MODEL_CHOICES = [
        ('partner', '相对方'),
        ('project', '项目'),
        ('contract', '合同'),
        ('invoice', '发票'),
    ]

    field_name = models.CharField(max_length=200, verbose_name="字段显示名称")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPE_CHOICES, verbose_name="字段类型")
    target_model = models.CharField(max_length=50, choices=TARGET_MODEL_CHOICES, verbose_name="目标模块")
    field_options = models.TextField(null=True, blank=True, verbose_name="选项值")  # JSON格式
    is_active = models.BooleanField(default=True, verbose_name="是否启用")

    class Meta:
        db_table = 'erp_custom_field'
        verbose_name = '自定义字段'
        verbose_name_plural = '自定义字段'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['target_model', 'is_active']),
            models.Index(fields=['field_name', 'target_model']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['field_name', 'target_model'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_field_name_per_model'
            )
        ]

    def __str__(self):
        return f"{self.get_target_model_display()} - {self.field_name}"

    @property
    def field_key(self):
        """生成字段键名，用于数据存储和查询"""
        return f"custom_field_{self.id.hex[:8]}"

    def clean(self):
        """模型验证"""
        super().clean()
        
        # 验证选项值格式（单选/多选类型必须有选项）
        if self.field_type in ['select', 'multiselect']:
            if not self.field_options:
                raise ValidationError("单选和多选类型必须设置选项值")

            # 支持两种格式：
            # 1. 字符串数组 ['选项1', '选项2']
            # 2. 逗号分隔字符串 '选项1,选项2'

            # 首先尝试作为JSON数组解析
            try:
                options = json.loads(self.field_options)
                if isinstance(options, list):
                    # 格式1: 字符串数组
                    if len(options) == 0:
                        raise ValidationError("选项值不能为空")

                    for option in options:
                        if not isinstance(option, str) or not option.strip():
                            raise ValidationError("选项值必须是非空字符串")
                else:
                    raise ValidationError("JSON格式的选项值必须是字符串数组")

            except json.JSONDecodeError:
                # 格式2: 逗号分隔字符串
                if isinstance(self.field_options, str):
                    options = [opt.strip() for opt in self.field_options.split(',')]
                    options = [opt for opt in options if opt]  # 过滤空字符串

                    if len(options) == 0:
                        raise ValidationError("选项值不能为空")

                    # 将逗号分隔字符串转换为JSON数组格式存储
                    self.field_options = json.dumps(options, ensure_ascii=False)
                else:
                    raise ValidationError("选项值格式错误，支持格式：1. 字符串数组 ['选项1', '选项2'] 2. 逗号分隔字符串 '选项1,选项2'")
        else:
            # 非选择类型不应该有选项值
            if self.field_options:
                self.field_options = None

    def save(self, *args, **kwargs):
        """保存前验证"""
        self.full_clean()
        super().save(*args, **kwargs)

    def get_options_list(self):
        """获取选项列表"""
        if self.field_options:
            try:
                return json.loads(self.field_options)
            except json.JSONDecodeError:
                return []
        return []

    def set_options(self, options_list):
        """设置选项列表"""
        if not isinstance(options_list, list):
            raise ValidationError("选项必须是列表格式")

        # 验证选项内容
        for option in options_list:
            if not isinstance(option, str):
                raise ValidationError("选项值必须是字符串")
            if not option.strip():
                raise ValidationError("选项值不能为空")

        # 去重并保持顺序
        unique_options = []
        for option in options_list:
            option = option.strip()
            if option not in unique_options:
                unique_options.append(option)

        # 保存为JSON格式
        self.field_options = json.dumps(unique_options, ensure_ascii=False)

    def validate_value(self, value):
        """验证字段值"""
        if value is None:
            return True
            
        if self.field_type == 'text':
            return isinstance(value, str)
        elif self.field_type == 'number':
            try:
                float(value)
                return True
            except (ValueError, TypeError):
                return False
        elif self.field_type == 'currency':
            try:
                float(value)
                return float(value) >= 0  # 货币不能为负数
            except (ValueError, TypeError):
                return False
        elif self.field_type == 'date':
            from datetime import datetime
            if isinstance(value, str):
                try:
                    datetime.strptime(value, '%Y-%m-%d')
                    return True
                except ValueError:
                    return False
            return hasattr(value, 'year')  # 日期对象
        elif self.field_type == 'boolean':
            return isinstance(value, bool)
        elif self.field_type == 'select':
            # 单选值必须在选项中
            options = self.get_options_list()
            return value in options
        elif self.field_type == 'multiselect':
            # 多选值必须是逗号分隔的字符串，且每个值都在选项中
            if isinstance(value, str):
                selected_values = [v.strip() for v in value.split(',') if v.strip()]
            elif isinstance(value, list):
                selected_values = value
            else:
                return False
            options = self.get_options_list()
            return all(v in options for v in selected_values)
        
        return False


class CustomFieldValue(CoreModel, SoftDeleteDatetimeModel):
    """自定义字段值模型"""

    custom_field = models.ForeignKey(CustomField, on_delete=models.CASCADE, verbose_name="自定义字段")
    object_id = models.UUIDField(verbose_name="对象ID")

    # 多类型值存储
    text_value = models.TextField(null=True, blank=True, verbose_name="文本值")
    number_value = models.DecimalField(max_digits=20, decimal_places=6, null=True, blank=True, verbose_name="数字值")
    date_value = models.DateField(null=True, blank=True, verbose_name="日期值")
    boolean_value = models.BooleanField(null=True, blank=True, verbose_name="布尔值")
    json_value = models.TextField(null=True, blank=True, verbose_name="JSON值")

    class Meta:
        db_table = 'erp_custom_field_value'
        verbose_name = '自定义字段值'
        verbose_name_plural = '自定义字段值'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['object_id']),
            models.Index(fields=['custom_field', 'object_id']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['custom_field', 'object_id'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_field_value_per_object'
            )
        ]

    def __str__(self):
        return f"{self.custom_field.field_name} - {self.object_id}"

    def get_value(self):
        """根据字段类型返回对应的值"""
        field_type = self.custom_field.field_type
        if field_type == 'text':
            return self.text_value
        elif field_type in ['number', 'currency']:
            return self.number_value
        elif field_type == 'date':
            return self.date_value
        elif field_type == 'boolean':
            return self.boolean_value
        elif field_type in ['select', 'multiselect']:
            if self.json_value:
                try:
                    return json.loads(self.json_value)
                except json.JSONDecodeError:
                    return None
            return None
        return None

    def set_value(self, value):
        """根据字段类型设置对应的值"""
        # 验证值的有效性
        if not self.custom_field.validate_value(value):
            raise ValidationError(f"字段 {self.custom_field.field_name} 的值格式不正确")

        field_type = self.custom_field.field_type

        # 清空所有值
        self.text_value = None
        self.number_value = None
        self.date_value = None
        self.boolean_value = None
        self.json_value = None

        # 设置对应类型的值
        if value is not None:
            if field_type == 'text':
                self.text_value = str(value)
            elif field_type in ['number', 'currency']:
                self.number_value = float(value)
            elif field_type == 'date':
                if isinstance(value, str):
                    from datetime import datetime
                    self.date_value = datetime.strptime(value, '%Y-%m-%d').date()
                else:
                    self.date_value = value
            elif field_type == 'boolean':
                self.boolean_value = bool(value)
            elif field_type in ['select', 'multiselect']:
                self.json_value = json.dumps(value)

    def clean(self):
        """模型验证"""
        super().clean()
        
        # 验证字段值
        current_value = self.get_value()
        if current_value is not None and not self.custom_field.validate_value(current_value):
            raise ValidationError(f"字段 {self.custom_field.field_name} 的值格式不正确")

    def save(self, *args, **kwargs):
        """保存前验证"""
        self.full_clean()
        super().save(*args, **kwargs)

    @classmethod
    def get_values_for_object(cls, object_id, target_model):
        """获取指定对象的所有自定义字段值（包括空值）"""
        # 获取所有启用的自定义字段定义
        all_fields = CustomField.objects.filter(
            target_model=target_model,
            is_active=True,
            delete_datetime__isnull=True
        )

        # 获取已有的字段值
        existing_values = cls.objects.filter(
            object_id=object_id,
            custom_field__target_model=target_model,
            custom_field__is_active=True,
            delete_datetime__isnull=True
        ).select_related('custom_field')

        # 创建字段值映射
        value_map = {}
        for value in existing_values:
            value_map[value.custom_field.id] = value

        # 构建结果，包含所有字段（有值的和空值的）
        result = {}
        for field in all_fields:
            if field.id in value_map:
                # 有值的字段
                value_obj = value_map[field.id]
                result[field.field_name] = {
                    'field_id': str(field.id),
                    'field_name': field.field_name,
                    'field_type': field.field_type,
                    'value': value_obj.get_value()
                }
            else:
                # 空值的字段
                result[field.field_name] = {
                    'field_id': str(field.id),
                    'field_name': field.field_name,
                    'field_type': field.field_type,
                    'value': None
                }

        return result

    @classmethod
    def set_values_for_object(cls, object_id, target_model, custom_fields_data, creator=None):
        """为指定对象设置自定义字段值"""
        if not custom_fields_data:
            return

        # 支持两种格式：列表格式和字典格式（向后兼容）
        if isinstance(custom_fields_data, list):
            # 新格式：[{"field_name": "字段名称", "value": "字段值"}]
            for field_data in custom_fields_data:
                field_name = field_data.get('field_name')
                value = field_data.get('value')

                if not field_name:
                    continue

                try:
                    # 根据字段名称获取自定义字段定义
                    custom_field = CustomField.objects.get(
                        field_name=field_name,
                        target_model=target_model,
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    cls._set_field_value(custom_field, object_id, value, creator)

                except CustomField.DoesNotExist:
                    continue  # 忽略不存在的字段
                except Exception:
                    continue  # 忽略设置失败的字段
        else:
            # 旧格式：{field_id: value}（向后兼容）
            for field_id, value in custom_fields_data.items():
                try:
                    # 获取自定义字段定义
                    custom_field = CustomField.objects.get(
                        id=field_id,
                        target_model=target_model,
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    cls._set_field_value(custom_field, object_id, value, creator)

                except CustomField.DoesNotExist:
                    continue  # 忽略不存在的字段
                except Exception:
                    continue  # 忽略设置失败的字段

    @classmethod
    def _set_field_value(cls, custom_field, object_id, value, creator=None):
        """设置单个字段值"""
        # 获取或创建字段值记录
        field_value, created = cls.objects.get_or_create(
            custom_field=custom_field,
            object_id=object_id,
            delete_datetime__isnull=True,
            defaults={'creator': creator}
        )

        # 设置值
        field_value.set_value(value)
        if not created and creator:
            field_value.updater = creator
        field_value.save()

    @classmethod
    def delete_values_for_object(cls, object_id):
        """删除指定对象的所有自定义字段值（软删除）"""
        cls.objects.filter(
            object_id=object_id,
            delete_datetime__isnull=True
        ).update(delete_datetime=models.functions.Now())
