from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel
from .contract import Contract


class PaymentPeriod(CoreModel, SoftDeleteDatetimeModel):
    """付款/收款期数模型"""

    # 状态选择 - 根据合同类型动态显示
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('completed', '已完成'),
    ]

    # 基本信息
    contract = models.ForeignKey(
        Contract,
        on_delete=models.PROTECT,
        related_name='payment_periods',
        verbose_name="关联合同"
    )
    
    # 期数信息
    period = models.PositiveIntegerField(verbose_name="期数", help_text="第几期")
    
    # 金额信息
    amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        verbose_name="期数金额",
        help_text="本期应收/应付金额"
    )
    
    # 时间信息
    date = models.DateField(verbose_name="日期", help_text="收付款日期")
    
    # 状态信息
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="状态"
    )
    
    # 说明信息
    description = models.TextField(
        null=True, 
        blank=True, 
        verbose_name="说明",
        help_text="期数说明或备注"
    )

    class Meta:
        db_table = 'erp_payment_period'
        verbose_name = '付款期数'
        verbose_name_plural = '付款期数'
        ordering = ['contract', 'period']
        indexes = [
            models.Index(fields=['contract']),
            models.Index(fields=['status']),
            models.Index(fields=['date']),
            models.Index(fields=['create_datetime']),
        ]
        constraints = [
            # 确保同一合同的期数唯一
            models.UniqueConstraint(
                fields=['contract', 'period'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_payment_period_contract_period_when_not_deleted'
            )
        ]

    def __str__(self):
        return f"{self.contract.name} - 第{self.period}期"

    def save(self, *args, **kwargs):
        """保存时验证数据"""
        self._validate_data()
        super().save(*args, **kwargs)

    def _validate_data(self):
        """验证数据逻辑"""
        if self.amount <= 0:
            raise ValidationError("期数金额必须大于0")
        
        if self.period <= 0:
            raise ValidationError("期数必须大于0")

    @property
    def status_display_by_contract_type(self):
        """根据合同类型返回状态显示名称"""
        if self.contract.category == 'sales':
            # 销售合同：已完成/待完成
            if self.status == 'completed':
                return '已完成'
            else:
                return '待完成'
        elif self.contract.category == 'procurement':
            # 采购合同：已付款/待付款
            if self.status == 'completed':
                return '已付款'
            else:
                return '待付款'
        else:
            return self.get_status_display()

    def mark_completed(self):
        """标记为已完成"""
        self.status = 'completed'
        self.save()

    def mark_pending(self):
        """标记为待处理"""
        self.status = 'pending'
        self.save()

    @classmethod
    def get_periods_by_contract(cls, contract_id):
        """获取指定合同的所有期数"""
        return cls.objects.filter(
            contract_id=contract_id,
            delete_datetime__isnull=True
        ).order_by('period')

    @classmethod
    def get_pending_periods(cls, contract_id=None):
        """获取待处理的期数"""
        queryset = cls.objects.filter(
            status='pending',
            delete_datetime__isnull=True
        )
        if contract_id:
            queryset = queryset.filter(contract_id=contract_id)
        return queryset.order_by('date')

    @classmethod
    def get_next_period_number(cls, contract_id):
        """获取下一个期数编号"""
        max_period = cls.objects.filter(
            contract_id=contract_id,
            delete_datetime__isnull=True
        ).aggregate(
            max_period=models.Max('period')
        )['max_period']
        
        return (max_period or 0) + 1

    @classmethod
    def calculate_contract_summary(cls, contract_id):
        """计算合同的账款汇总信息"""
        periods = cls.get_periods_by_contract(contract_id)
        
        total_amount = sum(p.amount for p in periods)
        completed_amount = sum(p.amount for p in periods if p.status == 'completed')
        pending_amount = total_amount - completed_amount
        
        completed_count = len([p for p in periods if p.status == 'completed'])
        pending_count = len(periods) - completed_count
        
        # 计算进度百分比
        progress = (completed_amount / total_amount * 100) if total_amount > 0 else 0
        
        # 获取下期信息
        next_period = periods.filter(status='pending').order_by('date').first()
        next_amount = next_period.amount if next_period else 0
        next_date = next_period.date if next_period else None
        
        return {
            'total_amount': total_amount,
            'completed_amount': completed_amount,
            'pending_amount': pending_amount,
            'completed_count': completed_count,
            'pending_count': pending_count,
            'progress': round(progress, 2),
            'next_amount': next_amount,
            'next_date': next_date,
        }
