from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel


class Supplier(CoreModel, SoftDeleteDatetimeModel):
    """供应商模型"""

    # 供应商类型选择
    SUPPLIER_TYPE_CHOICES = [
        ('ORIGINAL', '原厂'),
        ('CHANNEL', '渠道'),
    ]

    # 供应商专用行业分类选择
    INDUSTRY_TYPE_CHOICES = [
        ('HARDWARE', '硬件产品供应商'),
        ('SOFTWARE', '软件产品供应商'),
        ('INTEGRATION', '集成/服务供应商'),
    ]

    # 供应商状态选择
    SUPPLIER_STATUS_CHOICES = [
        ('ACTIVE', '活跃'),
        ('SUSPENDED', '暂停'),
        ('BLACKLISTED', '黑名单'),
    ]

    # 基本信息
    code = models.CharField(max_length=50, verbose_name="供应商编码")
    name = models.CharField(max_length=200, verbose_name="供应商名称")
    tax_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="纳税人识别号")
    type = models.CharField(max_length=20, choices=SUPPLIER_TYPE_CHOICES, default='ORIGINAL', verbose_name="供应商类型")
    status = models.CharField(max_length=20, choices=SUPPLIER_STATUS_CHOICES, default='ACTIVE', verbose_name="合作状态")
    industry = models.CharField(max_length=20, choices=INDUSTRY_TYPE_CHOICES, default='HARDWARE', verbose_name="所属行业")

    # 地区信息（代码+名称冗余存储）
    province_code = models.CharField(max_length=6, null=True, blank=True, verbose_name="省份代码", help_text="省级行政区划代码")
    province = models.CharField(max_length=50, null=True, blank=True, verbose_name="省份名称", help_text="省份名称，冗余存储")
    city_code = models.CharField(max_length=6, null=True, blank=True, verbose_name="城市代码", help_text="市级行政区划代码")
    city = models.CharField(max_length=50, null=True, blank=True, verbose_name="城市名称", help_text="城市名称，冗余存储")

    # 联系信息
    contact_person = models.CharField(max_length=100, verbose_name="联系人")
    phone = models.CharField(max_length=20, verbose_name="联系电话")
    email = models.EmailField(null=True, blank=True, verbose_name="电子邮箱")
    address = models.TextField(null=True, blank=True, verbose_name="联系地址")
    contact_remark = models.TextField(null=True, blank=True, verbose_name="联系备注")
    website = models.URLField(null=True, blank=True, verbose_name="官网地址")

    # 业务信息
    owner_name = models.CharField(max_length=100, verbose_name="负责人姓名")
    owner_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="负责人ID")
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    # 开票信息
    invoice_bank = models.CharField(max_length=100, null=True, blank=True, verbose_name="开户银行")
    invoice_bank_account = models.CharField(max_length=50, null=True, blank=True, verbose_name="银行账号")
    invoice_address = models.TextField(null=True, blank=True, verbose_name="开票地址")
    invoice_phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="开票电话")
    invoice_remark = models.TextField(null=True, blank=True, verbose_name="开票备注")

    class Meta:
        db_table = 'erp_supplier'
        verbose_name = '供应商'
        verbose_name_plural = '供应商'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['create_datetime']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        """保存时自动生成供应商编码"""
        # 从kwargs中提取自定义日期参数
        custom_date = kwargs.pop('code_date', None)

        if not self.code:
            # 生成供应商编码：V + 年月日 + 3位序号
            from django.utils import timezone
            from datetime import datetime

            # 使用自定义日期或当前日期
            if custom_date:
                if isinstance(custom_date, str):
                    # 如果是字符串，尝试解析为日期
                    try:
                        date_obj = datetime.strptime(custom_date, '%Y-%m-%d')
                    except ValueError:
                        raise ValueError(f"日期格式错误，请使用YYYY-MM-DD格式: {custom_date}")
                elif isinstance(custom_date, datetime):
                    date_obj = custom_date
                elif hasattr(custom_date, 'strftime'):
                    # 支持 datetime.date 类型
                    date_obj = datetime.combine(custom_date, datetime.min.time())
                else:
                    raise ValueError(f"不支持的日期类型: {type(custom_date)}")
                today = date_obj.strftime('%y%m%d')
            else:
                today = timezone.now().strftime('%y%m%d')

            code_prefix = f'V{today}'

            # 查找当天最大的序号（只查询未删除的记录）
            last_supplier = Supplier.objects.filter(
                code__startswith=code_prefix,
                delete_datetime__isnull=True  # 只查询未删除的记录
            ).order_by('-code').first()

            if last_supplier:
                last_seq = int(last_supplier.code[-3:])
                new_seq = last_seq + 1
            else:
                new_seq = 1

            self.code = f'{code_prefix}{new_seq:03d}'

        # 验证所有唯一性约束
        self._validate_name_uniqueness()
        self._validate_code_uniqueness()
        self._validate_tax_id_uniqueness()

        super().save(*args, **kwargs)

    def _validate_code_uniqueness(self):
        """验证供应商编码在未删除记录中的唯一性"""
        existing = Supplier.objects.filter(
            code=self.code,
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"供应商编码 {self.code} 已存在")

    def _validate_name_uniqueness(self):
        """验证供应商名称在未删除记录中的唯一性"""
        if not self.name:
            raise ValidationError("供应商名称不能为空")

        existing = Supplier.objects.filter(
            name=self.name.strip(),
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"供应商名称 '{self.name}' 已存在")

    def _validate_tax_id_uniqueness(self):
        """验证税号在未删除记录中的唯一性"""
        if self.tax_id and self.tax_id.strip():
            existing = Supplier.objects.filter(
                tax_id=self.tax_id.strip(),
                delete_datetime__isnull=True  # 只检查未删除的记录
            ).exclude(id=self.id)  # 排除自己

            if existing.exists():
                raise ValidationError(f"税号 {self.tax_id} 已存在")

    @property
    def display_name(self):
        """显示名称"""
        return f"{self.name}({self.code})"

    def get_active_projects_count(self):
        """获取活跃项目数量"""
        # 这里预留接口，等项目模型创建后实现
        return 0

    def get_total_contract_amount(self):
        """获取采购合同总金额"""
        # 这里预留接口，等采购合同模型创建后实现
        return 0

    def get_default_payment_info(self):
        """获取默认收款信息"""
        return self.payment_infos.filter(
            is_default=True,
            delete_datetime__isnull=True
        ).first()


class SupplierPaymentInfo(CoreModel, SoftDeleteDatetimeModel):
    """供应商收款信息模型"""

    # 关联信息
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='payment_infos',
        verbose_name="供应商"
    )

    # 账户信息
    bank_name = models.CharField(max_length=100, verbose_name="开户银行")
    account_number = models.CharField(max_length=50, verbose_name="银行账号")

    # 管理信息
    is_default = models.BooleanField(default=False, verbose_name="是否默认账户")
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    class Meta:
        db_table = 'erp_supplier_payment_info'
        verbose_name = '供应商收款信息'
        verbose_name_plural = '供应商收款信息'
        ordering = ['-is_default', '-create_datetime']
        indexes = [
            models.Index(fields=['supplier', 'is_default']),
        ]
        constraints = [
            # 确保每个供应商只有一个默认账户
            models.UniqueConstraint(
                fields=['supplier'],
                condition=models.Q(is_default=True, delete_datetime__isnull=True),
                name='unique_default_payment_per_supplier'
            )
        ]

    def __str__(self):
        return f"{self.supplier.name} - {self.bank_name} - {self.account_number}"

    def save(self, *args, **kwargs):
        """保存时处理默认账户逻辑"""
        if self.is_default:
            # 如果设置为默认账户，将同一供应商的其他账户设为非默认
            SupplierPaymentInfo.objects.filter(
                supplier=self.supplier,
                delete_datetime__isnull=True
            ).exclude(id=self.id).update(is_default=False)

        super().save(*args, **kwargs)
