from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel
from .partner import Partner


class Project(CoreModel, SoftDeleteDatetimeModel):
    """项目模型"""

    # 项目类型选择（基于前端定义）
    PROJECT_TYPE_CHOICES = [
        ('system_integration', '系统集成'),
        ('software_development', '软件开发'),
        ('product_own', '产品（自有）'),
        ('product_external', '产品（外采）'),
        ('service_own', '服务（自有）'),
        ('service_external', '服务（外采）'),
        ('other', '其他（行政、租房、人事等）'),
    ]

    # 项目状态选择（基于前端定义）
    PROJECT_STATUS_CHOICES = [
        ('preparing', '准备中'),
        ('in_progress', '进行中'),
        ('paused', '暂停'),
        ('completed', '已完成'),
        ('terminated', '已终止'),
    ]

    # 立项理由选择（基于前端定义）
    PROJECT_REASON_CHOICES = [
        ('bidding', '投标'),
        ('signed', '签约'),
        ('poc', '概念验证（POC）'),
    ]

    # 基础信息字段
    code = models.CharField(max_length=50, verbose_name="项目编号")
    name = models.CharField(max_length=200, verbose_name="项目名称")
    description = models.TextField(null=True, blank=True, verbose_name="项目描述")
    type = models.CharField(max_length=30, choices=PROJECT_TYPE_CHOICES, verbose_name="项目类型")
    status = models.CharField(max_length=20, choices=PROJECT_STATUS_CHOICES, default='preparing', verbose_name="项目状态")
    reason = models.CharField(max_length=20, choices=PROJECT_REASON_CHOICES, verbose_name="立项理由")

    # 客户关联字段（现在指向Partner模型）
    customer = models.ForeignKey(
        Partner,
        on_delete=models.PROTECT,
        related_name='projects',
        verbose_name="关联客户",
        limit_choices_to={'type__in': ['C', 'G']}  # 限制只能选择客户类型的相对方
    )
    customer_code = models.CharField(max_length=50, verbose_name="客户编号")  # 冗余字段
    customer_name = models.CharField(max_length=200, verbose_name="客户名称")  # 冗余字段

    # 最终用户信息字段
    end_user_name = models.CharField(max_length=200, verbose_name="最终用户名称")
    end_user_contact = models.CharField(max_length=100, null=True, blank=True, verbose_name="最终用户联系人")
    end_user_phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="最终用户电话")
    end_user_address = models.TextField(null=True, blank=True, verbose_name="最终用户地址")

    # 项目管理字段
    sales_manager_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="销售负责人ID")  # 暂时用字符串，后续可改为外键
    sales_manager_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="销售负责人姓名")

    # 时间相关字段
    start_date = models.DateField(verbose_name="项目开始日期")
    end_date = models.DateField(verbose_name="项目结束日期")
    completion_datetime = models.DateTimeField(null=True, blank=True, verbose_name="项目完成时间", help_text="项目实际完成时间，通过完成接口设置")
    completion_remark = models.TextField(null=True, blank=True, verbose_name="完成备注", help_text="项目完成时的备注信息")

    # 财务相关字段
    budget = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        null=True, 
        blank=True, 
        verbose_name="项目预算"
    )
    expected_profit_rate = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        verbose_name="预计毛利率",
        help_text="百分比，如25.50表示25.5%"
    )
    progress = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name="项目进度",
        help_text="百分比，0-100，支持小数如25.50"
    )

    class Meta:
        db_table = 'erp_project'
        verbose_name = '项目'
        verbose_name_plural = '项目'
        ordering = ['-create_datetime']
        constraints = [
            # 项目编号在未删除记录中唯一
            models.UniqueConstraint(
                fields=['code'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_project_code_when_not_deleted'
            ),
        ]
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['customer']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
            models.Index(fields=['create_datetime']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        """保存时自动生成项目编码和填充客户信息"""
        if not self.code:
            # 生成项目编码：P + 3位全局序号 + "-" + 客户编号
            # 例如：P001-C250606001, P002-G250607002

            # 查找全局最大的项目序号（只查询未删除的记录）
            # 使用Python代码处理，避免数据库特定语法
            all_projects = Project.objects.filter(
                delete_datetime__isnull=True,
                code__startswith='P'
            ).values_list('code', flat=True)

            max_seq = 0
            for code in all_projects:
                if code and len(code) >= 4 and code[0] == 'P':
                    try:
                        # 提取P后面到第一个"-"之间的数字
                        dash_pos = code.find('-')
                        if dash_pos > 1:
                            seq_str = code[1:dash_pos]
                            if seq_str.isdigit():
                                seq_num = int(seq_str)
                                max_seq = max(max_seq, seq_num)
                    except (ValueError, IndexError):
                        continue

            new_seq = max_seq + 1

            # 获取客户编号
            customer_code = self.customer.code if self.customer_id else "UNKNOWN"

            self.code = f'P{new_seq:03d}-{customer_code}'

        # 自动填充客户冗余信息
        if self.customer_id:
            self.customer_code = self.customer.code
            self.customer_name = self.customer.name

        # 验证数据
        self._validate_dates()
        self._validate_progress()
        self._validate_profit_rate()
        self._validate_name_uniqueness()

        super().save(*args, **kwargs)

    def _validate_dates(self):
        """验证日期逻辑"""
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError("项目开始日期不能晚于结束日期")

    def _validate_progress(self):
        """验证进度范围"""
        if self.progress < 0 or self.progress > 100:
            raise ValidationError("项目进度必须在0-100之间")

    def _validate_profit_rate(self):
        """验证毛利率范围"""
        if self.expected_profit_rate < 0 or self.expected_profit_rate > 100:
            raise ValidationError("预计毛利率必须在0-100之间")

    def _validate_name_uniqueness(self):
        """验证项目名称在未删除记录中的唯一性"""
        existing = Project.objects.filter(
            name=self.name,
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"项目名称 {self.name} 已存在")

    @property
    def display_name(self):
        """显示名称"""
        return f"{self.name}({self.code})"

    @property
    def duration_days(self):
        """项目持续天数"""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0

    @property
    def is_overdue(self):
        """是否逾期"""
        from django.utils import timezone
        if self.end_date and self.status not in ['completed', 'terminated']:
            return timezone.now().date() > self.end_date
        return False

    @property
    def status_display_with_overdue(self):
        """带逾期标识的状态显示"""
        status_display = self.get_status_display()
        if self.is_overdue:
            return f"{status_display}（逾期）"
        return status_display

    def get_task_count(self):
        """获取任务数量"""
        # 预留接口，等任务模型创建后实现
        return 0

    def get_milestone_count(self):
        """获取里程碑数量"""
        # 预留接口，等里程碑模型创建后实现
        return 0

    def get_member_count(self):
        """获取项目成员数量"""
        # 预留接口，等项目成员模型创建后实现
        return 0
