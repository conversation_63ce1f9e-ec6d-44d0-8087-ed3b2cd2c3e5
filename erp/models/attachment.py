from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel
import os


class Attachment(CoreModel, SoftDeleteDatetimeModel):
    """附件模型"""

    # 附件类别选择
    CATEGORY_CHOICES = [
        # 项目附件类别
        ('project_trial_balance', '试算表'),
        ('project_other', '项目其他'),

        # 合同附件类别
        ('contract_signed_scan', '合同双章扫描件'),
        ('contract_final_word', '合同终稿word版'),
        ('sales_invoice_scan', '销售发票扫描件'),
        ('receipt_confirmation', '签收单'),
        ('acceptance_report', '验收报告'),
        ('purchase_invoice_scan', '采购发票扫描件'),
        ('contract_other', '合同其他'),

        # 发票附件类别
        ('invoice_source', '发票源文件'),
    ]

    # 文件基本信息
    original_name = models.CharField(max_length=255, verbose_name="原始文件名")
    file_name = models.CharField(max_length=255, verbose_name="存储文件名", help_text="UUID+扩展名")
    file_path = models.CharField(max_length=500, verbose_name="文件存储路径")
    file_size = models.BigIntegerField(verbose_name="文件大小", help_text="单位：字节")
    file_type = models.CharField(max_length=100, null=True, blank=True, verbose_name="MIME类型")
    file_extension = models.CharField(max_length=10, null=True, blank=True, verbose_name="文件扩展名")
    file_md5 = models.CharField(max_length=32, null=True, blank=True, verbose_name="文件MD5值", help_text="用于文件去重")

    # 通用外键关联（支持关联到任何模型）
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        verbose_name="关联模型类型"
    )
    object_id = models.UUIDField(verbose_name="关联对象ID")
    content_object = GenericForeignKey('content_type', 'object_id')

    # 业务信息
    category = models.CharField(
        max_length=50,
        choices=CATEGORY_CHOICES,
        verbose_name="附件类别",
        help_text="附件分类，根据关联对象类型选择相应的类型"
    )
    description = models.TextField(null=True, blank=True, verbose_name="附件描述")

    class Meta:
        db_table = 'erp_attachment'
        verbose_name = '附件'
        verbose_name_plural = '附件'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['file_md5']),
            models.Index(fields=['create_datetime']),
            models.Index(fields=['category']),
        ]

    def __str__(self):
        return f"{self.original_name}"

    def save(self, *args, **kwargs):
        """保存时进行验证"""
        self._validate_file_info()
        super().save(*args, **kwargs)

    def _validate_file_info(self):
        """验证文件信息"""
        # 验证文件大小（限制为200MB）
        max_size = 200 * 1024 * 1024  # 200MB
        if self.file_size > max_size:
            raise ValidationError(f"文件大小不能超过{max_size // (1024*1024)}MB")

        # 验证文件扩展名
        if self.file_extension:
            allowed_extensions = [
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                '.txt', '.rtf', '.zip', '.rar', '.7z',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp',
                '.mp4', '.avi', '.mov', '.wmv'
            ]
            if self.file_extension.lower() not in allowed_extensions:
                raise ValidationError(f"不支持的文件类型: {self.file_extension}")

    @property
    def file_size_formatted(self):
        """格式化文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    @property
    def is_image(self):
        """判断是否为图片文件"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        return self.file_extension and self.file_extension.lower() in image_extensions

    @property
    def is_document(self):
        """判断是否为文档文件"""
        doc_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']
        return self.file_extension and self.file_extension.lower() in doc_extensions

    @property
    def is_archive(self):
        """判断是否为压缩文件"""
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz']
        return self.file_extension and self.file_extension.lower() in archive_extensions

    def get_file_icon(self):
        """获取文件图标类型"""
        if self.is_image:
            return 'image'
        elif self.is_document:
            if self.file_extension.lower() == '.pdf':
                return 'pdf'
            elif self.file_extension.lower() in ['.doc', '.docx']:
                return 'word'
            elif self.file_extension.lower() in ['.xls', '.xlsx']:
                return 'excel'
            elif self.file_extension.lower() in ['.ppt', '.pptx']:
                return 'powerpoint'
            else:
                return 'document'
        elif self.is_archive:
            return 'archive'
        else:
            return 'file'

    @property
    def related_model_name(self):
        """获取关联模型名称"""
        return self.content_type.model if self.content_type else None

    @property
    def is_contract_attachment(self):
        """判断是否为合同附件"""
        return self.related_model_name == 'contract'

    @property
    def is_project_attachment(self):
        """判断是否为项目附件"""
        return self.related_model_name == 'project'

    @property
    def related_object_display(self):
        """获取关联对象的显示名称"""
        if self.content_object:
            if hasattr(self.content_object, 'display_name'):
                return self.content_object.display_name
            elif hasattr(self.content_object, 'name'):
                return self.content_object.name
            else:
                return str(self.content_object)
        return None

    @property
    def category_display(self):
        """获取附件类别的显示名称"""
        return dict(self.CATEGORY_CHOICES).get(self.category, self.category) if self.category else None

    @classmethod
    def get_project_category_choices(cls):
        """获取项目附件类别选择"""
        return [choice for choice in cls.CATEGORY_CHOICES if choice[0].startswith('project_')]

    @classmethod
    def get_contract_category_choices(cls):
        """获取合同附件类别选择"""
        return [choice for choice in cls.CATEGORY_CHOICES if choice[0].startswith(('contract_', 'sales_', 'receipt_', 'acceptance_', 'purchase_'))]



    @classmethod
    def get_attachments_for_object(cls, obj):
        """获取指定对象的附件"""
        content_type = ContentType.objects.get_for_model(obj)
        queryset = cls.objects.filter(
            content_type=content_type,
            object_id=obj.id,
            delete_datetime__isnull=True
        )
        return queryset.order_by('-create_datetime')

    @classmethod
    def get_total_size_for_object(cls, obj):
        """获取指定对象的附件总大小"""
        attachments = cls.get_attachments_for_object(obj)
        return sum(attachment.file_size for attachment in attachments)

    def delete(self, using=None, keep_parents=False):
        """软删除重写，同时删除物理文件"""
        if hasattr(self, 'delete_datetime') and self.delete_datetime is None:
            # 在软删除之前，先删除物理文件
            self._delete_physical_file()

            from django.utils import timezone
            self.delete_datetime = timezone.now()
            self.save(update_fields=['delete_datetime'])
        else:
            # 硬删除时也删除物理文件
            self._delete_physical_file()
            super().delete(using=using, keep_parents=keep_parents)

    def _delete_physical_file(self):
        """删除物理文件"""
        if self.file_path:
            try:
                from erp.utils.storage import get_storage_backend
                storage = get_storage_backend()

                # 检查是否还有其他附件引用同一个文件（通过MD5去重）
                if self.file_md5:
                    other_attachments = Attachment.objects.filter(
                        file_md5=self.file_md5,
                        delete_datetime__isnull=True
                    ).exclude(id=self.id)

                    if other_attachments.exists():
                        # 还有其他附件引用同一个文件，不删除物理文件
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.info(f"文件 {self.file_path} 被其他附件引用，不删除物理文件")
                        return

                # 删除物理文件
                storage.delete(self.file_path)

                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"已删除物理文件: {self.file_path}")

            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"删除物理文件失败 {self.file_path}: {str(e)}")
