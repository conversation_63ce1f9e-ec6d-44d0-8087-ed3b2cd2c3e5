from django.db import models
from .base import CoreModel, SoftDeleteDatetimeModel


class Customer(CoreModel, SoftDeleteDatetimeModel):
    """客户模型"""

    # 客户类型选择（基于前端定义）
    CUSTOMER_TYPE_CHOICES = [
        ('C', '企业客户'),
        ('G', '政府客户'),
    ]


    # 行业分类选择（使用有意义的英文代号）
    INDUSTRY_TYPE_CHOICES = [
        ('GOVT', '政府机构'),
        ('FINANCE', '金融服务'),
        ('IT', '信息技术/互联网'),
        ('MANUFACTURING', '制造与工业'),
        ('RETAIL', '零售与消费品'),
        ('ENERGY', '能源与公用事业'),
        ('LOGISTICS', '交通与物流'),
        ('HEALTHCARE', '医疗与健康'),
        ('EDUCATION', '教育与科研'),
        ('REALESTATE', '房地产与建筑'),
        ('PROFESSIONAL', '专业服务'),
        ('AGRICULTURE', '农林牧渔'),
        ('OTHER', '其他/未分类'),
    ]

    # 客户状态选择
    CUSTOMER_STATUS_CHOICES = [
        ('ACTIVE', '活跃'),
        ('BLACKLISTED', '黑名单'),
        ('DEACTIVATED', '已注销'),
    ]



    # 基本信息（基于前端Customer接口）
    code = models.CharField(max_length=50, verbose_name="客户编码")
    name = models.CharField(max_length=200, verbose_name="客户名称")
    tax_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="纳税人识别号")
    type = models.CharField(max_length=10, choices=CUSTOMER_TYPE_CHOICES, default='C', verbose_name="客户类型")
    status = models.CharField(max_length=20, choices=CUSTOMER_STATUS_CHOICES, default='ACTIVE', verbose_name="客户状态")

    industry = models.CharField(max_length=20, choices=INDUSTRY_TYPE_CHOICES, default='OTHER', verbose_name="所属行业")

    # 地区信息（代码+名称冗余存储）
    province_code = models.CharField(max_length=6, null=True, blank=True, verbose_name="省份代码", help_text="省级行政区划代码")
    province = models.CharField(max_length=50, null=True, blank=True, verbose_name="省份名称", help_text="省份名称，冗余存储")
    city_code = models.CharField(max_length=6, null=True, blank=True, verbose_name="城市代码", help_text="市级行政区划代码")
    city = models.CharField(max_length=50, null=True, blank=True, verbose_name="城市名称", help_text="城市名称，冗余存储")

    # 联系信息
    contact_person = models.CharField(max_length=100, null=True, blank=True, verbose_name="联系人")
    phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="联系电话")
    email = models.EmailField(null=True, blank=True, verbose_name="电子邮箱")
    address = models.TextField(null=True, blank=True, verbose_name="联系地址")
    contact_remark = models.TextField(null=True, blank=True, verbose_name="联系备注")

    # 业务信息
    owner_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="负责人姓名")
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    # 开票信息（整合到客户信息中）
    # 注意：客户名称(name)即为开票公司名称，税号(tax_id)即为开票税号
    invoice_bank = models.CharField(max_length=100, null=True, blank=True, verbose_name="开户银行")
    invoice_bank_account = models.CharField(max_length=50, null=True, blank=True, verbose_name="银行账号")
    invoice_address = models.TextField(null=True, blank=True, verbose_name="开票地址")
    invoice_phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="开票电话")

    class Meta:
        db_table = 'erp_customer'
        verbose_name = '客户'
        verbose_name_plural = '客户'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['create_datetime']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        """保存时自动生成客户编码"""
        # 从kwargs中提取自定义日期参数
        custom_date = kwargs.pop('code_date', None)

        if not self.code:
            # 生成客户编码：根据客户类型 + 年月日 + 3位序号
            from django.utils import timezone
            from datetime import datetime

            # 使用自定义日期或当前日期
            if custom_date:
                if isinstance(custom_date, str):
                    # 如果是字符串，尝试解析为日期
                    try:
                        date_obj = datetime.strptime(custom_date, '%Y-%m-%d')
                    except ValueError:
                        raise ValueError(f"日期格式错误，请使用YYYY-MM-DD格式: {custom_date}")
                elif isinstance(custom_date, datetime):
                    date_obj = custom_date
                elif hasattr(custom_date, 'strftime'):
                    # 支持 datetime.date 类型
                    date_obj = datetime.combine(custom_date, datetime.min.time())
                else:
                    raise ValueError(f"不支持的日期类型: {type(custom_date)}")
                today = date_obj.strftime('%y%m%d')
            else:
                today = timezone.now().strftime('%y%m%d')

            # 根据客户类型确定前缀
            prefix = self.type if self.type else 'C'
            code_prefix = f'{prefix}{today}'

            # 查找当天最大的序号（只查询未删除的记录）
            last_customer = Customer.objects.filter(
                code__startswith=code_prefix,
                delete_datetime__isnull=True  # 只查询未删除的记录
            ).order_by('-code').first()

            if last_customer:
                last_seq = int(last_customer.code[-3:])
                new_seq = last_seq + 1
            else:
                new_seq = 1

            self.code = f'{code_prefix}{new_seq:03d}'

        # 验证所有唯一性约束
        self._validate_name_uniqueness()
        self._validate_code_uniqueness()
        self._validate_tax_id_uniqueness()

        super().save(*args, **kwargs)

    def _validate_code_uniqueness(self):
        """验证客户编码在未删除记录中的唯一性"""
        from django.core.exceptions import ValidationError

        existing = Customer.objects.filter(
            code=self.code,
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"客户编码 {self.code} 已存在")

    def _validate_name_uniqueness(self):
        """验证客户名称在未删除记录中的唯一性"""
        from django.core.exceptions import ValidationError

        if not self.name:
            raise ValidationError("客户名称不能为空")

        existing = Customer.objects.filter(
            name=self.name.strip(),
            delete_datetime__isnull=True  # 只检查未删除的记录
        ).exclude(id=self.id)  # 排除自己

        if existing.exists():
            raise ValidationError(f"客户名称 '{self.name}' 已存在")

    def _validate_tax_id_uniqueness(self):
        """验证税号在未删除记录中的唯一性"""
        from django.core.exceptions import ValidationError

        if self.tax_id and self.tax_id.strip():
            existing = Customer.objects.filter(
                tax_id=self.tax_id.strip(),
                delete_datetime__isnull=True  # 只检查未删除的记录
            ).exclude(id=self.id)  # 排除自己

            if existing.exists():
                raise ValidationError(f"税号 {self.tax_id} 已存在")

    @property
    def display_name(self):
        """显示名称"""
        return f"{self.name}({self.code})"

    def get_active_projects_count(self):
        """获取活跃项目数量（未删除的项目）"""
        return self.projects.filter(delete_datetime__isnull=True).count()

    def get_total_contract_amount(self):
        """获取销售合同总金额"""
        from django.db.models import Sum
        from .contract import Contract

        result = Contract.objects.filter(
            customer=self,
            category='sales',
            delete_datetime__isnull=True
        ).aggregate(total_amount=Sum('amount'))

        return result['total_amount'] or 0


