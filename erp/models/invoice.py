from django.db import models
from django.core.exceptions import ValidationError
from decimal import Decimal
from .base import CoreModel, SoftDeleteDatetimeModel
from .contract import Contract
from .attachment import Attachment


class Invoice(CoreModel, SoftDeleteDatetimeModel):
    """发票模型"""

    INVOICE_TYPE_CHOICES = [
        ('ordinary', '普票'),
        ('special', '专票'),
    ]

    # 关联信息
    contract = models.ForeignKey(
        Contract,
        on_delete=models.PROTECT,
        related_name='invoices',
        verbose_name="关联合同"
    )

    # 基本信息
    invoice_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name="开票金额",
        help_text="发票开具金额"
    )
    invoice_date = models.DateField(verbose_name="开票时间")
    invoice_type = models.CharField(
        max_length=20,
        choices=INVOICE_TYPE_CHOICES,
        verbose_name="开票类型"
    )

    # 收付款方信息
    payee = models.CharField(max_length=200, verbose_name="收款方")
    payee_tax_id = models.CharField(
        max_length=50,
        verbose_name="收款人纳税识别号",
        help_text="收款方的纳税人识别号"
    )
    payer = models.CharField(max_length=200, verbose_name="付款方")
    payer_tax_id = models.CharField(
        max_length=50,
        verbose_name="付款人纳税识别号",
        help_text="付款方的纳税人识别号"
    )

    # 发票信息
    invoice_code = models.CharField(
        max_length=50,
        verbose_name="开票代码",
        help_text="发票代码"
    )
    invoice_number = models.CharField(
        max_length=50,
        verbose_name="开票号码",
        help_text="发票号码"
    )
    verification_code = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="开票校验码",
        help_text="发票校验码（可选）"
    )

    # 税务信息
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        verbose_name="税率",
        help_text="税率，如0.13表示13%"
    )
    tax_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name="税额",
        help_text="税额金额"
    )

    # 备注
    description = models.TextField(
        blank=True,
        verbose_name="开票说明",
        help_text="发票相关说明或备注"
    )

    # 源文件（关联到附件）
    source_file = models.ForeignKey(
        Attachment,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        verbose_name="发票源文件",
        help_text="发票的原始文件"
    )

    class Meta:
        db_table = 'erp_invoice'
        verbose_name = '发票'
        verbose_name_plural = '发票'
        ordering = ['-invoice_date', '-create_datetime']
        indexes = [
            models.Index(fields=['contract']),
            models.Index(fields=['invoice_date']),
            models.Index(fields=['invoice_type']),
            models.Index(fields=['invoice_number']),
            models.Index(fields=['invoice_code']),
            models.Index(fields=['create_datetime']),
        ]
        constraints = [
            # 发票号码在同一发票代码下唯一
            models.UniqueConstraint(
                fields=['invoice_code', 'invoice_number'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_invoice_code_number_when_not_deleted'
            )
        ]

    def __str__(self):
        return f"{self.contract.name} - {self.invoice_number}"

    def clean(self):
        """模型验证"""
        super().clean()
        
        # 验证开票金额必须大于0
        if self.invoice_amount <= 0:
            raise ValidationError("开票金额必须大于0")
        
        # 验证税率范围（0-1）
        if not (0 <= self.tax_rate <= 1):
            raise ValidationError("税率必须在0-1之间")
        
        # 验证税额必须大于等于0
        if self.tax_amount < 0:
            raise ValidationError("税额不能为负数")
        
        # 验证发票日期不能早于合同签订日期
        if self.contract and self.contract.sign_date and self.invoice_date < self.contract.sign_date:
            raise ValidationError("发票日期不能早于合同签订日期")

    def save(self, *args, **kwargs):
        """保存时验证数据"""
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def invoice_type_display(self):
        """获取发票类型显示名称"""
        return self.get_invoice_type_display()

    @property
    def contract_partners(self):
        """获取合同相对方信息"""
        if self.contract:
            return self.contract.partners.filter(delete_datetime__isnull=True)
        return []

    @classmethod
    def get_invoices_by_contract(cls, contract_id):
        """获取指定合同的所有发票"""
        return cls.objects.filter(
            contract_id=contract_id,
            delete_datetime__isnull=True
        ).order_by('-invoice_date')

    @classmethod
    def calculate_contract_invoice_summary(cls, contract_id):
        """计算合同的发票汇总信息"""
        invoices = cls.get_invoices_by_contract(contract_id)
        
        total_amount = sum(invoice.invoice_amount for invoice in invoices)
        total_tax = sum(invoice.tax_amount for invoice in invoices)
        invoice_count = len(invoices)
        
        # 按类型统计
        ordinary_count = len([i for i in invoices if i.invoice_type == 'ordinary'])
        special_count = len([i for i in invoices if i.invoice_type == 'special'])
        
        return {
            'total_amount': total_amount,
            'total_tax': total_tax,
            'invoice_count': invoice_count,
            'ordinary_count': ordinary_count,
            'special_count': special_count,
        }

    def validate_against_contract(self):
        """验证发票与合同的业务规则"""
        if not self.contract:
            return True
            
        # 获取该合同的所有发票总额（不包括当前发票）
        existing_invoices = Invoice.objects.filter(
            contract=self.contract,
            delete_datetime__isnull=True
        ).exclude(id=self.id)
        
        existing_total = sum(invoice.invoice_amount for invoice in existing_invoices)
        new_total = existing_total + self.invoice_amount
        
        # 检查是否超过合同总额
        if new_total > self.contract.total_amount:
            raise ValidationError(
                f"发票总额({new_total})不能超过合同总额({self.contract.total_amount})"
            )
        
        return True
