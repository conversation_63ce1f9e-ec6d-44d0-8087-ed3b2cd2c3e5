import uuid
from django.db import models
from django.utils import timezone


class CoreModel(models.Model):
    """核心模型基类，包含通用字段"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name="ID")
    create_datetime = models.DateTimeField(auto_now_add=True, verbose_name="创建时间", help_text="创建时间")
    update_datetime = models.DateTimeField(auto_now=True, verbose_name="更新时间", help_text="更新时间")
    creator = models.CharField(max_length=100, null=True, blank=True, verbose_name="创建者", help_text="创建者")
    updater = models.CharField(max_length=100, null=True, blank=True, verbose_name="更新者", help_text="更新者")

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        """重写save方法，自动设置creator和updater"""
        from erp.utils.jwt_helper import JWTHelper

        # 检查是否是新建记录（通过检查数据库中是否存在该记录）
        is_new = self._state.adding

        # 如果是新建记录，设置creator
        if is_new:
            self.creator = JWTHelper.get_current_user_id()

        # 每次保存都更新updater
        self.updater = JWTHelper.get_current_user_id()

        super().save(*args, **kwargs)


class SoftDeleteManager(models.Manager):
    """软删除管理器，自动过滤已删除的记录"""

    def get_queryset(self):
        """重写查询集，自动过滤已删除的记录"""
        return super().get_queryset().filter(delete_datetime__isnull=True)

    def include_deleted(self):
        """获取包含已删除记录的查询集"""
        return super().get_queryset()


class SoftDeleteDatetimeModel(models.Model):
    """软删除模型基类"""
    delete_datetime = models.DateTimeField(null=True, blank=True, verbose_name="删除时间", help_text="删除时间")

    # 默认管理器，自动过滤已删除记录
    objects = SoftDeleteManager()
    # 包含已删除记录的管理器
    all_objects = models.Manager()

    class Meta:
        abstract = True

    def delete(self, using=None, keep_parents=False):
        """软删除"""
        self.delete_datetime = timezone.now()
        self.save(using=using)

    def hard_delete(self, using=None, keep_parents=False):
        """硬删除"""
        super().delete(using=using, keep_parents=keep_parents)
