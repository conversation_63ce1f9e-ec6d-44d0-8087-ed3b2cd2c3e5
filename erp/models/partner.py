from django.db import models
from django.core.exceptions import ValidationError
from .base import CoreModel, SoftDeleteDatetimeModel


class Partner(CoreModel, SoftDeleteDatetimeModel):
    """相对方模型（统一的客户和供应商管理）"""

    # 合作伙伴类型选择（区分客户/供应商）
    BUSINESS_TYPE_CHOICES = [
        ('customer', '客户'),
        ('supplier', '供应商'),
    ]

    # 相对方类型选择（具体分类）
    PARTNER_TYPE_CHOICES = [
        # 客户类型
        ('C', '企业客户'),
        ('G', '政府客户'),
        # 供应商类型
        ('ORIGINAL', '原厂'),
        ('CHANNEL', '渠道'),
    ]

    # 行业分类选择（客户和供应商统一使用）
    INDUSTRY_CHOICES = [
        ('GOVT', '政府机构'),
        ('FINANCE', '金融服务'),
        ('IT', '信息技术/互联网'),
        ('MANUFACTURING', '制造与工业'),
        ('RETAIL', '零售与消费品'),
        ('ENERGY', '能源与公用事业'),
        ('LOGISTICS', '交通与物流'),
        ('HEALTHCARE', '医疗与健康'),
        ('EDUCATION', '教育与科研'),
        ('REALESTATE', '房地产与建筑'),
        ('PROFESSIONAL', '专业服务'),
        ('AGRICULTURE', '农林牧渔'),
        ('OTHER', '其他/未分类'),
    ]

    # 相对方状态选择（统一状态）
    PARTNER_STATUS_CHOICES = [
        ('ACTIVE', '活跃'),
        ('SUSPENDED', '暂停'),
        ('BLACKLISTED', '黑名单'),
        ('DEACTIVATED', '已注销'),
    ]

    # 基本信息
    code = models.CharField(max_length=50, verbose_name="相对方编码")
    name = models.CharField(max_length=200, verbose_name="相对方名称")
    tax_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="纳税人识别号")
    partner_type = models.CharField(max_length=20, choices=BUSINESS_TYPE_CHOICES, default='customer', verbose_name="合作伙伴类型")
    type = models.CharField(max_length=20, choices=PARTNER_TYPE_CHOICES, verbose_name="相对方类型")
    status = models.CharField(max_length=20, choices=PARTNER_STATUS_CHOICES, default='ACTIVE', verbose_name="状态")
    industry = models.CharField(max_length=20, choices=INDUSTRY_CHOICES, verbose_name="所属行业")

    # 地区信息
    province_code = models.CharField(max_length=10, null=True, blank=True, verbose_name="省份代码")
    province = models.CharField(max_length=50, null=True, blank=True, verbose_name="省份名称")
    city_code = models.CharField(max_length=10, null=True, blank=True, verbose_name="城市代码")
    city = models.CharField(max_length=50, null=True, blank=True, verbose_name="城市名称")

    # 联系信息
    contact_person = models.CharField(max_length=100, verbose_name="联系人")
    contact_phone = models.CharField(max_length=20, verbose_name="联系电话")
    contact_email = models.EmailField(null=True, blank=True, verbose_name="电子邮箱")
    contact_address = models.TextField(null=True, blank=True, verbose_name="联系地址")
    contact_remark = models.TextField(null=True, blank=True, verbose_name="联系备注")
    website = models.URLField(null=True, blank=True, verbose_name="官网地址")

    # 业务信息
    owner_name = models.CharField(max_length=100, verbose_name="负责人姓名")
    owner_id = models.CharField(max_length=50, null=True, blank=True, verbose_name="负责人ID")
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    # 开票信息
    invoice_bank = models.CharField(max_length=100, null=True, blank=True, verbose_name="开户银行")
    invoice_bank_account = models.CharField(max_length=50, null=True, blank=True, verbose_name="银行账号")
    invoice_address = models.TextField(null=True, blank=True, verbose_name="开票地址")
    invoice_phone = models.CharField(max_length=20, null=True, blank=True, verbose_name="开票电话")
    invoice_remark = models.TextField(null=True, blank=True, verbose_name="开票备注")

    class Meta:
        db_table = 'erp_partner'
        verbose_name = '相对方'
        verbose_name_plural = '相对方'
        ordering = ['-create_datetime']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['type']),
            models.Index(fields=['status']),
            models.Index(fields=['industry']),
            models.Index(fields=['create_datetime']),
        ]
        constraints = [
            # 确保相对方编码在未删除记录中唯一
            models.UniqueConstraint(
                fields=['code'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_partner_code'
            ),
            # 确保相对方名称在未删除记录中唯一
            models.UniqueConstraint(
                fields=['name'],
                condition=models.Q(delete_datetime__isnull=True),
                name='unique_partner_name'
            ),
            # 确保税号在未删除记录中唯一（如果不为空）
            models.UniqueConstraint(
                fields=['tax_id'],
                condition=models.Q(delete_datetime__isnull=True, tax_id__isnull=False),
                name='unique_partner_tax_id'
            ),
        ]

    def __str__(self):
        return f"{self.name}({self.code})"

    @property
    def is_customer(self):
        """判断是否为客户类型"""
        return self.type in ['C', 'G']

    @property
    def is_supplier(self):
        """判断是否为供应商类型"""
        return self.type in ['ORIGINAL', 'CHANNEL']

    @property
    def display_name(self):
        """显示名称"""
        return f"{self.name}({self.code})"

    def get_industry_choices(self):
        """获取行业选择（客户和供应商统一使用）"""
        return self.INDUSTRY_CHOICES

    def get_industry_display(self):
        """获取行业显示名称"""
        choices = self.get_industry_choices()
        for code, name in choices:
            if code == self.industry:
                return name
        return self.industry

    def save(self, *args, **kwargs):
        """保存时自动生成编码并验证数据"""
        # 如果是新记录且没有编码，自动生成编码
        if not self.code:
            self._generate_code(kwargs.get('code_date'))

        # 验证所有唯一性约束
        self._validate_name_uniqueness()
        self._validate_code_uniqueness()
        self._validate_tax_id_uniqueness()
        self._validate_industry_choice()

        super().save(*args, **kwargs)

    def _generate_code(self, code_date=None):
        """生成相对方编码"""
        from datetime import date
        
        # 使用指定日期或当前日期
        today = code_date.strftime('%y%m%d') if code_date else date.today().strftime('%y%m%d')
        
        # 根据相对方类型确定前缀
        if self.type in ['C', 'G']:
            prefix = self.type
        elif self.type in ['ORIGINAL', 'CHANNEL']:
            prefix = 'V'  # 供应商统一使用V前缀
        else:
            raise ValidationError(f"无效的相对方类型: {self.type}")
            
        code_prefix = f'{prefix}{today}'

        # 查找当天最大的序号（只查询未删除的记录）
        last_partner = Partner.objects.filter(
            code__startswith=code_prefix,
            delete_datetime__isnull=True
        ).order_by('-code').first()

        if last_partner:
            last_seq = int(last_partner.code[-3:])
            new_seq = last_seq + 1
        else:
            new_seq = 1

        self.code = f'{code_prefix}{new_seq:03d}'

    def _validate_code_uniqueness(self):
        """验证相对方编码在未删除记录中的唯一性"""
        existing = Partner.objects.filter(
            code=self.code,
            delete_datetime__isnull=True
        ).exclude(id=self.id)

        if existing.exists():
            raise ValidationError(f"相对方编码 {self.code} 已存在")

    def _validate_name_uniqueness(self):
        """验证相对方名称在未删除记录中的唯一性"""
        if not self.name or not self.name.strip():
            raise ValidationError("相对方名称不能为空")

        existing = Partner.objects.filter(
            name=self.name.strip(),
            delete_datetime__isnull=True
        ).exclude(id=self.id)

        if existing.exists():
            raise ValidationError(f"相对方名称 {self.name} 已存在")

    def _validate_tax_id_uniqueness(self):
        """验证税号在未删除记录中的唯一性"""
        if self.tax_id and self.tax_id.strip():
            existing = Partner.objects.filter(
                tax_id=self.tax_id.strip(),
                delete_datetime__isnull=True
            ).exclude(id=self.id)

            if existing.exists():
                raise ValidationError(f"税号 {self.tax_id} 已存在")

    def _validate_industry_choice(self):
        """验证行业选择是否符合相对方类型"""
        valid_industries = [choice[0] for choice in self.get_industry_choices()]
        if self.industry and self.industry not in valid_industries:
            type_name = "客户" if self.is_customer else "供应商"
            raise ValidationError(f"行业选择 {self.industry} 不适用于{type_name}类型")

    def get_active_projects_count(self):
        """获取活跃项目数量（客户类型）"""
        if self.is_customer:
            return self.projects.filter(delete_datetime__isnull=True).count()
        return 0

    def get_sales_contract_count(self):
        """获取销售合同数量（客户类型）"""
        if self.is_customer:
            return self.contracts.filter(
                delete_datetime__isnull=True,
                category='sales'
            ).count()
        return 0

    def get_sales_contract_total_amount(self):
        """获取销售合同总金额（客户类型）"""
        if self.is_customer:
            from django.db.models import Sum
            total = self.contracts.filter(
                delete_datetime__isnull=True,
                category='sales'
            ).aggregate(total=Sum('total_amount'))['total']
            return float(total) if total else 0
        return 0

    def get_purchase_contract_count(self):
        """获取采购合同数量（供应商类型）"""
        if self.is_supplier:
            return self.contracts.filter(
                delete_datetime__isnull=True,
                category='procurement'
            ).count()
        return 0

    def get_purchase_contract_total_amount(self):
        """获取采购合同总金额（供应商类型）"""
        if self.is_supplier:
            from django.db.models import Sum
            total = self.contracts.filter(
                delete_datetime__isnull=True,
                category='procurement'
            ).aggregate(total=Sum('total_amount'))['total']
            return float(total) if total else 0
        return 0

    def get_default_payment_info(self):
        """获取默认收款信息（供应商类型）"""
        if self.is_supplier:
            return self.payment_infos.filter(
                is_default=True,
                delete_datetime__isnull=True
            ).first()
        return None


class PartnerPaymentInfo(CoreModel, SoftDeleteDatetimeModel):
    """相对方收款信息模型（仅供应商类型使用）"""

    # 关联信息
    partner = models.ForeignKey(
        Partner,
        on_delete=models.CASCADE,
        related_name='payment_infos',
        verbose_name="相对方"
    )

    # 账户信息
    bank_name = models.CharField(max_length=100, verbose_name="开户银行")
    account_number = models.CharField(max_length=50, verbose_name="银行账号")

    # 管理信息
    is_default = models.BooleanField(default=False, verbose_name="是否默认账户")
    remark = models.TextField(null=True, blank=True, verbose_name="备注")

    class Meta:
        db_table = 'erp_partner_payment_info'
        verbose_name = '相对方收款信息'
        verbose_name_plural = '相对方收款信息'
        ordering = ['-is_default', '-create_datetime']
        indexes = [
            models.Index(fields=['partner', 'is_default']),
        ]
        constraints = [
            # 确保每个相对方只有一个默认账户
            models.UniqueConstraint(
                fields=['partner'],
                condition=models.Q(is_default=True, delete_datetime__isnull=True),
                name='unique_default_payment_per_partner'
            )
        ]

    def __str__(self):
        return f"{self.partner.name} - {self.bank_name} - {self.account_number}"

    def save(self, *args, **kwargs):
        """保存时处理默认账户逻辑和类型验证"""
        # 验证只有供应商类型才能有收款信息
        if not self.partner.is_supplier:
            raise ValidationError("只有供应商类型的相对方才能添加收款信息")

        if self.is_default:
            # 如果设置为默认账户，将同一相对方的其他账户设为非默认
            PartnerPaymentInfo.objects.filter(
                partner=self.partner,
                delete_datetime__isnull=True
            ).exclude(id=self.id).update(is_default=False)

        super().save(*args, **kwargs)
