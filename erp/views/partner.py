from django.db.models import Q
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import ValidationError
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import Partner, PartnerPaymentInfo
from erp.serializers.partner import (
    PartnerSerializer, PartnerListSerializer, PartnerCreateSerializer, PartnerUpdateSerializer,
    PartnerPartialUpdateSerializer, PartnerStatusUpdateSerializer, PartnerSimpleSerializer,
    PartnerPaymentInfoSerializer, PartnerPaymentInfoUpdateSerializer
)
from erp.filters import PartnerFilter
from erp.utils.json_response import ListResponse, ErrorResponse, StandardResponse, CreatedResponse
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.excel_export import PartnerExcelExporter
from erp.swagger.response_schemas import excel_export_responses


class PartnerViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """相对方管理视图集"""

    queryset = Partner.objects.filter(delete_datetime__isnull=True)
    serializer_class = PartnerSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = PartnerFilter
    ordering_fields = [
        'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
        'province_code', 'province', 'city_code', 'city',
        'contact_person', 'contact_phone', 'contact_email', 'website',
        'owner_name', 'owner_id',
        'invoice_bank', 'invoice_bank_account', 'invoice_phone',
        'create_datetime', 'update_datetime', 'creator', 'updater'
    ]
    ordering = ['-create_datetime']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return PartnerListSerializer
        elif self.action == 'create':
            return PartnerCreateSerializer
        elif self.action == 'update':
            return PartnerUpdateSerializer
        elif self.action == 'partial_update':
            return PartnerPartialUpdateSerializer
        elif self.action == 'update_status':
            return PartnerStatusUpdateSerializer
        elif self.action == 'simple_list':
            return PartnerSimpleSerializer
        return PartnerSerializer

    @swagger_auto_schema(
        operation_summary="获取相对方列表",
        operation_description="获取相对方列表，支持分页、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description='排序字段，支持：create_datetime, update_datetime, name, type, industry, code。前缀-表示倒序，如：-create_datetime',
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={200: PartnerListSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """获取相对方列表"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="创建相对方",
        operation_description="创建新的相对方",
        request_body=PartnerCreateSerializer,
        responses={201: PartnerSerializer}
    )
    def create(self, request):
        """创建相对方"""
        try:
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 设置创建者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['creator'] = request.user.username

            partner = serializer.save()

            # 返回完整的相对方信息
            response_serializer = PartnerSerializer(partner)
            return CreatedResponse(data=response_serializer.data, msg="相对方创建成功")

        except ValidationError as e:
            return ErrorResponse(msg=f"相对方创建失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"相对方创建失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="获取相对方详情",
        operation_description="根据ID获取相对方详细信息",
        responses={200: PartnerSerializer}
    )
    def retrieve(self, request, pk=None):
        """获取相对方详情"""
        try:
            partner = self.get_object()
            serializer = PartnerSerializer(partner)
            return StandardResponse(data=serializer.data, msg="获取相对方详情成功")
        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            if "No Partner matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="相对方不存在", code=4004)
            return ErrorResponse(msg=f"获取相对方详情失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="更新相对方",
        operation_description="完全更新相对方信息（PUT方法）",
        request_body=PartnerUpdateSerializer,
        responses={200: PartnerSerializer}
    )
    def update(self, request, *args, **kwargs):
        """更新相对方"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data)
            if serializer.is_valid():
                # 设置更新者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username
                    # 同时设置到实例上，供自定义字段使用
                    instance.updater = request.user.username

                instance = serializer.save()
                response_serializer = PartnerSerializer(instance)
                return StandardResponse(data=response_serializer.data, msg="相对方更新成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"相对方更新失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"相对方更新失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="部分更新相对方",
        operation_description="部分更新相对方信息（PATCH方法）",
        request_body=PartnerPartialUpdateSerializer,
        responses={200: PartnerSerializer}
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新相对方"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            if serializer.is_valid():
                # 设置更新者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username
                    # 同时设置到实例上，供自定义字段使用
                    instance.updater = request.user.username

                instance = serializer.save()
                response_serializer = PartnerSerializer(instance)
                return StandardResponse(data=response_serializer.data, msg="相对方更新成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"相对方更新失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"相对方更新失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="删除相对方",
        operation_description="软删除相对方",
        responses={200: openapi.Response('删除成功')}
    )
    def destroy(self, request, pk=None):
        """软删除相对方"""
        try:
            partner = self.get_object()

            # 执行软删除
            partner.delete()  # 这会调用模型的软删除方法

            return StandardResponse(msg="相对方删除成功")

        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            if "No Partner matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="相对方不存在", code=4004)
            return ErrorResponse(msg=f"删除相对方失败: {str(e)}")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="更新相对方状态",
        operation_description="更新相对方的状态",
        request_body=PartnerStatusUpdateSerializer,
        responses={200: PartnerSerializer}
    )
    def update_status(self, request, pk=None):
        """更新相对方状态"""
        try:
            partner = self.get_object()
            serializer = self.get_serializer(partner, data=request.data, partial=True)
            
            if serializer.is_valid():
                # 设置更新者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username
                
                partner = serializer.save()
                response_serializer = PartnerSerializer(partner)
                return StandardResponse(data=response_serializer.data, msg="相对方状态更新成功")
            
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
            
        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"更新相对方状态失败: {str(e)}")

    @action(detail=False, methods=['get'], filter_backends=[], filterset_class=None)
    @swagger_auto_schema(
        operation_summary="获取相对方简单列表",
        operation_description="获取相对方简单列表，用于下拉选择等场景，支持分页和搜索",
        manual_parameters=[
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description='搜索关键词，支持按相对方编码和相对方名称搜索',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'type',
                openapi.IN_QUERY,
                description='相对方类型过滤，可选值：C（企业客户）、G（政府客户）、ORIGINAL（原厂）、CHANNEL（渠道）',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description='页码',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description='每页数量',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'partner_type',
                openapi.IN_QUERY,
                description='合作伙伴类型过滤，可选值：customer（客户）、supplier（供应商）',
                type=openapi.TYPE_STRING,
                enum=['customer', 'supplier'],
                required=False
            ),
        ],
        responses={200: PartnerSimpleSerializer(many=True)}
    )
    def simple_list(self, request):
        """获取相对方简单列表"""
        queryset = Partner.objects.filter(delete_datetime__isnull=True).order_by('-create_datetime')

        # 合作伙伴类型过滤（新增）
        partner_type = request.query_params.get('partner_type', None)
        if partner_type:
            queryset = queryset.filter(partner_type=partner_type)

        # 相对方类型过滤（原有）
        type_filter = request.query_params.get('type', None)
        if type_filter:
            queryset = queryset.filter(type=type_filter)

        # 搜索功能
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(code__icontains=search) | Q(name__icontains=search)
            )

        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request, view=self)
        if page is not None:
            serializer = PartnerSimpleSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        # 如果没有分页，返回所有数据
        serializer = PartnerSimpleSerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取相对方简单列表成功")

    @action(detail=True, methods=['get'])
    @swagger_auto_schema(
        operation_summary="获取相对方收款信息列表",
        operation_description="获取指定相对方的收款信息列表（仅供应商类型）",
        responses={200: PartnerPaymentInfoSerializer(many=True)}
    )
    def payment_infos(self, request, pk=None):
        """获取相对方收款信息列表"""
        try:
            partner = self.get_object()

            # 验证是否为供应商类型
            if not partner.is_supplier:
                return ErrorResponse(msg="只有供应商类型的相对方才能查看收款信息", code=4003)

            payment_infos = partner.payment_infos.filter(delete_datetime__isnull=True)
            serializer = PartnerPaymentInfoSerializer(payment_infos, many=True)
            return ListResponse(data=serializer.data, msg="获取收款信息列表成功")

        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"获取收款信息列表失败: {str(e)}")

    @action(detail=True, methods=['post'])
    @swagger_auto_schema(
        operation_summary="新增相对方收款信息",
        operation_description="为指定相对方新增收款信息（仅供应商类型）",
        request_body=PartnerPaymentInfoUpdateSerializer,
        responses={201: PartnerPaymentInfoSerializer}
    )
    def add_payment_info(self, request, pk=None):
        """新增相对方收款信息"""
        try:
            partner = self.get_object()

            # 验证是否为供应商类型
            if not partner.is_supplier:
                return ErrorResponse(msg="只有供应商类型的相对方才能添加收款信息", code=4003)

            serializer = PartnerPaymentInfoUpdateSerializer(data=request.data)
            if serializer.is_valid():
                # 设置创建者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['creator'] = request.user.username

                # 关联相对方
                payment_info = serializer.save(partner=partner)

                response_serializer = PartnerPaymentInfoSerializer(payment_info)
                return CreatedResponse(data=response_serializer.data, msg="收款信息添加成功")

            return ErrorResponse(msg="数据验证失败", data=serializer.errors)

        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except ValidationError as e:
            return ErrorResponse(msg=f"添加收款信息失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"添加收款信息失败: {str(e)}")

    @action(detail=True, methods=['patch'], url_path='update_payment_info')
    @swagger_auto_schema(
        operation_summary="更新相对方收款信息",
        operation_description="更新指定相对方的收款信息（仅供应商类型），需要在请求体中提供payment_info_id",
        request_body=PartnerPaymentInfoUpdateSerializer,
        responses={200: PartnerPaymentInfoSerializer}
    )
    def update_payment_info(self, request, pk=None):
        """更新相对方收款信息"""
        try:
            partner = self.get_object()

            # 验证是否为供应商类型
            if not partner.is_supplier:
                return ErrorResponse(msg="只有供应商类型的相对方才能更新收款信息", code=4003)

            # 从请求体中获取payment_info_id
            payment_info_id = request.data.get('payment_info_id')
            if not payment_info_id:
                return ErrorResponse(msg="缺少payment_info_id参数", code=4000)

            # 获取收款信息
            try:
                payment_info = partner.payment_infos.get(
                    id=payment_info_id,
                    delete_datetime__isnull=True
                )
            except PartnerPaymentInfo.DoesNotExist:
                return ErrorResponse(msg="收款信息不存在", code=4004)

            serializer = PartnerPaymentInfoUpdateSerializer(payment_info, data=request.data, partial=True)
            if serializer.is_valid():
                # 设置更新者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username

                payment_info = serializer.save()
                response_serializer = PartnerPaymentInfoSerializer(payment_info)
                return StandardResponse(data=response_serializer.data, msg="收款信息更新成功")

            return ErrorResponse(msg="数据验证失败", data=serializer.errors)

        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"更新收款信息失败: {str(e)}")

    @action(detail=True, methods=['delete'], url_path='delete_payment_info')
    @swagger_auto_schema(
        operation_summary="删除相对方收款信息",
        operation_description="删除指定相对方的收款信息（仅供应商类型），需要在请求体中提供payment_info_id",
        responses={200: openapi.Response('删除成功')}
    )
    def delete_payment_info(self, request, pk=None):
        """删除相对方收款信息"""
        try:
            partner = self.get_object()

            # 验证是否为供应商类型
            if not partner.is_supplier:
                return ErrorResponse(msg="只有供应商类型的相对方才能删除收款信息", code=4003)

            # 从请求体中获取payment_info_id
            payment_info_id = request.data.get('payment_info_id')
            if not payment_info_id:
                return ErrorResponse(msg="缺少payment_info_id参数", code=4000)

            # 获取收款信息
            try:
                payment_info = partner.payment_infos.get(
                    id=payment_info_id,
                    delete_datetime__isnull=True
                )
            except PartnerPaymentInfo.DoesNotExist:
                return ErrorResponse(msg="收款信息不存在", code=4004)

            # 执行软删除
            payment_info.delete()

            return StandardResponse(msg="收款信息删除成功")

        except Partner.DoesNotExist:
            return ErrorResponse(msg="相对方不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"删除收款信息失败: {str(e)}")

    @action(detail=False, methods=['get'], authentication_classes=[], permission_classes=[])
    @swagger_auto_schema(
        operation_summary="导出相对方数据Excel",
        operation_description="导出所有相对方数据到Excel文件，支持多选过滤参数",
        manual_parameters=[
            openapi.Parameter(
                'type',
                openapi.IN_QUERY,
                description='相对方类型过滤，支持多选，逗号分隔。可选值：C（企业客户）、G（政府客户）、ORIGINAL（原厂）、CHANNEL（渠道）',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description='相对方状态过滤，支持多选，逗号分隔。可选值：ACTIVE（活跃）、SUSPENDED（暂停）、BLACKLISTED（黑名单）、DEACTIVATED（已注销）',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'industry',
                openapi.IN_QUERY,
                description='所属行业过滤，支持多选，逗号分隔',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description='模糊搜索，支持相对方名称、编码、联系人、电话、纳税人识别号',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'include_payment_info',
                openapi.IN_QUERY,
                description='是否包含收款信息（供应商类型），true表示每个收款账户一行，false表示基本信息一行',
                type=openapi.TYPE_BOOLEAN,
                required=False
            ),
        ],
        responses=excel_export_responses
    )
    def export_excel(self, request):
        """导出相对方数据Excel"""
        try:
            # 获取查询集
            queryset = self.filter_queryset(self.get_queryset())

            # 预加载收款信息（用于包含收款信息的导出）
            queryset = queryset.prefetch_related('payment_infos')

            # 获取所有数据（不分页）
            partners = list(queryset)

            # 创建导出器
            exporter = PartnerExcelExporter()

            # 检查是否包含收款信息
            include_payment_info = request.query_params.get('include_payment_info', 'false').lower() == 'true'

            if include_payment_info:
                return exporter.export_partners_with_payment_info(partners)
            else:
                return exporter.export_partners(partners)

        except Exception as e:
            return ErrorResponse(msg=f"导出Excel失败: {str(e)}")
