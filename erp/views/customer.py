from django.db.models import Q
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import Partner
from erp.serializers.compatibility import (
    CustomerCompatibilitySerializer, CustomerListCompatibilitySerializer,
    CustomerCreateUpdateCompatibilitySerializer
)
from erp.serializers.customer import CustomerStatusUpdateSerializer, CustomerSimpleSerializer
from erp.filters import CustomerFilter
from erp.utils.json_response import ListResponse, ErrorResponse, DetailResponse, CreatedResponse, StandardResponse
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.excel_export import CustomerExcelExporter
from erp.swagger.response_schemas import (
    customer_list_responses, customer_create_responses, customer_retrieve_responses,
    customer_update_responses, customer_delete_responses, customer_export_responses
)


class CustomerViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """客户管理视图集（兼容性视图，内部使用Partner模型）"""
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = CustomerFilter
    ordering_fields = ['create_datetime', 'update_datetime', 'name', 'type', 'industry', 'code']
    ordering = ['-create_datetime']

    def get_queryset(self):
        """获取查询集，只返回客户类型的相对方"""
        return Partner.objects.filter(
            type__in=['C', 'G'],  # 只返回客户类型
            delete_datetime__isnull=True
        )

    def get_serializer_class(self):
        """根据操作类型返回对应的序列化器"""
        if self.action == 'list':
            return CustomerListCompatibilitySerializer
        elif self.action == 'retrieve':
            return CustomerCompatibilitySerializer
        elif self.action == 'create':
            return CustomerCreateUpdateCompatibilitySerializer
        elif self.action == 'update':
            return CustomerCreateUpdateCompatibilitySerializer
        elif self.action == 'partial_update':
            return CustomerCreateUpdateCompatibilitySerializer  # 使用同一个序列化器
        elif self.action == 'simple_list':
            return CustomerSimpleSerializer
        return CustomerCompatibilitySerializer

    @swagger_auto_schema(
        operation_summary="获取客户列表",
        operation_description="获取客户列表，支持分页、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description='排序字段，支持：create_datetime, update_datetime, name, type, industry, code。前缀-表示倒序，如：-create_datetime',
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses=customer_list_responses
    )
    def list(self, request, *args, **kwargs):
        """获取客户列表"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="创建客户",
        operation_description="创建新的客户记录",
        request_body=CustomerCreateUpdateCompatibilitySerializer,
        responses=customer_create_responses
    )
    def create(self, request):
        """创建客户"""
        try:
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 设置创建者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['creator'] = request.user.username

            customer = serializer.save()

            # 返回完整的客户信息
            response_serializer = CustomerSerializer(customer)
            return CreatedResponse(data=response_serializer.data, msg="客户创建成功")

        except Exception as e:
            return ErrorResponse(msg=f"创建客户失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="获取客户详情",
        operation_description="根据ID获取客户详细信息",
        responses=customer_retrieve_responses
    )
    def retrieve(self, request, pk=None):
        """获取客户详情"""
        try:
            customer = self.get_object()
            serializer_class = self.get_serializer_class()
            serializer = serializer_class(customer)
            return DetailResponse(data=serializer.data, msg="客户详情获取成功")
        except Customer.DoesNotExist:
            return ErrorResponse(msg="客户不存在", code=4004)
        except Exception as e:
            # 检查是否是因为对象不存在导致的错误
            if "No Customer matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="客户不存在", code=4004)
            return ErrorResponse(msg=f"获取客户详情失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="完整更新客户",
        operation_description="完整更新客户信息（PUT方法，需要提供所有必填字段）",
        request_body=CustomerCreateUpdateCompatibilitySerializer,
        responses=customer_update_responses
    )
    def update(self, request, pk=None):
        """完整更新客户（PUT）"""
        try:
            customer = self.get_object()
            serializer = self.get_serializer(customer, data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['updater'] = request.user.username

            customer = serializer.save()

            # 返回完整的客户信息
            response_serializer = CustomerSerializer(customer)
            return DetailResponse(data=response_serializer.data, msg="客户更新成功")

        except Customer.DoesNotExist:
            return ErrorResponse(msg="客户不存在", code=4004)
        except Exception as e:
            if "No Customer matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="客户不存在", code=4004)
            return ErrorResponse(msg=f"更新客户失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="部分更新客户",
        operation_description="部分更新客户信息（PATCH方法，只需要提供要更新的字段，所有字段都是可选的）",
        request_body=CustomerCreateUpdateCompatibilitySerializer,
        responses=customer_update_responses
    )
    def partial_update(self, request, pk=None):
        """部分更新客户（PATCH）"""
        try:
            customer = self.get_object()
            serializer = self.get_serializer(customer, data=request.data, partial=True)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['updater'] = request.user.username

            customer = serializer.save()

            # 返回完整的客户信息
            response_serializer = CustomerSerializer(customer)
            return DetailResponse(data=response_serializer.data, msg="客户部分更新成功")

        except Customer.DoesNotExist:
            return ErrorResponse(msg="客户不存在", code=4004)
        except Exception as e:
            if "No Customer matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="客户不存在", code=4004)
            return ErrorResponse(msg=f"部分更新客户失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="删除客户",
        operation_description="软删除客户记录",
        responses=customer_delete_responses
    )
    def destroy(self, request, pk=None):
        """软删除客户"""
        try:
            customer = self.get_object()

            # 执行软删除
            customer.delete()  # 这会调用模型的软删除方法

            return StandardResponse(msg="客户删除成功")

        except Customer.DoesNotExist:
            return ErrorResponse(msg="客户不存在", code=4004)
        except Exception as e:
            if "No Customer matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="客户不存在", code=4004)
            return ErrorResponse(msg=f"删除客户失败: {str(e)}")

    @action(detail=False, methods=['get'], authentication_classes=[], permission_classes=[])
    @swagger_auto_schema(
        operation_summary="导出客户数据Excel",
        operation_description="导出所有客户数据到Excel文件，支持多选过滤参数",
        responses=customer_export_responses
    )
    def export_excel(self, request):
        """导出客户数据到Excel"""
        try:
            # 获取筛选后的客户数据（不分页）
            queryset = self.filter_queryset(self.get_queryset())

            # 预加载相关数据以提高性能
            customers = queryset.select_related().all()

            # 创建Excel导出器
            exporter = CustomerExcelExporter()

            # 导出Excel
            return exporter.export_customers(customers)

        except Exception as e:
            return ErrorResponse(msg=f"导出Excel失败: {str(e)}")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="更新客户状态",
        operation_description="更新指定客户的状态",
        request_body=CustomerStatusUpdateSerializer,
        responses={
            200: openapi.Response(
                description="状态更新成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应代码'),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, description='客户ID'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='更新后的状态'),
                                'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='状态显示名称'),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description="请求参数错误"),
            404: openapi.Response(description="客户不存在"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def update_status(self, request, pk=None):
        """更新客户状态"""
        try:
            customer = self.get_object()
            serializer = CustomerStatusUpdateSerializer(data=request.data)

            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 更新状态
            new_status = serializer.validated_data['status']
            customer.status = new_status

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                customer.updater = request.user.username

            customer.save()

            # 返回更新后的状态信息
            response_data = {
                'id': str(customer.id),
                'status': customer.status,
                'status_display': customer.get_status_display()
            }

            return StandardResponse(data=response_data, msg="客户状态更新成功")

        except Customer.DoesNotExist:
            return ErrorResponse(msg="客户不存在", code=4004)
        except Exception as e:
            if "No Customer matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="客户不存在", code=4004)
            return ErrorResponse(msg=f"更新客户状态失败: {str(e)}")

    @action(detail=False, methods=['get'], filter_backends=[], filterset_class=None)
    @swagger_auto_schema(
        operation_summary="获取客户简单列表",
        operation_description="获取客户简单列表，用于下拉选择等场景，支持分页和搜索",
        manual_parameters=[
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description='搜索关键词，支持按客户编码和客户名称搜索',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description='页码',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description='每页数量',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
        ],
        responses={200: CustomerSimpleSerializer(many=True)}
    )
    def simple_list(self, request):
        """获取客户简单列表"""
        queryset = Customer.objects.filter(delete_datetime__isnull=True).order_by('-create_datetime')

        # 搜索功能
        search = request.query_params.get('search', None)
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(code__icontains=search) | Q(name__icontains=search)
            )

        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request, view=self)
        if page is not None:
            serializer = CustomerSimpleSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        # 如果没有分页，返回所有数据
        serializer = CustomerSimpleSerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取客户简单列表成功")

