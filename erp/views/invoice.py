from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging

from erp.models import Invoice, Contract, Attachment
from erp.serializers.invoice import (
    InvoiceListSerializer, InvoiceDetailSerializer,
    InvoiceCreateSerializer, InvoiceUpdateSerializer,
    InvoiceOCRUploadSerializer
)
from erp.filters import InvoiceFilter
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.json_response import StandardResponse, ListResponse, CreatedResponse
from erp.utils.pagination import CustomPagination
from erp.utils.ocr_service import OCRServiceFactory, OCRServiceException
from erp.swagger.response_schemas import invoice_list_responses, invoice_detail_responses

logger = logging.getLogger(__name__)


class InvoiceViewSet(JWTRequiredMixin, viewsets.ModelViewSet):
    """发票管理视图集"""
    
    queryset = Invoice.objects.filter(delete_datetime__isnull=True)
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = InvoiceFilter
    search_fields = ['contract__name', 'invoice_number', 'invoice_code', 'payee', 'payer']
    ordering_fields = [
        'invoice_amount', 'invoice_date', 'invoice_type', 'tax_rate', 'tax_amount',
        'create_datetime', 'update_datetime', 'contract__name', 'payee', 'payer'
    ]
    ordering = ['-invoice_date', '-create_datetime']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return InvoiceListSerializer
        elif self.action == 'retrieve':
            return InvoiceDetailSerializer
        elif self.action == 'create':
            return InvoiceCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return InvoiceUpdateSerializer
        return InvoiceListSerializer

    @swagger_auto_schema(
        operation_summary="获取发票列表",
        operation_description="获取发票记录列表，支持分页、搜索、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'search', openapi.IN_QUERY,
                description="搜索关键词（合同名称、发票号码、发票代码、收款方、付款方）",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'contract_id', openapi.IN_QUERY,
                description="按合同ID筛选",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'invoice_type', openapi.IN_QUERY,
                description="按发票类型筛选（ordinary/special）",
                type=openapi.TYPE_STRING,
                enum=['ordinary', 'special']
            ),
            openapi.Parameter(
                'invoice_date_start', openapi.IN_QUERY,
                description="开票时间范围开始（YYYY-MM-DD）",
                type=openapi.TYPE_STRING,
                format='date'
            ),
            openapi.Parameter(
                'invoice_date_end', openapi.IN_QUERY,
                description="开票时间范围结束（YYYY-MM-DD）",
                type=openapi.TYPE_STRING,
                format='date'
            ),
            openapi.Parameter(
                'amount_min', openapi.IN_QUERY,
                description="开票金额最小值",
                type=openapi.TYPE_NUMBER
            ),
            openapi.Parameter(
                'amount_max', openapi.IN_QUERY,
                description="开票金额最大值",
                type=openapi.TYPE_NUMBER
            ),
            openapi.Parameter(
                'ordering', openapi.IN_QUERY,
                description="排序字段，支持：invoice_amount, invoice_date, invoice_type, tax_rate, tax_amount, create_datetime, update_datetime, contract__name, payee, payer。前缀-表示倒序",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'page', openapi.IN_QUERY,
                description="页码",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'limit', openapi.IN_QUERY,
                description="每页数量",
                type=openapi.TYPE_INTEGER
            ),
        ],
        responses=invoice_list_responses
    )
    def list(self, request):
        """获取发票列表"""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return ListResponse(data=serializer.data)

    @swagger_auto_schema(
        operation_summary="获取发票详情",
        operation_description="获取指定发票的详细信息",
        responses=invoice_detail_responses
    )
    def retrieve(self, request, pk=None):
        """获取发票详情"""
        invoice = get_object_or_404(Invoice, pk=pk, delete_datetime__isnull=True)
        serializer = self.get_serializer(invoice)
        return StandardResponse(data=serializer.data, msg="获取发票详情成功")

    @swagger_auto_schema(
        operation_summary="创建发票",
        operation_description="创建新的发票记录",
        request_body=InvoiceCreateSerializer,
        responses={201: InvoiceDetailSerializer()}
    )
    def create(self, request):
        """创建发票"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 设置创建者
        serializer.validated_data['creator'] = request.user.username
        invoice = serializer.save()
        
        # 返回详细信息
        detail_serializer = InvoiceDetailSerializer(invoice)
        return CreatedResponse(data=detail_serializer.data, msg="创建发票成功")

    @swagger_auto_schema(
        operation_summary="完整更新发票",
        operation_description="完整更新发票信息",
        request_body=InvoiceUpdateSerializer,
        responses={200: InvoiceDetailSerializer()}
    )
    def update(self, request, pk=None):
        """完整更新发票"""
        invoice = get_object_or_404(Invoice, pk=pk, delete_datetime__isnull=True)
        serializer = self.get_serializer(invoice, data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 设置更新者
        serializer.validated_data['updater'] = request.user.username
        updated_invoice = serializer.save()
        
        # 返回详细信息
        detail_serializer = InvoiceDetailSerializer(updated_invoice)
        return StandardResponse(data=detail_serializer.data, msg="更新发票成功")

    @swagger_auto_schema(
        operation_summary="部分更新发票",
        operation_description="部分更新发票信息",
        request_body=InvoiceUpdateSerializer,
        responses={200: InvoiceDetailSerializer()}
    )
    def partial_update(self, request, pk=None):
        """部分更新发票"""
        invoice = get_object_or_404(Invoice, pk=pk, delete_datetime__isnull=True)
        serializer = self.get_serializer(invoice, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        # 设置更新者
        serializer.validated_data['updater'] = request.user.username
        updated_invoice = serializer.save()
        
        # 返回详细信息
        detail_serializer = InvoiceDetailSerializer(updated_invoice)
        return StandardResponse(data=detail_serializer.data, msg="更新发票成功")

    @swagger_auto_schema(
        operation_summary="删除发票",
        operation_description="删除指定发票（软删除）",
        responses={200: openapi.Response('删除成功')}
    )
    def destroy(self, request, pk=None):
        """删除发票"""
        invoice = get_object_or_404(Invoice, pk=pk, delete_datetime__isnull=True)
        
        # 软删除
        from django.utils import timezone
        invoice.delete_datetime = timezone.now()
        invoice.save()
        
        return StandardResponse(msg="删除发票成功")

    @swagger_auto_schema(
        operation_summary="批量删除发票",
        operation_description="批量删除多个发票",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['invoice_ids'],
            properties={
                'invoice_ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='发票ID列表'
                )
            }
        ),
        responses={200: openapi.Response('批量删除成功')}
    )
    @action(detail=False, methods=['delete'])
    def batch_delete(self, request):
        """批量删除发票"""
        invoice_ids = request.data.get('invoice_ids', [])
        
        if not invoice_ids:
            return StandardResponse(code=4000, msg="请提供要删除的发票ID列表")
        
        # 查找存在的发票
        invoices = Invoice.objects.filter(
            id__in=invoice_ids,
            delete_datetime__isnull=True
        )
        
        if not invoices.exists():
            return StandardResponse(code=4000, msg="未找到要删除的发票")
        
        # 批量软删除
        from django.utils import timezone
        deleted_count = invoices.update(delete_datetime=timezone.now())
        
        return StandardResponse(msg=f"成功删除 {deleted_count} 个发票")

    @swagger_auto_schema(
        operation_summary="发票OCR识别",
        operation_description="上传发票图片进行自动识别，返回识别结果供用户确认。文件会自动保存到附件系统。",
        request_body=InvoiceOCRUploadSerializer,
        responses={
            200: openapi.Response(
                description='识别成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=2000),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, example='发票识别成功'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'recognition_result': openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        'invoice_code': openapi.Schema(type=openapi.TYPE_STRING, description='发票代码'),
                                        'invoice_number': openapi.Schema(type=openapi.TYPE_STRING, description='发票号码'),
                                        'invoice_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='开票日期'),
                                        'invoice_type': openapi.Schema(type=openapi.TYPE_STRING, enum=['ordinary', 'special'], description='发票类型'),
                                        'invoice_amount': openapi.Schema(type=openapi.TYPE_STRING, description='开票金额'),
                                        'tax_rate': openapi.Schema(type=openapi.TYPE_STRING, description='税率'),
                                        'tax_amount': openapi.Schema(type=openapi.TYPE_STRING, description='税额'),
                                        'payee': openapi.Schema(type=openapi.TYPE_STRING, description='收款方'),
                                        'payee_tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='收款方纳税识别号'),
                                        'payer': openapi.Schema(type=openapi.TYPE_STRING, description='付款方'),
                                        'payer_tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='付款方纳税识别号'),
                                        'description': openapi.Schema(type=openapi.TYPE_STRING, description='发票描述'),
                                    }
                                ),
                                'confidence': openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    description='各字段识别置信度（0-1）',
                                    additional_properties=openapi.Schema(type=openapi.TYPE_NUMBER)
                                ),
                                'source_file_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='源文件ID'),
                                'preview_url': openapi.Schema(type=openapi.TYPE_STRING, description='文件预览链接')
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            413: openapi.Response(description='文件过大'),
            422: openapi.Response(description='识别失败')
        }
    )
    @action(detail=False, methods=['post'], url_path='ocr', parser_classes=[MultiPartParser, FormParser])
    def ocr_recognition(self, request):
        """发票OCR识别"""

        # 1. 验证文件
        if 'file' not in request.FILES:
            return StandardResponse(code=4000, msg="请上传发票图片文件")

        file = request.FILES['file']
        contract_id = request.data.get('contract_id')

        # 文件格式验证
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
        if file.content_type not in allowed_types:
            return StandardResponse(
                code=4000,
                msg="不支持的文件格式，请上传JPG、PNG或PDF文件"
            )

        # 文件大小验证（10MB）
        if file.size > 10 * 1024 * 1024:
            return StandardResponse(code=4000, msg="文件过大，请上传小于10MB的文件")

        try:
            # 2. 保存源文件到附件系统
            # 为OCR识别创建临时附件记录，暂时不关联到具体对象
            from django.contrib.contenttypes.models import ContentType
            from django.utils import timezone
            import uuid

            # 使用Invoice的ContentType作为临时关联
            invoice_content_type = ContentType.objects.get_for_model(Invoice)
            temp_object_id = uuid.uuid4()  # 临时ID，后续创建发票时会更新

            attachment = Attachment.objects.create(
                original_name=file.name,
                file_name=file.name,
                file_size=file.size,
                file_type=file.content_type,
                content_type=invoice_content_type,
                object_id=temp_object_id,
                category='invoice_source',
                description='发票OCR识别源文件（待关联）',
                creator=request.user.username
            )

            # 3. 调用OCR服务进行识别
            recognition_result = self._perform_ocr_recognition(file, contract_id)

            # 4. 返回识别结果
            return StandardResponse(
                data={
                    'recognition_result': recognition_result['fields'],
                    'confidence': recognition_result['confidence'],
                    'source_file_id': str(attachment.id),
                    'preview_url': f'/api/v1/attachments/{attachment.id}/download/'
                },
                msg="发票识别成功"
            )

        except OCRServiceException as e:
            logger.error(f"OCR服务异常：{str(e)}")
            return StandardResponse(
                code=4220,
                msg=f"识别失败：{str(e)}",
                data={
                    'error_type': 'ocr_service_error',
                    'suggestions': [
                        '请确保图片清晰度足够',
                        '检查发票是否完整',
                        '尝试重新拍照或扫描'
                    ]
                }
            )
        except Exception as e:
            logger.error(f"OCR识别异常：{str(e)}")
            return StandardResponse(
                code=5000,
                msg="系统错误，请稍后重试",
                data={
                    'error_type': 'system_error',
                    'suggestions': [
                        '请稍后重试',
                        '如问题持续存在，请联系管理员'
                    ]
                }
            )

    def _perform_ocr_recognition(self, file, contract_id=None):
        """执行OCR识别"""
        try:
            # 获取OCR服务实例
            ocr_service = OCRServiceFactory.get_service('baidu')

            # 执行识别
            result = ocr_service.recognize_invoice(file)

            # 如果提供了contract_id，可以预填充相关信息
            if contract_id:
                try:
                    contract = Contract.objects.get(id=contract_id, delete_datetime__isnull=True)
                    result = self._enhance_with_contract_info(result, contract)
                except Contract.DoesNotExist:
                    logger.warning(f"合同不存在：{contract_id}")

            return result

        except Exception as e:
            logger.error(f"OCR识别执行失败：{str(e)}")
            raise OCRServiceException(f"识别服务异常：{str(e)}")

    def _enhance_with_contract_info(self, result, contract):
        """根据合同信息增强识别结果"""
        try:
            fields = result['fields']

            # 如果识别结果中的付款方为空，尝试从合同相对方中获取
            if not fields.get('payer') and contract.partners.exists():
                partner = contract.partners.first()
                if partner:
                    fields['payer'] = partner.name
                    if hasattr(partner, 'tax_id') and partner.tax_id:
                        fields['payer_tax_id'] = partner.tax_id

            # 可以根据合同类型推断发票相关信息
            if contract.category == 'sales':
                # 销售合同，当前公司是收款方
                if not fields.get('payee'):
                    fields['payee'] = '本公司'  # 可以从系统配置中获取
            elif contract.category == 'procurement':
                # 采购合同，当前公司是付款方
                if not fields.get('payer'):
                    fields['payer'] = '本公司'  # 可以从系统配置中获取

            result['fields'] = fields
            return result

        except Exception as e:
            logger.error(f"合同信息增强失败：{str(e)}")
            return result
