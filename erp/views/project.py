from rest_framework import viewsets
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType

from erp.models import Project, Attachment
from erp.serializers.project import (
    ProjectSerializer,
    ProjectListSerializer,
    ProjectCreateUpdateSerializer,
    ProjectPartialUpdateSerializer,
    ProjectDetailSerializer,
    ProjectSimpleSerializer
)
from erp.filters import ProjectFilter
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.json_response import StandardResponse, ListResponse, ErrorResponse

class ProjectViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """项目管理视图集"""
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_class = ProjectFilter
    ordering_fields = ['create_datetime', 'update_datetime', 'start_date', 'end_date', 'progress', 'expected_profit_rate', 'code']
    ordering = ['-create_datetime']

    def get_queryset(self):
        """获取查询集，只返回未删除的记录"""
        return Project.objects.filter(delete_datetime__isnull=True).select_related('customer')

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action in ['create', 'update']:
            return ProjectCreateUpdateSerializer
        elif self.action == 'partial_update':
            return ProjectPartialUpdateSerializer
        elif self.action in ['list', 'retrieve']:
            return ProjectDetailSerializer
        elif self.action == 'simple_list':
            return ProjectSimpleSerializer
        return ProjectSerializer

    @swagger_auto_schema(
        operation_summary="获取项目列表",
        operation_description="获取项目列表，支持分页、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description='排序字段，支持：create_datetime, update_datetime,start_date, end_date, progress, expected_profit_rate, code。前缀-表示倒序，如：-create_datetime',
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={200: ProjectDetailSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """获取项目列表"""
        return super().list(request, *args, **kwargs)



    @swagger_auto_schema(
        operation_summary="创建项目",
        operation_description="创建新项目"
    )
    def create(self, request, *args, **kwargs):
        """创建项目"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="获取项目详情",
        operation_description="根据ID获取项目详细信息"
    )
    def retrieve(self, request, *args, **kwargs):
        """获取项目详情"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="完整更新项目",
        operation_description="更新项目信息"
    )
    def update(self, request, *args, **kwargs):
        """完整更新项目"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="部分更新项目",
        operation_description="部分更新项目信息（PATCH方法，只需要提供要更新的字段，所有字段都是可选的，项目编码不能修改）",
        request_body=ProjectPartialUpdateSerializer,
        responses={200: "更新成功"}
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新项目"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="删除项目",
        operation_description="软删除项目（逻辑删除）"
    )
    def destroy(self, request, *args, **kwargs):
        """软删除项目"""
        try:
            instance = self.get_object()
            instance.delete()  # 调用模型的软删除方法
            from erp.utils.json_response import StandardResponse
            return StandardResponse(msg="项目删除成功")
        except Exception as e:
            from erp.utils.json_response import ErrorResponse
            return ErrorResponse(msg=f"项目删除失败: {str(e)}", code=5000)

    @action(detail=False, methods=['get'], filter_backends=[], filterset_class=None)
    @swagger_auto_schema(
        operation_summary="获取项目简单列表",
        operation_description="获取项目简单列表，用于下拉选择等场景，支持分页和搜索",
        manual_parameters=[
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description='搜索关键词，支持按项目编码和项目名称搜索',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description='页码',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description='每页数量',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
        ],
        responses={200: ProjectSimpleSerializer(many=True)}
    )
    def simple_list(self, request):
        """获取项目简单列表"""
        queryset = Project.objects.filter(delete_datetime__isnull=True).order_by('-create_datetime')

        # 搜索功能
        search = request.query_params.get('search', None)
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(code__icontains=search) | Q(name__icontains=search)
            )

        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request, view=self)
        if page is not None:
            serializer = ProjectSimpleSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        # 如果没有分页，返回所有数据
        serializer = ProjectSimpleSerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取项目简单列表成功")

    @action(detail=False, methods=['get'], filter_backends=[], pagination_class=None)
    @swagger_auto_schema(
        operation_summary="获取项目统计信息",
        operation_description="获取项目统计信息",
        manual_parameters=[],
        responses={200: openapi.Response(
            description="统计信息",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'total_count': openapi.Schema(type=openapi.TYPE_INTEGER, description="项目总数"),
                    'status_stats': openapi.Schema(type=openapi.TYPE_OBJECT, description="状态统计"),
                    'type_stats': openapi.Schema(type=openapi.TYPE_OBJECT, description="类型统计"),
                    'overdue_count': openapi.Schema(type=openapi.TYPE_INTEGER, description="逾期项目数"),
                }
            )
        )}
    )
    def statistics(self, request):
        """获取项目统计信息"""
        # 直接查询，不使用过滤器
        queryset = Project.objects.filter(delete_datetime__isnull=True)

        # 总数统计
        total_count = queryset.count()

        # 状态统计
        status_stats = {}
        for choice in Project.PROJECT_STATUS_CHOICES:
            status_code, status_name = choice
            count = queryset.filter(status=status_code).count()
            status_stats[status_code] = {
                'name': status_name,
                'count': count
            }

        # 类型统计
        type_stats = {}
        for choice in Project.PROJECT_TYPE_CHOICES:
            type_code, type_name = choice
            count = queryset.filter(type=type_code).count()
            type_stats[type_code] = {
                'name': type_name,
                'count': count
            }

        # 逾期项目统计
        from django.utils import timezone
        today = timezone.now().date()
        overdue_count = queryset.filter(
            end_date__lt=today,
            status__in=['preparing', 'in_progress', 'paused']
        ).count()

        return StandardResponse(data={
            'total_count': total_count,
            'status_stats': status_stats,
            'type_stats': type_stats,
            'overdue_count': overdue_count,
        }, msg="获取项目统计信息成功")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="更新项目状态",
        operation_description="更新项目状态，如果状态为completed，需要检查是否上传了试算表附件",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'status': openapi.Schema(type=openapi.TYPE_STRING, description="新状态: preparing, in_progress, paused, completed, terminated"),
                'completion_remark': openapi.Schema(type=openapi.TYPE_STRING, description="完成备注（状态为completed时可选）")
            },
            required=['status']
        )
    )
    def update_status(self, request, pk=None):
        """更新项目状态"""
        instance = self.get_object()
        new_status = request.data.get('status')
        completion_remark = request.data.get('completion_remark', '')

        if not new_status:
            return ErrorResponse(msg="状态不能为空", code=4000)

        # 验证状态值
        valid_statuses = [choice[0] for choice in Project.PROJECT_STATUS_CHOICES]
        if new_status not in valid_statuses:
            return ErrorResponse(msg="无效的状态值", code=4000)

        # 如果状态更新为completed，需要检查试算表附件
        if new_status == 'completed':


            # 获取项目的试算表附件
            content_type = ContentType.objects.get_for_model(Project)
            trial_balance_attachments = Attachment.objects.filter(
                content_type=content_type,
                object_id=instance.id,
                category='project_trial_balance',
                delete_datetime__isnull=True
            )

            if not trial_balance_attachments.exists():
                return ErrorResponse(msg="项目完成前必须上传试算表附件", code=4000)

            # 更新完成时间和备注
            instance.completion_datetime = timezone.now()
            instance.completion_remark = completion_remark

        instance.status = new_status
        instance.save()

        serializer = self.get_serializer(instance)
        return StandardResponse(data=serializer.data, msg="项目状态更新成功")


