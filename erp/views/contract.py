from rest_framework import viewsets, filters
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone

from erp.models.contract import Contract
from erp.serializers.contract import (
    ContractSerializer, ContractListSerializer, ContractCreateSerializer,
    ContractUpdateSerializer, ContractPartialUpdateSerializer, ContractStatusUpdateSerializer
)
from erp.utils.json_response import ListResponse, ErrorResponse, StandardResponse, CreatedResponse
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.filters import ContractFilter
from erp.swagger.response_schemas import (
    contract_list_responses, contract_create_responses, contract_retrieve_responses,
    contract_update_responses, contract_delete_responses
)


class ContractViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """合同管理视图集"""

    serializer_class = ContractSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = ContractFilter
    ordering_fields = ['create_datetime', 'update_datetime', 'sign_date', 'start_date', 'end_date', 'amount', 'code']
    ordering = ['-create_datetime']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ContractListSerializer
        elif self.action == 'create':
            return ContractCreateSerializer
        elif self.action == 'update':
            return ContractUpdateSerializer
        elif self.action == 'partial_update':
            return ContractPartialUpdateSerializer
        return ContractSerializer

    def get_queryset(self):
        """获取查询集，只返回未删除的记录"""
        return Contract.objects.filter(delete_datetime__isnull=True).select_related(
            'project'
        ).prefetch_related('partners')

    @swagger_auto_schema(
        operation_summary="获取合同列表",
        operation_description="获取合同列表，支持分页、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description='排序字段，支持：create_datetime, update_datetime, sign_date, start_date, end_date, amount, code。前缀-表示倒序，如：-create_datetime',
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses=contract_list_responses
    )
    def list(self, request, *args, **kwargs):
        """获取合同列表"""
        return super().list(request, *args, **kwargs)
    

    
    @swagger_auto_schema(
        operation_summary="创建合同",
        operation_description="创建新的合同",
        request_body=ContractCreateSerializer,
        responses=contract_create_responses
    )
    def create(self, request):
        """创建合同"""
        try:
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            contract = serializer.save()

            # 返回完整的合同信息
            response_serializer = ContractSerializer(contract)
            return CreatedResponse(data=response_serializer.data, msg="合同创建成功")

        except Exception as e:
            return ErrorResponse(msg=f"创建合同失败: {str(e)}")
    
    @swagger_auto_schema(
        operation_summary="获取合同详情",
        operation_description="根据ID获取合同详细信息",
        responses=contract_retrieve_responses
    )
    def retrieve(self, request, pk=None):
        """获取合同详情"""
        try:
            contract = self.get_object()
            serializer = self.get_serializer(contract)
            return StandardResponse(data=serializer.data, msg="合同详情获取成功")
        except Exception as e:
            return ErrorResponse(msg=f"获取合同详情失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="完整更新合同",
        operation_description="完整更新合同信息（PUT方法，不允许修改合同编号、类别、客户、供应商、项目）",
        request_body=ContractUpdateSerializer,
        responses=contract_update_responses
    )
    def update(self, request, pk=None):
        """完整更新合同"""
        try:
            contract = self.get_object()
            serializer = self.get_serializer(contract, data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            updated_contract = serializer.save()

            # 返回完整的合同信息
            response_serializer = ContractSerializer(updated_contract)
            return StandardResponse(data=response_serializer.data, msg="合同更新成功")

        except Exception as e:
            return ErrorResponse(msg=f"更新合同失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="部分更新合同",
        operation_description="部分更新合同信息（PATCH方法，只需要提供要更新的字段，不允许修改合同编号、类别、客户、供应商、项目）",
        request_body=ContractPartialUpdateSerializer,
        responses=contract_update_responses
    )
    def partial_update(self, request, pk=None):
        """部分更新合同"""
        try:
            contract = self.get_object()
            serializer = self.get_serializer(contract, data=request.data, partial=True)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            updated_contract = serializer.save()

            # 返回完整的合同信息
            response_serializer = ContractSerializer(updated_contract)
            return StandardResponse(data=response_serializer.data, msg="合同更新成功")

        except Exception as e:
            return ErrorResponse(msg=f"更新合同失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="删除合同",
        operation_description="软删除合同记录",
        responses=contract_delete_responses
    )
    def destroy(self, request, pk=None):
        """删除合同"""
        try:
            contract = self.get_object()
            contract.delete()  # 使用重写的delete方法进行软删除
            return StandardResponse(msg="合同删除成功")

        except Exception as e:
            return ErrorResponse(msg=f"删除合同失败: {str(e)}")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="更新合同状态",
        operation_description="更新合同的签订状态和/或履约状态",
        request_body=ContractStatusUpdateSerializer,
        responses={
            200: openapi.Response(
                description="状态更新成功",
                schema=ContractSerializer
            ),
            400: openapi.Response(description="请求参数错误"),
            404: openapi.Response(description="合同不存在"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def update_status(self, request, pk=None):
        """更新合同状态"""
        try:
            contract = self.get_object()

            # 验证请求数据
            serializer = ContractStatusUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors, code=4000)

            # 更新状态字段
            validated_data = serializer.validated_data
            updated_fields = []

            if 'sign_status' in validated_data:
                old_sign_status = contract.sign_status
                contract.sign_status = validated_data['sign_status']
                updated_fields.append(f"签订状态: {old_sign_status} → {contract.sign_status}")

            if 'performance_status' in validated_data:
                old_performance_status = contract.performance_status
                contract.performance_status = validated_data['performance_status']
                updated_fields.append(f"履约状态: {old_performance_status} → {contract.performance_status}")

            # 保存更改
            contract.save(update_fields=['sign_status', 'performance_status', 'update_datetime'])

            # 返回更新后的合同信息
            response_serializer = ContractSerializer(contract)

            return StandardResponse(
                data=response_serializer.data,
                msg=f"合同状态更新成功: {', '.join(updated_fields)}"
            )

        except Exception as e:
            return ErrorResponse(msg=f"更新合同状态失败: {str(e)}", code=5000)


