from rest_framework import viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import Contract
from erp.serializers.contract_document import ContractDocumentListSerializer
from erp.filters import ContractDocumentFilter
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.json_response import ListResponse
from erp.utils.pagination import CustomPagination


class ContractDocumentViewSet(JWTRequiredMixin, viewsets.ReadOnlyModelViewSet):
    """合同文档管理视图集"""
    
    queryset = Contract.objects.filter(delete_datetime__isnull=True)
    serializer_class = ContractDocumentListSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ContractDocumentFilter
    search_fields = ['name', 'code']
    ordering_fields = [
        'name', 'code', 'total_amount',
        'create_datetime', 'update_datetime'
    ]
    ordering = ['-update_datetime']

    @swagger_auto_schema(
        operation_summary="获取合同文档管理列表",
        operation_description="获取合同列表，包含文档数量统计信息，专用于文档管理功能",
        manual_parameters=[
            openapi.Parameter(
                'search', openapi.IN_QUERY,
                description="搜索关键词（合同名称、编号）",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'category', openapi.IN_QUERY,
                description="按合同类别筛选",
                type=openapi.TYPE_STRING,
                enum=['sales', 'procurement']
            ),
            openapi.Parameter(
                'sign_status', openapi.IN_QUERY,
                description="按签订状态筛选",
                type=openapi.TYPE_STRING,
                enum=['unsigned', 'communicating', 'signing', 'signed', 'terminating']
            ),
            openapi.Parameter(
                'performance_status', openapi.IN_QUERY,
                description="按履约状态筛选",
                type=openapi.TYPE_STRING,
                enum=['not_performed', 'performing', 'performed']
            ),
            openapi.Parameter(
                'project_id', openapi.IN_QUERY,
                description="按项目ID筛选",
                type=openapi.TYPE_STRING,
                format='uuid'
            ),
            openapi.Parameter(
                'partner_id', openapi.IN_QUERY,
                description="按相对方ID筛选",
                type=openapi.TYPE_STRING,
                format='uuid'
            ),
            openapi.Parameter(
                'ordering', openapi.IN_QUERY,
                description="排序字段，支持：name, code, total_amount, document_count, create_datetime, update_datetime。前缀-表示倒序",
                type=openapi.TYPE_STRING
            ),
            openapi.Parameter(
                'page', openapi.IN_QUERY,
                description="页码",
                type=openapi.TYPE_INTEGER
            ),
            openapi.Parameter(
                'limit', openapi.IN_QUERY,
                description="每页数量",
                type=openapi.TYPE_INTEGER
            ),
        ],
        responses={
            200: openapi.Response(
                description='获取成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=2000),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'items': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                            'name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
                                            'code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
                                            'category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别'),
                                            'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别显示名'),
                                            'status': openapi.Schema(type=openapi.TYPE_STRING, description='合同状态'),
                                            'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同状态显示名'),
                                            'total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额'),
                                            'project_info': openapi.Schema(
                                                type=openapi.TYPE_OBJECT,
                                                properties={
                                                    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                                    'name': openapi.Schema(type=openapi.TYPE_STRING),
                                                    'code': openapi.Schema(type=openapi.TYPE_STRING)
                                                },
                                                nullable=True
                                            ),
                                            'partners': openapi.Schema(
                                                type=openapi.TYPE_ARRAY,
                                                items=openapi.Schema(
                                                    type=openapi.TYPE_OBJECT,
                                                    properties={
                                                        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                                        'name': openapi.Schema(type=openapi.TYPE_STRING)
                                                    }
                                                )
                                            ),
                                            'document_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='文档数量'),
                                            'document_categories': openapi.Schema(
                                                type=openapi.TYPE_OBJECT,
                                                description='按类别统计的文档数量',
                                                additional_properties=openapi.Schema(type=openapi.TYPE_INTEGER)
                                            ),
                                            'latest_document': openapi.Schema(
                                                type=openapi.TYPE_OBJECT,
                                                properties={
                                                    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                                    'original_name': openapi.Schema(type=openapi.TYPE_STRING),
                                                    'category_display': openapi.Schema(type=openapi.TYPE_STRING),
                                                    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time')
                                                },
                                                nullable=True
                                            ),
                                            'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                                            'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                                            'creator': openapi.Schema(type=openapi.TYPE_STRING),
                                            'creator_name': openapi.Schema(type=openapi.TYPE_STRING)
                                        }
                                    )
                                ),
                                'page': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'pages': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'limit': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'is_next': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                'is_previous': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            500: openapi.Response(description='服务器内部错误')
        }
    )
    def list(self, request):
        """获取合同文档管理列表"""
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return ListResponse(data=serializer.data)
