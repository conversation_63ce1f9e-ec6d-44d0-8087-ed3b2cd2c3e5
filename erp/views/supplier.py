from django.db.models import Q
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import ValidationError
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import Supplier, SupplierPaymentInfo
from erp.serializers.supplier import (
    SupplierSerializer, SupplierListSerializer, SupplierCreateUpdateSerializer,
    SupplierPartialUpdateSerializer, SupplierQuerySerializer, SupplierPaymentInfoSerializer,
    SupplierPaymentInfoUpdateSerializer, SupplierStatusUpdateSerializer, SupplierSimpleSerializer
)
from erp.filters import SupplierFilter
from erp.utils.json_response import ListResponse, ErrorResponse, StandardResponse, CreatedResponse
from erp.utils.pagination import CustomPagination
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin
from erp.utils.excel_export import SupplierExcelExporter
from erp.swagger.response_schemas import excel_export_responses


class SupplierViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """供应商管理视图集"""

    queryset = Supplier.objects.filter(delete_datetime__isnull=True)
    serializer_class = SupplierSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = SupplierFilter
    ordering_fields = ['create_datetime', 'update_datetime', 'name', 'type', 'industry', 'code']
    ordering = ['-create_datetime']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return SupplierListSerializer
        elif self.action == 'create':
            return SupplierCreateUpdateSerializer
        elif self.action == 'update':
            return SupplierCreateUpdateSerializer
        elif self.action == 'partial_update':
            return SupplierPartialUpdateSerializer
        elif self.action == 'simple_list':
            return SupplierSimpleSerializer
        return SupplierSerializer

    @swagger_auto_schema(
        operation_summary="获取供应商列表",
        operation_description="获取供应商列表，支持分页、过滤和排序",
        manual_parameters=[
            openapi.Parameter(
                'ordering',
                openapi.IN_QUERY,
                description='排序字段，支持：create_datetime, update_datetime, name, type, industry, code。前缀-表示倒序，如：-create_datetime',
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={200: SupplierListSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        """获取供应商列表"""
        return super().list(request, *args, **kwargs)


    @swagger_auto_schema(
        operation_summary="创建供应商",
        operation_description="创建新的供应商",
        request_body=SupplierCreateUpdateSerializer,
        responses={201: SupplierSerializer}
    )
    def create(self, request, *args, **kwargs):
        """创建供应商"""
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                instance = serializer.save()
                response_serializer = SupplierSerializer(instance)
                return CreatedResponse(data=response_serializer.data, msg="供应商创建成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"供应商创建失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"供应商创建失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="获取供应商详情",
        operation_description="根据ID获取供应商详细信息",
        responses={200: SupplierSerializer}
    )
    def retrieve(self, request, *args, **kwargs):
        """获取供应商详情"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return StandardResponse(data=serializer.data, msg="供应商详情获取成功")
        except Exception as e:
            return ErrorResponse(msg=f"获取供应商详情失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="完全更新供应商",
        operation_description="完全更新供应商信息（PUT方法）",
        request_body=SupplierCreateUpdateSerializer,
        responses={200: SupplierSerializer}
    )
    def update(self, request, *args, **kwargs):
        """更新供应商"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data)
            if serializer.is_valid():
                instance = serializer.save()
                response_serializer = SupplierSerializer(instance)
                return StandardResponse(data=response_serializer.data, msg="供应商更新成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"供应商更新失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"供应商更新失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="部分更新供应商",
        operation_description="部分更新供应商信息（PATCH方法）",
        request_body=SupplierPartialUpdateSerializer,
        responses={200: SupplierSerializer}
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新供应商"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            if serializer.is_valid():
                instance = serializer.save()
                response_serializer = SupplierSerializer(instance)
                return StandardResponse(data=response_serializer.data, msg="供应商更新成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"供应商更新失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"供应商更新失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="删除供应商",
        operation_description="软删除供应商",
        responses={204: "删除成功"}
    )
    def destroy(self, request, *args, **kwargs):
        """删除供应商"""
        try:
            instance = self.get_object()
            # 软删除
            from django.utils import timezone
            instance.delete_datetime = timezone.now()
            instance.save()
            return StandardResponse(msg="供应商删除成功")
        except Exception as e:
            return ErrorResponse(msg=f"供应商删除失败: {str(e)}")

    @action(detail=True, methods=['get'])
    @swagger_auto_schema(
        operation_summary="获取供应商收款信息",
        operation_description="获取指定供应商的所有收款信息",
        responses={200: SupplierPaymentInfoSerializer(many=True)}
    )
    def payment_infos(self, request, pk=None):
        """获取供应商收款信息"""
        try:
            supplier = self.get_object()
            payment_infos = supplier.payment_infos.filter(delete_datetime__isnull=True)
            serializer = SupplierPaymentInfoSerializer(payment_infos, many=True)
            return ListResponse(data=serializer.data, msg="收款信息获取成功")
        except Exception as e:
            return ErrorResponse(msg=f"获取收款信息失败: {str(e)}")

    @action(detail=True, methods=['post'])
    @swagger_auto_schema(
        operation_summary="添加供应商收款信息",
        operation_description="为指定供应商添加收款信息",
        request_body=SupplierPaymentInfoSerializer,
        responses={201: SupplierPaymentInfoSerializer}
    )
    def add_payment_info(self, request, pk=None):
        """添加供应商收款信息"""
        try:
            supplier = self.get_object()
            serializer = SupplierPaymentInfoSerializer(data=request.data)

            if serializer.is_valid():
                serializer.save(supplier=supplier)
                return CreatedResponse(data=serializer.data, msg="收款信息添加成功")
            return ErrorResponse(msg="数据验证失败", data=serializer.errors)
        except Exception as e:
            return ErrorResponse(msg=f"添加收款信息失败: {str(e)}")

    @action(detail=True, methods=['put'], url_path='payment_infos/(?P<payment_id>[^/.]+)/update')
    @swagger_auto_schema(
        operation_summary="完整更新供应商收款信息",
        operation_description="完整更新指定供应商的指定收款信息记录（PUT方法，需要提供所有必填字段）",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['bank_name', 'account_number', 'is_default'],
            properties={
                'bank_name': openapi.Schema(type=openapi.TYPE_STRING, description='开户银行', example='中国银行北京分行'),
                'account_number': openapi.Schema(type=openapi.TYPE_STRING, description='银行账号', example='****************'),
                'is_default': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否默认账户', example=True),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注', example='主要收款账户'),
            }
        ),
        responses={
            200: openapi.Response(
                description="更新成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=200),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, example='收款信息更新成功'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, example='uuid'),
                                'bank_name': openapi.Schema(type=openapi.TYPE_STRING, example='中国银行北京分行'),
                                'account_number': openapi.Schema(type=openapi.TYPE_STRING, example='****************'),
                                'is_default': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                                'remark': openapi.Schema(type=openapi.TYPE_STRING, example='主要收款账户'),
                                'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, example='2025-07-14 10:30:00'),
                                'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, example='2025-07-14 15:45:00'),
                                'creator': openapi.Schema(type=openapi.TYPE_STRING, example='admin'),
                                'updater': openapi.Schema(type=openapi.TYPE_STRING, example='admin'),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description="数据验证失败"),
            404: openapi.Response(description="收款信息不存在"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def update_payment_info(self, request, pk=None, payment_id=None):
        """完整更新供应商收款信息（PUT）"""
        try:
            supplier = self.get_object()
            payment_info = supplier.payment_infos.get(id=payment_id, delete_datetime__isnull=True)

            serializer = SupplierPaymentInfoUpdateSerializer(payment_info, data=request.data)
            if serializer.is_valid():
                # 设置更新者信息
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username

                # 保存更新（模型的save方法会自动处理默认账户逻辑）
                updated_payment_info = serializer.save()

                # 返回更新后的完整数据
                response_serializer = SupplierPaymentInfoSerializer(updated_payment_info)
                return StandardResponse(data=response_serializer.data, msg="收款信息更新成功")

            return ErrorResponse(msg=serializer.errors)
        except SupplierPaymentInfo.DoesNotExist:
            return ErrorResponse(msg="收款信息不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"更新收款信息失败: {str(e)}")

    @action(detail=True, methods=['delete'], url_path='payment_infos/(?P<payment_id>[^/.]+)')
    @swagger_auto_schema(
        operation_summary="删除供应商收款信息",
        operation_description="删除指定的收款信息。如果删除后只剩一条收款信息，会自动将其设置为默认。",
        responses={204: "删除成功"}
    )
    def delete_payment_info(self, request, pk=None, payment_id=None):
        """删除供应商收款信息"""
        from django.db import transaction
        from django.utils import timezone

        try:
            supplier = self.get_object()
            payment_info = supplier.payment_infos.get(id=payment_id, delete_datetime__isnull=True)

            with transaction.atomic():
                # 软删除
                payment_info.delete_datetime = timezone.now()
                payment_info.save()

                # 检查该供应商剩余的收款信息数量
                remaining_payment_infos = supplier.payment_infos.filter(delete_datetime__isnull=True)
                remaining_count = remaining_payment_infos.count()

                # 如果只剩下一条记录，自动将其设置为默认
                if remaining_count == 1:
                    last_payment_info = remaining_payment_infos.first()
                    if not last_payment_info.is_default:
                        last_payment_info.is_default = True
                        last_payment_info.save()

            return StandardResponse(msg="收款信息删除成功")
        except SupplierPaymentInfo.DoesNotExist:
            return ErrorResponse(msg="收款信息不存在")
        except Exception as e:
            return ErrorResponse(msg=f"删除收款信息失败: {str(e)}")

    @action(detail=True, methods=['post'], url_path='payment_infos/(?P<payment_id>[^/.]+)/set_default')
    @swagger_auto_schema(
        operation_summary="设置默认收款信息",
        operation_description="将指定的收款信息设置为默认",
        responses={200: SupplierPaymentInfoSerializer}
    )
    def set_default_payment_info(self, request, pk=None, payment_id=None):
        """设置默认收款信息"""
        try:
            supplier = self.get_object()
            payment_info = supplier.payment_infos.get(id=payment_id, delete_datetime__isnull=True)

            # 设置为默认（save方法会自动处理其他账户的默认状态）
            payment_info.is_default = True
            payment_info.save()

            serializer = SupplierPaymentInfoSerializer(payment_info)
            return ListResponse(data=serializer.data, msg="默认收款信息设置成功")
        except SupplierPaymentInfo.DoesNotExist:
            return ErrorResponse(msg="收款信息不存在")
        except Exception as e:
            return ErrorResponse(msg=f"设置默认收款信息失败: {str(e)}")

    @action(detail=False, methods=['get'], authentication_classes=[], permission_classes=[])
    @swagger_auto_schema(
        operation_summary="导出供应商数据Excel",
        operation_description="导出所有供应商数据到Excel文件（方案二：每个收款信息单独一行），支持多选过滤参数",
        responses=excel_export_responses
    )
    def export_excel(self, request):
        """导出供应商数据到Excel（方案二：多行方式）"""
        try:
            # 获取筛选后的供应商数据（不分页）
            queryset = self.filter_queryset(self.get_queryset())

            # 预加载相关数据以提高性能（只包含未删除的收款信息）
            from django.db import models
            suppliers = queryset.prefetch_related(
                models.Prefetch(
                    'payment_infos',
                    queryset=SupplierPaymentInfo.objects.filter(delete_datetime__isnull=True)
                )
            ).all()

            # 创建Excel导出器
            exporter = SupplierExcelExporter()

            # 使用方案二：多行方式导出Excel
            return exporter.export_suppliers_multi_row(suppliers)

        except Exception as e:
            return ErrorResponse(msg=f"导出Excel失败: {str(e)}")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="更新供应商状态",
        operation_description="更新指定供应商的状态",
        request_body=SupplierStatusUpdateSerializer,
        responses={
            200: openapi.Response(
                description="状态更新成功",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应代码'),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, description='供应商ID'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='更新后的状态'),
                                'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='状态显示名称'),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description="数据验证失败"),
            404: openapi.Response(description="供应商不存在"),
            500: openapi.Response(description="服务器内部错误")
        }
    )
    def update_status(self, request, pk=None):
        """更新供应商状态"""
        try:
            supplier = self.get_object()
            serializer = SupplierStatusUpdateSerializer(data=request.data)

            if not serializer.is_valid():
                return ErrorResponse(msg=serializer.errors)

            # 更新状态
            new_status = serializer.validated_data['status']
            supplier.status = new_status

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                supplier.updater = request.user.username

            supplier.save()

            # 返回更新后的状态信息
            response_data = {
                'id': str(supplier.id),
                'status': supplier.status,
                'status_display': supplier.get_status_display()
            }

            return StandardResponse(data=response_data, msg="供应商状态更新成功")

        except Supplier.DoesNotExist:
            return ErrorResponse(msg="供应商不存在", code=4004)
        except Exception as e:
            # 检查是否是因为对象不存在导致的错误
            if "No Supplier matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="供应商不存在", code=4004)
            return ErrorResponse(msg=f"更新供应商状态失败: {str(e)}")

    @action(detail=False, methods=['get'], filter_backends=[], filterset_class=None)
    @swagger_auto_schema(
        operation_summary="获取供应商简单列表",
        operation_description="获取供应商简单列表，用于下拉选择等场景，支持分页和搜索",
        manual_parameters=[
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description='搜索关键词，支持按供应商编码和供应商名称搜索',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'page',
                openapi.IN_QUERY,
                description='页码',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'limit',
                openapi.IN_QUERY,
                description='每页数量',
                type=openapi.TYPE_INTEGER,
                required=False
            ),
        ],
        responses={200: SupplierSimpleSerializer(many=True)}
    )
    def simple_list(self, request):
        """获取供应商简单列表"""
        queryset = Supplier.objects.filter(delete_datetime__isnull=True).order_by('-create_datetime')

        # 搜索功能
        search = request.query_params.get('search', None)
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(code__icontains=search) | Q(name__icontains=search)
            )

        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request, view=self)
        if page is not None:
            serializer = SupplierSimpleSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        # 如果没有分页，返回所有数据
        serializer = SupplierSimpleSerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取供应商简单列表成功")
