from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import AdministrativeDivision
from erp.serializers.administrative_division import ProvinceSerializer, CitySerializer
from erp.utils.json_response import StandardResponse, ErrorResponse


@swagger_auto_schema(
    method='get',
    operation_summary="获取省份列表",
    operation_description="获取所有省/直辖市/自治区列表",
    responses={
        200: openapi.Response(
            description="成功",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='状态码', example=2000),
                    'msg': openapi.Schema(type=openapi.TYPE_STRING, description='消息'),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'code': openapi.Schema(type=openapi.TYPE_STRING, description='省份代码'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='省份名称'),
                            }
                        )
                    )
                }
            )
        )
    }
)
@api_view(['GET'])
def province_list(request):
    """获取省份列表"""
    try:
        provinces = AdministrativeDivision.get_provinces()
        serializer = ProvinceSerializer(provinces, many=True)
        return StandardResponse(
            data=serializer.data,
            msg='获取省份列表成功'
        )
    except Exception as e:
        return ErrorResponse(
            code=5000,
            msg=f"获取省份列表失败: {str(e)}"
        )


@swagger_auto_schema(
    method='get',
    operation_summary="获取城市列表",
    operation_description="根据省份代码获取该省下的城市列表",
    manual_parameters=[
        openapi.Parameter(
            'province_code',
            openapi.IN_PATH,
            description="省份代码（6位数字）",
            type=openapi.TYPE_STRING,
            required=True,
            pattern=r'^\d{6}$'
        )
    ],
    responses={
        200: openapi.Response(
            description="成功",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='状态码', example=2000),
                    'msg': openapi.Schema(type=openapi.TYPE_STRING, description='消息'),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'code': openapi.Schema(type=openapi.TYPE_STRING, description='城市代码'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='城市名称'),
                                'parent_code': openapi.Schema(type=openapi.TYPE_STRING, description='省份代码'),
                            }
                        )
                    )
                }
            )
        ),
        400: openapi.Response(description="参数错误"),
        404: openapi.Response(description="省份不存在")
    }
)
@api_view(['GET'])
def city_list(request, province_code):
    """根据省份代码获取城市列表"""
    try:
        # 验证省份代码格式
        if not province_code.isdigit() or len(province_code) != 6:
            return ErrorResponse(
                code=4000,
                msg="省份代码格式错误，应为6位数字"
            )

        # 验证省份是否存在
        try:
            province = AdministrativeDivision.objects.get(code=province_code, level='province')
        except AdministrativeDivision.DoesNotExist:
            return ErrorResponse(
                code=4004,
                msg="省份不存在"
            )

        # 获取城市列表
        cities = AdministrativeDivision.get_cities_by_province(province_code)
        serializer = CitySerializer(cities, many=True)

        return StandardResponse(
            data=serializer.data,
            msg=f"获取{province.name}城市列表成功"
        )
    except Exception as e:
        return ErrorResponse(
            code=5000,
            msg=f"获取城市列表失败: {str(e)}"
        )


@swagger_auto_schema(
    method='get',
    operation_summary="获取区县列表",
    operation_description="根据城市代码获取该市下的区县列表",
    manual_parameters=[
        openapi.Parameter(
            'city_code',
            openapi.IN_PATH,
            description="城市代码（6位数字）",
            type=openapi.TYPE_STRING,
            required=True,
            pattern=r'^\d{6}$'
        )
    ],
    responses={
        200: openapi.Response(
            description="成功",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='状态码', example=2000),
                    'msg': openapi.Schema(type=openapi.TYPE_STRING, description='消息'),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'code': openapi.Schema(type=openapi.TYPE_STRING, description='区县代码'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='区县名称'),
                                'parent_code': openapi.Schema(type=openapi.TYPE_STRING, description='城市代码'),
                            }
                        )
                    )
                }
            )
        ),
        400: openapi.Response(description="参数错误"),
        404: openapi.Response(description="城市不存在")
    }
)
@api_view(['GET'])
def district_list(request, city_code):
    """根据城市代码获取区县列表"""
    try:
        # 验证城市代码格式
        if not city_code.isdigit() or len(city_code) != 6:
            return ErrorResponse(
                code=4000,
                msg="城市代码格式错误，应为6位数字"
            )

        # 验证城市是否存在
        try:
            city = AdministrativeDivision.objects.get(code=city_code, level='city')
        except AdministrativeDivision.DoesNotExist:
            return ErrorResponse(
                code=4004,
                msg="城市不存在"
            )

        # 获取区县列表
        from erp.serializers.administrative_division import DistrictSerializer
        districts = AdministrativeDivision.get_districts_by_city(city_code)
        serializer = DistrictSerializer(districts, many=True)

        return StandardResponse(
            data=serializer.data,
            msg=f"获取{city.name}区县列表成功"
        )
    except Exception as e:
        return ErrorResponse(
            code=5000,
            msg=f"获取区县列表失败: {str(e)}"
        )
