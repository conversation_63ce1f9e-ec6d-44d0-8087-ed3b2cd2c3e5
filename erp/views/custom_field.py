from django.db import models
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import ValidationError
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp.models import CustomField, CustomFieldValue
from erp.serializers.custom_field import (
    CustomFieldSerializer, CustomFieldCreateSerializer, CustomFieldUpdateSerializer
)
from erp.utils.json_response import ListResponse, ErrorResponse, StandardResponse, CreatedResponse
from erp.utils.viewsets import CreateUpdateMixin
from erp.utils.decorators import JWTRequiredMixin


class CustomFieldViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """自定义字段管理视图集"""

    queryset = CustomField.objects.filter(delete_datetime__isnull=True)
    serializer_class = CustomFieldSerializer
    # 去掉分页、搜索、筛选功能
    pagination_class = None
    filter_backends = []
    ordering_fields = []
    ordering = ['create_datetime']  # 改为正序排序（从早到晚）

    # 禁用PUT方法
    http_method_names = ['get', 'post', 'patch', 'delete', 'head', 'options', 'trace']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return CustomFieldCreateSerializer
        elif self.action == 'partial_update':
            return CustomFieldUpdateSerializer
        return CustomFieldSerializer

    @swagger_auto_schema(
        operation_summary="获取自定义字段列表",
        operation_description="获取指定模块的自定义字段列表，不分页",
        manual_parameters=[
            openapi.Parameter(
                'target_model',
                openapi.IN_QUERY,
                description='目标模块，必传参数。可选值：partner（相对方）、project（项目）、contract(合同)',
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'is_active',
                openapi.IN_QUERY,
                description='是否启用，可选值：True、False。不传表示获取所有字段',
                type=openapi.TYPE_BOOLEAN,
                required=False
            )
        ],
        responses={200: CustomFieldSerializer(many=True)}
    )
    def list(self, request):
        """获取自定义字段列表"""
        # 验证target_model参数必传
        target_model = request.query_params.get('target_model')
        if not target_model:
            return ErrorResponse(msg="target_model参数是必传的")
        
        # 验证target_model的有效值
        valid_choices = [choice[0] for choice in CustomField.TARGET_MODEL_CHOICES]
        if target_model not in valid_choices:
            return ErrorResponse(msg=f"target_model参数无效，可选值：{', '.join(valid_choices)}")

        # 处理is_active参数
        is_active_param = request.query_params.get('is_active')
        
        queryset = self.get_queryset().filter(target_model=target_model).order_by('create_datetime')

        if is_active_param is not None:
            # 将字符串转换为布尔值
            if is_active_param.lower() == 'true':
                queryset = queryset.filter(is_active=True)
            elif is_active_param.lower() == 'false':
                queryset = queryset.filter(is_active=False)
            else:
                return ErrorResponse(msg="is_active参数无效，可选值：true、false")

        # 序列化数据
        serializer = self.get_serializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取自定义字段列表成功")

    @swagger_auto_schema(
        operation_summary="创建自定义字段",
        operation_description="创建新的自定义字段",
        request_body=CustomFieldCreateSerializer,
        responses={201: CustomFieldSerializer}
    )
    def create(self, request):
        """创建自定义字段"""
        try:
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                # 处理错误消息格式，直接返回错误消息字符串
                errors = serializer.errors
                if 'field_name' in errors and isinstance(errors['field_name'], list) and len(errors['field_name']) == 1:
                    # 如果是field_name字段的单个错误，直接返回错误消息
                    return ErrorResponse(msg=errors['field_name'][0])
                else:
                    # 其他情况保持原有格式
                    formatted_errors = {}
                    for field, field_errors in errors.items():
                        if isinstance(field_errors, list) and len(field_errors) == 1:
                            formatted_errors[field] = field_errors[0]
                        else:
                            formatted_errors[field] = field_errors
                    return ErrorResponse(msg=formatted_errors)

            # 设置创建者
            if hasattr(request, 'user') and request.user.is_authenticated:
                serializer.validated_data['creator'] = request.user.username

            custom_field = serializer.save()

            # 返回完整的字段信息
            response_serializer = CustomFieldSerializer(custom_field)
            return CreatedResponse(data=response_serializer.data, msg="自定义字段创建成功")

        except ValidationError as e:
            return ErrorResponse(msg=f"自定义字段创建失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"自定义字段创建失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="获取自定义字段详情",
        operation_description="根据ID获取自定义字段详细信息",
        responses={200: CustomFieldSerializer}
    )
    def retrieve(self, request, pk=None):
        """获取自定义字段详情"""
        try:
            custom_field = self.get_object()
            serializer = CustomFieldSerializer(custom_field)
            return StandardResponse(data=serializer.data, msg="获取自定义字段详情成功")
        except CustomField.DoesNotExist:
            return ErrorResponse(msg="自定义字段不存在", code=4004)
        except Exception as e:
            if "No CustomField matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="自定义字段不存在", code=4004)
            return ErrorResponse(msg=f"获取自定义字段详情失败: {str(e)}")


    @swagger_auto_schema(
        operation_summary="部分更新自定义字段",
        operation_description="部分更新自定义字段信息（仅允许修改名称）",
        request_body=CustomFieldUpdateSerializer,
        responses={200: CustomFieldSerializer}
    )
    def partial_update(self, request, *args, **kwargs):
        """部分更新自定义字段"""
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            if serializer.is_valid():
                # 设置更新者
                if hasattr(request, 'user') and request.user.is_authenticated:
                    serializer.validated_data['updater'] = request.user.username

                instance = serializer.save()
                response_serializer = CustomFieldSerializer(instance)
                return StandardResponse(data=response_serializer.data, msg="自定义字段更新成功")

            # 处理错误消息格式，直接返回错误消息字符串
            errors = serializer.errors
            if 'field_name' in errors and isinstance(errors['field_name'], list) and len(errors['field_name']) == 1:
                # 如果是field_name字段的单个错误，直接返回错误消息
                return ErrorResponse(msg=errors['field_name'][0])
            else:
                # 其他情况保持原有格式
                formatted_errors = {}
                for field, field_errors in errors.items():
                    if isinstance(field_errors, list) and len(field_errors) == 1:
                        formatted_errors[field] = field_errors[0]
                    else:
                        formatted_errors[field] = field_errors
                return ErrorResponse(msg=formatted_errors)
        except ValidationError as e:
            return ErrorResponse(msg=f"自定义字段更新失败: {str(e)}")
        except Exception as e:
            return ErrorResponse(msg=f"自定义字段更新失败: {str(e)}")

    @swagger_auto_schema(
        operation_summary="删除自定义字段",
        operation_description="软删除自定义字段（同时删除所有相关的字段值）",
        responses={200: openapi.Response('删除成功')}
    )
    def destroy(self, request, pk=None):
        """软删除自定义字段"""
        try:
            custom_field = self.get_object()

            # 软删除所有相关的字段值
            CustomFieldValue.objects.filter(
                custom_field=custom_field,
                delete_datetime__isnull=True
            ).update(delete_datetime=models.functions.Now())

            # 软删除字段定义
            custom_field.delete()

            return StandardResponse(msg="自定义字段删除成功")

        except CustomField.DoesNotExist:
            return ErrorResponse(msg="自定义字段不存在", code=4004)
        except Exception as e:
            if "No CustomField matches the given query" in str(e) or "does not exist" in str(e).lower():
                return ErrorResponse(msg="自定义字段不存在", code=4004)
            return ErrorResponse(msg=f"删除自定义字段失败: {str(e)}")

    @action(detail=True, methods=['patch'])
    @swagger_auto_schema(
        operation_summary="设置自定义字段启用状态",
        operation_description="设置自定义字段的启用或禁用状态",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'is_active': openapi.Schema(
                    type=openapi.TYPE_BOOLEAN,
                    description='是否启用字段'
                )
            },
            required=['is_active']
        ),
        responses={200: CustomFieldSerializer}
    )
    def set_active_status(self, request, pk=None):
        """设置字段启用状态"""
        try:
            custom_field = self.get_object()

            # 验证请求参数
            is_active = request.data.get('is_active')
            if is_active is None:
                return ErrorResponse(msg="is_active参数是必传的")

            if not isinstance(is_active, bool):
                return ErrorResponse(msg="is_active参数必须是布尔值")

            custom_field.is_active = is_active

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                custom_field.updater = request.user.username

            custom_field.save()

            serializer = CustomFieldSerializer(custom_field)
            action_text = "启用" if custom_field.is_active else "禁用"
            return StandardResponse(data=serializer.data, msg=f"自定义字段{action_text}成功")

        except CustomField.DoesNotExist:
            return ErrorResponse(msg="自定义字段不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"设置字段状态失败: {str(e)}")

    @action(detail=True, methods=['patch'], url_path='options')
    @swagger_auto_schema(
        operation_summary="更新自定义字段选项",
        operation_description="""
        更新单选和多选类型自定义字段的选项。
        前端传递完整的选项列表，后端直接覆盖原有选项。

        注意：只有 select（单选）和 multiselect（多选）类型的字段才能使用此接口。
        """,
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'options': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description='完整的选项列表，支持格式：1. 字符串数组 ["选项1", "选项2"] 2. 逗号分隔字符串 "选项1,选项2"'
                )
            },
            required=['options']
        ),
        responses={
            200: openapi.Response(
                description='更新成功',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=2000),
                        'msg': openapi.Schema(type=openapi.TYPE_STRING, example='选项更新成功'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'field_id': openapi.Schema(type=openapi.TYPE_STRING, description='字段ID'),
                                'field_name': openapi.Schema(type=openapi.TYPE_STRING, description='字段名称'),
                                'options_list': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                    description='更新后的选项列表'
                                )
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description='请求参数错误'),
            403: openapi.Response(description='字段类型不支持选项管理'),
            404: openapi.Response(description='自定义字段不存在')
        }
    )
    def manage_options(self, request, pk=None):
        """更新自定义字段选项"""
        try:
            custom_field = self.get_object()

            # 验证字段类型
            if custom_field.field_type not in ['select', 'multiselect']:
                return ErrorResponse(
                    msg=f"字段类型 '{custom_field.get_field_type_display()}' 不支持选项管理，只有单选和多选类型的字段才能管理选项",
                    code=4003
                )

            # 获取选项数据
            options = request.data.get('options')
            if options is None:
                return ErrorResponse(msg="options参数是必传的")

            # 处理选项格式，支持字符串数组和逗号分隔字符串
            if isinstance(options, str):
                # 逗号分隔字符串格式
                options_list = [option.strip() for option in options.split(',') if option.strip()]
            elif isinstance(options, list):
                # 字符串数组格式
                options_list = []
                for option in options:
                    if not isinstance(option, str):
                        return ErrorResponse(msg="选项值必须是字符串")
                    option = option.strip()
                    if option:
                        options_list.append(option)
            else:
                return ErrorResponse(msg="选项值必须是字符串数组或逗号分隔的字符串")

            # 验证选项不能为空
            if not options_list:
                return ErrorResponse(msg="选项值不能为空")

            # 检查重复选项
            if len(options_list) != len(set(options_list)):
                return ErrorResponse(msg="选项值不能重复")

            # 直接覆盖选项
            custom_field.set_options(options_list)

            # 设置更新者
            if hasattr(request, 'user') and request.user.is_authenticated:
                custom_field.updater = request.user.username

            custom_field.save()

            # 返回结果
            return StandardResponse(
                data={
                    'field_id': str(custom_field.id),
                    'field_name': custom_field.field_name,
                    'options_list': custom_field.get_options_list()
                },
                msg="选项更新成功"
            )

        except CustomField.DoesNotExist:
            return ErrorResponse(msg="自定义字段不存在", code=4004)
        except Exception as e:
            return ErrorResponse(msg=f"选项更新失败: {str(e)}")


