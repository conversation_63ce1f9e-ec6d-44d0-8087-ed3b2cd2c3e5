from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.shortcuts import get_object_or_404

from erp.models import PaymentPeriod, Contract
from erp.serializers.account import (
    PaymentPeriodListSerializer,
    PaymentPeriodDetailSerializer,
    PaymentPeriodCreateSerializer,
    PaymentPeriodUpdateSerializer,
    ContractAccountSummarySerializer,
    ContractAccountStatusSerializer
)
from erp.filters import PaymentPeriodFilter, ContractAccountFilter
from erp.utils.json_response import StandardResponse, ListResponse, CreatedResponse
from erp.utils.pagination import CustomPagination

from erp.utils.decorators import JWTRequiredMixin
from erp.swagger.response_schemas import account_list_responses, account_detail_responses


class AccountViewSet(JWTRequiredMixin, viewsets.ViewSet):
    """账款管理视图集"""
    
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]

    @swagger_auto_schema(
        operation_summary="获取账款记录列表",
        operation_description="获取合同账款汇总列表（F005-02），支持搜索和过滤",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="搜索合同名称或编号", type=openapi.TYPE_STRING),
            openapi.Parameter('contract_type', openapi.IN_QUERY, description="合同类型", type=openapi.TYPE_STRING,
                            enum=['sales', 'procurement']),
            openapi.Parameter('project_id', openapi.IN_QUERY, description="项目ID", type=openapi.TYPE_STRING),
            openapi.Parameter('partner_id', openapi.IN_QUERY, description="相对方ID", type=openapi.TYPE_STRING),
        ],
        responses=account_list_responses
    )
    def list(self, request):
        """获取账款记录列表（F005-02）"""
        # 获取所有合同
        queryset = Contract.objects.filter(delete_datetime__isnull=True)
        
        # 应用过滤器
        filterset = ContractAccountFilter(request.GET, queryset=queryset)
        if filterset.is_valid():
            queryset = filterset.qs
        
        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        
        if page is not None:
            serializer = ContractAccountSummarySerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        
        serializer = ContractAccountSummarySerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取账款记录列表成功")

    @swagger_auto_schema(
        operation_summary="获取合同账款详情",
        operation_description="获取指定合同的账款详情信息，返回内容与列表页一致",
        responses=account_detail_responses
    )
    def retrieve(self, request, pk=None):
        """获取合同账款详情"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        serializer = ContractAccountSummarySerializer(contract)
        return StandardResponse(data=serializer.data, msg="获取合同账款详情成功")

    @swagger_auto_schema(
        methods=['get'],
        operation_summary="获取合同期数列表",
        operation_description="获取指定合同的期数列表（F003-02）",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="搜索期数说明", type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description="期数状态", type=openapi.TYPE_STRING,
                            enum=['pending', 'completed']),
            openapi.Parameter('is_overdue', openapi.IN_QUERY, description="是否逾期", type=openapi.TYPE_BOOLEAN),
        ],
        responses={200: PaymentPeriodListSerializer(many=True)}
    )
    @swagger_auto_schema(
        methods=['post'],
        operation_summary="新增期数",
        operation_description="为指定合同新增期数（F003-02）",
        request_body=PaymentPeriodCreateSerializer,
        responses={201: PaymentPeriodDetailSerializer()}
    )
    @action(detail=True, methods=['get', 'post'], url_path='periods')
    def periods(self, request, pk=None):
        """期数管理（F003-02）"""
        if request.method == 'GET':
            return self.periods_list(request, pk)
        elif request.method == 'POST':
            return self.periods_create(request, pk)

    def periods_list(self, request, pk=None):
        """获取合同期数列表（F003-02）"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        
        # 获取该合同的期数
        queryset = PaymentPeriod.objects.filter(
            contract=contract,
            delete_datetime__isnull=True
        ).order_by('period')
        
        # 应用过滤器
        filterset = PaymentPeriodFilter(request.GET, queryset=queryset)
        if filterset.is_valid():
            queryset = filterset.qs
        
        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        
        if page is not None:
            serializer = PaymentPeriodListSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        
        serializer = PaymentPeriodListSerializer(queryset, many=True)
        return ListResponse(data=serializer.data, msg="获取期数列表成功")

    def periods_create(self, request, pk=None):
        """新增期数（F003-02）"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        
        serializer = PaymentPeriodCreateSerializer(
            data=request.data,
            context={'contract': contract}
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        
        # 返回详细信息
        detail_serializer = PaymentPeriodDetailSerializer(instance)
        return CreatedResponse(
            data=detail_serializer.data,
            msg="新增期数成功"
        )

    @swagger_auto_schema(
        methods=['get'],
        operation_summary="获取期数详情",
        operation_description="获取指定期数的详细信息",
        responses={200: PaymentPeriodDetailSerializer()}
    )
    @swagger_auto_schema(
        methods=['put'],
        operation_summary="更新期数",
        operation_description="更新指定期数的信息（F003-02）",
        request_body=PaymentPeriodUpdateSerializer,
        responses={200: PaymentPeriodDetailSerializer()}
    )
    @swagger_auto_schema(
        methods=['delete'],
        operation_summary="删除期数",
        operation_description="删除指定的期数（F003-02）",
        responses={200: openapi.Response(description="删除成功")}
    )
    @action(detail=True, methods=['get', 'put', 'delete'], url_path='periods/(?P<period_id>[^/.]+)')
    def period_detail(self, request, pk=None, period_id=None):
        """期数详情管理"""
        if request.method == 'GET':
            return self.periods_detail(request, pk, period_id)
        elif request.method == 'PUT':
            return self.periods_update(request, pk, period_id)
        elif request.method == 'DELETE':
            return self.periods_delete(request, pk, period_id)

    def periods_detail(self, request, pk=None, period_id=None):
        """获取期数详情"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        period = get_object_or_404(
            PaymentPeriod,
            pk=period_id,
            contract=contract,
            delete_datetime__isnull=True
        )
        
        serializer = PaymentPeriodDetailSerializer(period)
        return StandardResponse(data=serializer.data, msg="获取期数详情成功")

    def periods_update(self, request, pk=None, period_id=None):
        """更新期数（F003-02）"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        period = get_object_or_404(
            PaymentPeriod,
            pk=period_id,
            contract=contract,
            delete_datetime__isnull=True
        )
        
        serializer = PaymentPeriodUpdateSerializer(period, data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        
        # 返回详细信息
        detail_serializer = PaymentPeriodDetailSerializer(instance)
        return StandardResponse(data=detail_serializer.data, msg="更新期数成功")

    def periods_delete(self, request, pk=None, period_id=None):
        """删除期数（F003-02）"""
        contract = get_object_or_404(Contract, pk=pk, delete_datetime__isnull=True)
        period = get_object_or_404(
            PaymentPeriod,
            pk=period_id,
            contract=contract,
            delete_datetime__isnull=True
        )
        
        period.delete()  # 软删除
        return StandardResponse(msg="删除期数成功")


