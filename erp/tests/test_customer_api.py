from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from erp.models import Customer


class CustomerAPITestCase(TestCase):
    """客户API测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()

        # 设置JWT认证token
        self.jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.jwt_token}')

        # 创建测试客户
        self.customer = Customer.objects.create(
            name="测试客户公司",
            tax_id="91110108MA********9",
            type="C",
            industry="IT",  # 更新为新的英文代号
            contact_person="张三",
            phone="138********",
            email="<EMAIL>",
            address="北京市朝阳区测试路123号",
            province="北京",
            city="朝阳区",
            owner_name="李四",
            remark="这是一个测试客户"
        )
    
    def test_create_customer(self):
        """测试创建客户"""
        url = '/api/v1/customers/'
        data = {
            'name': '新客户公司',
            'tax_id': '91110108MA987654321',
            'type': 'C',
            'industry': 'I02',
            'contact_person': '王五',
            'phone': '***********',
            'email': '<EMAIL>',
            'address': '上海市浦东新区测试路456号',
            'province': '上海',
            'city': '浦东新区',
            'owner_name': '赵六',
            'remark': '新创建的测试客户',
            'invoice_bank': '中国银行上海分行',
            'invoice_bank_account': '********90********9',
            'invoice_address': '上海市浦东新区开票地址',
            'invoice_phone': '021-********'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Customer.objects.count(), 2)
        
        # 验证返回数据
        response_data = response.json()
        self.assertEqual(response_data['msg'], '客户创建成功')
        self.assertIn('data', response_data)
        self.assertEqual(response_data['data']['name'], '新客户公司')
    
    def test_list_customers(self):
        """测试获取客户列表"""
        url = '/api/v1/customers/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['msg'], '客户列表获取成功')
        self.assertIn('data', response_data)
        self.assertEqual(len(response_data['data']), 1)
    
    def test_search_customers(self):
        """测试搜索客户"""
        url = '/api/v1/customers/'

        # 按名称搜索
        response = self.client.get(url, {'search': '测试客户'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(len(response_data['data']), 1)

        # 按税号搜索
        response = self.client.get(url, {'search': '91110108MA********9'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(len(response_data['data']), 1)

        # 搜索不存在的客户
        response = self.client.get(url, {'search': '不存在的客户'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(len(response_data['data']), 0)
    
    def test_filter_customers(self):
        """测试筛选客户"""
        url = '/api/v1/customers/'
        
        # 按类型筛选
        response = self.client.get(url, {'type': 'C'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(len(response_data['data']), 1)
        
        # 按行业筛选
        response = self.client.get(url, {'industry': 'IT'})  # 更新为新的英文代号
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(len(response_data['data']), 1)
    
    def test_retrieve_customer(self):
        """测试获取客户详情"""
        url = f'/api/v1/customers/{self.customer.id}/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['msg'], '客户详情获取成功')
        self.assertEqual(response_data['data']['name'], '测试客户公司')
    
    def test_update_customer(self):
        """测试更新客户"""
        url = f'/api/v1/customers/{self.customer.id}/'
        data = {
            'name': '更新后的客户公司',
            'contact_person': '更新后的联系人',
            'phone': '13999999999'
        }
        
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证更新
        self.customer.refresh_from_db()
        self.assertEqual(self.customer.name, '更新后的客户公司')
        self.assertEqual(self.customer.contact_person, '更新后的联系人')
        self.assertEqual(self.customer.phone, '13999999999')
    
    def test_delete_customer(self):
        """测试删除客户（软删除）"""
        url = f'/api/v1/customers/{self.customer.id}/'
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证软删除
        self.customer.refresh_from_db()
        self.assertIsNotNone(self.customer.delete_datetime)
        
        # 验证列表中不再显示
        list_url = '/api/v1/customers/'
        list_response = self.client.get(list_url)
        response_data = list_response.json()
        self.assertEqual(len(response_data['data']), 0)
    

