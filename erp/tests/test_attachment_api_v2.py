"""
新版附件API单元测试 - 本地文件存储版本
"""
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.contenttypes.models import ContentType
from rest_framework.test import APITestCase
from rest_framework import status

from erp.models import Attachment, Customer, Project, Partner
from erp.utils.storage import LocalFileSystemStorage


class AttachmentAPIV2TestCase(APITestCase):
    """新版附件API测试"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试客户
        self.customer = Customer.objects.create(
            code="C250606001",
            name="测试客户",
            type="C",
            industry="IT",
            contact_person="张三",
            phone="13800138000",
            creator="test_user"
        )
        
        # 创建合作伙伴（客户）
        self.partner = Partner.objects.create(
            code="C250606001",
            name="测试客户",
            partner_type="customer",
            type="C",
            industry="IT",
            contact_person="张三",
            contact_phone="13800138000",
            owner_name="张三",
            creator="test_user"
        )

        # 创建测试项目
        self.project = Project.objects.create(
            code="P001-C250606001",
            name="测试项目",
            type="software_development",
            reason="signed",
            customer=self.partner,
            end_user_name="最终用户",
            # sales_manager_id 和 sales_manager_name 现在是可选的
            start_date="2024-01-01",
            end_date="2024-12-31",
            expected_profit_rate=25.0,
            creator="test_user"
        )
        
        # 创建临时存储目录
        self.test_storage_path = tempfile.mkdtemp()
        
        # Mock本地存储
        self.storage_patcher = patch('erp.utils.storage.get_storage_backend')
        mock_get_storage = self.storage_patcher.start()
        self.mock_storage = LocalFileSystemStorage()
        self.mock_storage.base_path = self.test_storage_path
        mock_get_storage.return_value = self.mock_storage

    def tearDown(self):
        """清理测试环境"""
        self.storage_patcher.stop()
        if os.path.exists(self.test_storage_path):
            shutil.rmtree(self.test_storage_path)

    def create_test_file(self, name="test.pdf", content=b"test file content"):
        """创建测试文件"""
        return SimpleUploadedFile(
            name=name,
            content=content,
            content_type="application/pdf"
        )

    def test_upload_contract_attachment(self):
        """测试上传合同附件"""
        test_file = self.create_test_file("contract.pdf")
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'contract',
            'object_id': str(self.customer.id),  # 使用客户ID作为测试
            'category': 'contract_signed_scan',
            'description': '合同双章扫描件'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['code'], 201)
        self.assertIn('data', response.data)
        
        # 验证附件记录
        attachment = Attachment.objects.get(original_name="contract.pdf")
        self.assertEqual(attachment.category, 'contract_signed_scan')
        self.assertEqual(attachment.description, '合同双章扫描件')

    def test_upload_project_attachment(self):
        """测试上传项目附件"""
        test_file = self.create_test_file("project_trial_balance.xlsx")
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'project',
            'object_id': str(self.project.id),
            'category': 'project_trial_balance',
            'description': '项目试算表'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证文件路径格式
        attachment = Attachment.objects.get(original_name="project_trial_balance.xlsx")
        self.assertTrue(attachment.file_path.startswith('project-files/P001-C250606001/'))

    def test_upload_customer_attachment(self):
        """测试上传客户附件"""
        test_file = self.create_test_file("customer_info.pdf")
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'customer',
            'object_id': str(self.customer.id),
            'category': 'project_other',
            'description': '客户资料'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证文件路径格式
        attachment = Attachment.objects.get(original_name="customer_info.pdf")
        self.assertTrue(attachment.file_path.startswith('customer-files/C250606001/'))

    def test_upload_invalid_content_type(self):
        """测试上传无效的内容类型"""
        test_file = self.create_test_file()
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'invalid_type',
            'object_id': str(self.customer.id),
            'category': 'project_other'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('不支持的内容类型', str(response.data))

    def test_upload_missing_required_fields(self):
        """测试缺少必需字段"""
        test_file = self.create_test_file()
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            # 缺少 content_type, object_id, category
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_upload_large_file(self):
        """测试上传大文件"""
        # 创建超过200MB的文件
        large_content = b"x" * (201 * 1024 * 1024)  # 201MB
        test_file = SimpleUploadedFile(
            name="large_file.pdf",
            content=large_content,
            content_type="application/pdf"
        )
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'customer',
            'object_id': str(self.customer.id),
            'category': 'project_other'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('文件大小', str(response.data))

    def test_upload_unsupported_file_type(self):
        """测试上传不支持的文件类型"""
        test_file = SimpleUploadedFile(
            name="malicious.exe",
            content=b"malicious content",
            content_type="application/x-executable"
        )
        
        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'customer',
            'object_id': str(self.customer.id),
            'category': 'project_other'
        }
        
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('不支持的文件类型', str(response.data))

    def test_list_attachments_by_content_type(self):
        """测试按内容类型筛选附件列表"""
        # 创建不同类型的附件
        content_type_customer = ContentType.objects.get_for_model(Customer)
        content_type_project = ContentType.objects.get_for_model(Project)
        
        Attachment.objects.create(
            original_name="customer_file.pdf",
            file_name="uuid-customer.pdf",
            file_path="customer-files/C250606001/uuid-customer.pdf",
            file_size=1024,
            content_type=content_type_customer,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )
        
        Attachment.objects.create(
            original_name="project_file.xlsx",
            file_name="uuid-project.xlsx",
            file_path="project-files/P001-C250606001/uuid-project.xlsx",
            file_size=2048,
            content_type=content_type_project,
            object_id=self.project.id,
            category='project_trial_balance',
            creator="test_user"
        )
        
        # 测试筛选客户附件
        url = reverse('attachment-list')
        response = self.client.get(url, {
            'content_type': 'customer',
            'object_id': str(self.customer.id)
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        items = response.data['data']['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['original_name'], "customer_file.pdf")

    def test_download_attachment(self):
        """测试下载附件"""
        # 先创建一个附件记录和实际文件
        content_type = ContentType.objects.get_for_model(Customer)
        file_path = "customer-files/C250606001/test.pdf"
        
        attachment = Attachment.objects.create(
            original_name="test.pdf",
            file_name="uuid-test.pdf",
            file_path=file_path,
            file_size=1024,
            file_type="application/pdf",
            content_type=content_type,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )
        
        # 创建实际文件
        full_path = os.path.join(self.test_storage_path, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'wb') as f:
            f.write(b"test file content")
        
        # 测试下载
        url = reverse('attachment-download')
        response = self.client.get(url, {'file_path': file_path})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response.content, b"test file content")

    def test_download_nonexistent_file(self):
        """测试下载不存在的文件"""
        url = reverse('attachment-download')
        response = self.client.get(url, {'file_path': 'nonexistent/file.pdf'})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('附件不存在', str(response.data))

    def test_attachment_detail(self):
        """测试获取附件详情"""
        content_type = ContentType.objects.get_for_model(Customer)
        attachment = Attachment.objects.create(
            original_name="detail_test.pdf",
            file_name="uuid-detail.pdf",
            file_path="customer-files/C250606001/uuid-detail.pdf",
            file_size=1024,
            content_type=content_type,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )
        
        url = reverse('attachment-detail', kwargs={'pk': attachment.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['original_name'], "detail_test.pdf")

    def test_delete_attachment(self):
        """测试删除附件"""
        content_type = ContentType.objects.get_for_model(Customer)
        attachment = Attachment.objects.create(
            original_name="delete_test.pdf",
            file_name="uuid-delete.pdf",
            file_path="customer-files/C250606001/uuid-delete.pdf",
            file_size=1024,
            content_type=content_type,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )
        
        url = reverse('attachment-detail', kwargs={'pk': attachment.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证软删除
        attachment.refresh_from_db()
        self.assertIsNotNone(attachment.delete_datetime)

    def test_chunk_upload_endpoint_removed(self):
        """测试分片上传接口已被移除"""
        test_file = self.create_test_file()

        # 尝试访问分片上传接口
        try:
            url = reverse('attachment-chunk-upload')
            # 如果能找到URL，说明接口还存在
            self.fail("分片上传接口应该已被移除")
        except:
            # 找不到URL是预期的结果
            pass

    def test_file_md5_calculation(self):
        """测试文件MD5计算"""
        test_content = b"test content for md5 calculation"
        test_file = SimpleUploadedFile(
            name="md5_test.txt",
            content=test_content,
            content_type="text/plain"
        )

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'customer',
            'object_id': str(self.customer.id),
            'category': 'project_other'
        }

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # 验证MD5值被正确计算
        attachment = Attachment.objects.get(original_name="md5_test.txt")
        self.assertIsNotNone(attachment.file_md5)
        self.assertEqual(len(attachment.file_md5), 32)

    def test_file_path_generation(self):
        """测试文件路径生成"""
        test_file = self.create_test_file("path_test.pdf")

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'project',
            'object_id': str(self.project.id),
            'category': 'project_trial_balance'
        }

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        attachment = Attachment.objects.get(original_name="path_test.pdf")

        # 验证文件路径格式
        self.assertTrue(attachment.file_path.startswith('project-files/P001-C250606001/'))
        self.assertTrue(attachment.file_path.endswith('.pdf'))

        # 验证文件名是UUID格式
        file_name_without_ext = attachment.file_name.split('.')[0]
        self.assertEqual(len(file_name_without_ext), 36)  # UUID长度

    def test_attachment_category_validation(self):
        """测试附件类别验证"""
        test_file = self.create_test_file()

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'customer',
            'object_id': str(self.customer.id),
            'category': 'invalid_category'  # 无效的类别
        }

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_search_attachments(self):
        """测试搜索附件"""
        content_type = ContentType.objects.get_for_model(Customer)

        # 创建测试附件
        Attachment.objects.create(
            original_name="search_test_document.pdf",
            file_name="uuid-search1.pdf",
            file_path="customer-files/C250606001/uuid-search1.pdf",
            file_size=1024,
            content_type=content_type,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )

        Attachment.objects.create(
            original_name="another_file.xlsx",
            file_name="uuid-search2.xlsx",
            file_path="customer-files/C250606001/uuid-search2.xlsx",
            file_size=2048,
            content_type=content_type,
            object_id=self.customer.id,
            category='project_other',
            creator="test_user"
        )

        # 搜索包含"search"的文件
        url = reverse('attachment-list')
        response = self.client.get(url, {'search': 'search'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        items = response.data['data']['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['original_name'], "search_test_document.pdf")

    def test_pagination(self):
        """测试分页功能"""
        content_type = ContentType.objects.get_for_model(Customer)

        # 创建多个附件
        for i in range(15):
            Attachment.objects.create(
                original_name=f"pagination_test_{i}.pdf",
                file_name=f"uuid-page-{i}.pdf",
                file_path=f"customer-files/C250606001/uuid-page-{i}.pdf",
                file_size=1024,
                content_type=content_type,
                object_id=self.customer.id,
                category='project_other',
                creator="test_user"
            )

        # 测试第一页
        url = reverse('attachment-list')
        response = self.client.get(url, {'page': 1, 'page_size': 10})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 10)
        self.assertEqual(response.data['data']['total'], 15)

        # 测试第二页
        response = self.client.get(url, {'page': 2, 'page_size': 10})
        self.assertEqual(len(response.data['data']['items']), 5)

    def test_no_automatic_status_update(self):
        """测试文件上传不会自动更新业务状态"""
        # 这个测试确保文件上传后不会触发合同或项目状态的自动更新
        test_file = self.create_test_file("contract_scan.pdf")

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'contract',
            'object_id': str(self.customer.id),  # 使用客户ID作为测试
            'category': 'contract_signed_scan'
        }

        response = self.client.post(url, data, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # 验证没有调用状态更新方法
        # 由于我们已经移除了状态更新逻辑，这里主要是确保上传成功
        attachment = Attachment.objects.get(original_name="contract_scan.pdf")
        self.assertEqual(attachment.category, 'contract_signed_scan')
