import os
import tempfile
import uuid
from io import BytesIO
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.contenttypes.models import ContentType
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch

from erp.models import Attachment, Customer, Project
from erp.utils.storage import LocalFileSystemStorage, AttachmentUploadManager

class AttachmentModelTestCase(TestCase):
    """附件模型测试"""

    def setUp(self):
        """设置测试数据"""
        self.customer = Customer.objects.create(
            name="测试客户",
            type="C",
            industry="IT",
            contact_person="张三",
            phone="13800138000",
            creator="test_user"
        )
        self.content_type = ContentType.objects.get_for_model(Customer)

    def test_create_attachment(self):
        """测试创建附件"""
        attachment = Attachment.objects.create(
            original_name="test.pdf",
            file_name="uuid-test.pdf",
            file_path="customer/2024/07/uuid-test.pdf",
            file_size=1024,
            file_type="application/pdf",
            file_extension=".pdf",
            file_md5="abc123",
            content_type=self.content_type,
            object_id=self.customer.id,
            description="测试附件",
            creator="test_user"
        )
        
        self.assertEqual(attachment.original_name, "test.pdf")
        self.assertEqual(attachment.file_size, 1024)
        self.assertEqual(attachment.content_object, self.customer)
        self.assertTrue(attachment.is_document)
        self.assertFalse(attachment.is_image)

    def test_file_size_formatted(self):
        """测试文件大小格式化"""
        attachment = Attachment(file_size=1024)
        self.assertEqual(attachment.file_size_formatted, "1.0 KB")
        
        attachment.file_size = 1024 * 1024
        self.assertEqual(attachment.file_size_formatted, "1.0 MB")

    def test_file_type_properties(self):
        """测试文件类型判断"""
        # 测试图片文件
        attachment = Attachment(file_extension=".jpg")
        self.assertTrue(attachment.is_image)
        self.assertFalse(attachment.is_document)
        self.assertEqual(attachment.get_file_icon(), "image")
        
        # 测试文档文件
        attachment.file_extension = ".pdf"
        self.assertFalse(attachment.is_image)
        self.assertTrue(attachment.is_document)
        self.assertEqual(attachment.get_file_icon(), "pdf")
        
        # 测试压缩文件
        attachment.file_extension = ".zip"
        self.assertFalse(attachment.is_image)
        self.assertFalse(attachment.is_document)
        self.assertTrue(attachment.is_archive)
        self.assertEqual(attachment.get_file_icon(), "archive")

    def test_related_model_properties(self):
        """测试关联模型属性"""
        attachment = Attachment.objects.create(
            original_name="test.pdf",
            file_name="uuid-test.pdf",
            file_path="customer/2024/07/uuid-test.pdf",
            file_size=1024,
            content_type=self.content_type,
            object_id=self.customer.id,
            creator="test_user"
        )
        
        self.assertEqual(attachment.related_model_name, "customer")
        self.assertFalse(attachment.is_contract_attachment)
        self.assertFalse(attachment.is_project_attachment)
        self.assertIsNotNone(attachment.related_object_display)

    def test_attachment_category_field(self):
        """测试附件类型字段"""
        attachment = Attachment.objects.create(
            original_name="test.pdf",
            file_name="uuid-test.pdf",
            file_path="project/2024/07/uuid-test.pdf",
            file_size=1024,
            content_type=self.content_type,
            object_id=self.customer.id,
            category='project_trial_balance',
            creator="test_user"
        )

        self.assertEqual(attachment.category, 'project_trial_balance')
        self.assertEqual(attachment.category_display, '试算表')

    def test_category_choices_methods(self):
        """测试附件类型选择方法"""
        project_choices = Attachment.get_project_category_choices()
        contract_choices = Attachment.get_contract_category_choices()

        # 验证项目附件类型
        project_values = [choice[0] for choice in project_choices]
        self.assertIn('project_trial_balance', project_values)
        self.assertIn('project_other', project_values)

        # 验证合同附件类型
        contract_values = [choice[0] for choice in contract_choices]
        self.assertIn('contract_signed_scan', contract_values)
        self.assertIn('sales_invoice_scan', contract_values)
        self.assertIn('contract_other', contract_values)

    def test_get_attachments_for_object(self):
        """测试获取对象的附件"""
        # 创建附件
        attachment1 = Attachment.objects.create(
            original_name="test1.pdf",
            file_name="uuid-test1.pdf",
            file_path="customer/2024/07/uuid-test1.pdf",
            file_size=1024,
            content_type=self.content_type,
            object_id=self.customer.id,
            creator="test_user"
        )
        
        attachment2 = Attachment.objects.create(
            original_name="test2.pdf",
            file_name="uuid-test2.pdf",
            file_path="customer/2024/07/uuid-test2.pdf",
            file_size=2048,
            content_type=self.content_type,
            object_id=self.customer.id,
            creator="test_user"
        )
        
        # 获取附件
        attachments = Attachment.get_attachments_for_object(self.customer)
        self.assertEqual(attachments.count(), 2)
        
        # 测试总大小
        total_size = Attachment.get_total_size_for_object(self.customer)
        self.assertEqual(total_size, 3072)

    def test_soft_delete(self):
        """测试软删除"""
        attachment = Attachment.objects.create(
            original_name="test.pdf",
            file_name="uuid-test.pdf",
            file_path="customer/2024/07/uuid-test.pdf",
            file_size=1024,
            content_type=self.content_type,
            object_id=self.customer.id,
            creator="test_user"
        )
        
        # 软删除
        attachment.delete()
        
        # 验证软删除
        self.assertIsNotNone(attachment.delete_datetime)
        
        # 验证查询时不包含已删除的记录
        attachments = Attachment.get_attachments_for_object(self.customer)
        self.assertEqual(attachments.count(), 0)

class AttachmentSearchTestCase(TestCase):
    """附件搜索测试"""

    def setUp(self):
        """设置测试数据"""
        self.customer = Customer.objects.create(
            code="C250606001",
            name="测试客户",
            type="C",
            industry="IT",
            contact_person="张三",
            phone="13800138000",
            creator="test_user"
        )

    def test_search_attachments(self):
        """测试搜索附件"""
        content_type = ContentType.objects.get_for_model(Customer)

        # 创建多个附件
        Attachment.objects.create(
            original_name="contract.pdf",
            file_name="uuid-contract.pdf",
            file_path="customer/2024/07/uuid-contract.pdf",
            file_size=1024,
            content_type=content_type,
            object_id=self.customer.id,
            description="合同文档",
            creator="test_user"
        )

        Attachment.objects.create(
            original_name="report.docx",
            file_name="uuid-report.docx",
            file_path="customer/2024/07/uuid-report.docx",
            file_size=2048,
            content_type=content_type,
            object_id=self.customer.id,
            description="报告文档",
            creator="test_user"
        )

        # 搜索测试
        url = reverse('attachment-list')
        response = self.client.get(url, {'search': 'contract'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        items = response.data['data']['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['original_name'], "contract.pdf")

    def test_filter_by_file_type(self):
        """测试按文件类型筛选"""
        content_type = ContentType.objects.get_for_model(Customer)

        # 创建不同类型的附件
        Attachment.objects.create(
            original_name="image.jpg",
            file_name="uuid-image.jpg",
            file_path="customer/2024/07/uuid-image.jpg",
            file_size=1024,
            file_extension=".jpg",
            content_type=content_type,
            object_id=self.customer.id,
            creator="test_user"
        )

        Attachment.objects.create(
            original_name="document.pdf",
            file_name="uuid-document.pdf",
            file_path="customer/2024/07/uuid-document.pdf",
            file_size=2048,
            file_extension=".pdf",
            content_type=content_type,
            object_id=self.customer.id,
            creator="test_user"
        )

        # 筛选图片文件
        url = reverse('attachment-list')
        response = self.client.get(url, {'file_type': 'image'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        items = response.data['data']['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['original_name'], "image.jpg")

    def test_file_deduplication(self):
        """测试文件去重功能"""
        # 这个测试需要Mock存储管理器来模拟文件去重
        with patch('erp.utils.storage.attachment_upload_manager') as mock_manager:
            # 模拟已存在相同MD5的文件
            existing_attachment = Attachment.objects.create(
                original_name="existing.pdf",
                file_name="uuid-existing.pdf",
                file_path="customer/2024/07/uuid-existing.pdf",
                file_size=1024,
                file_md5="same_md5_hash",
                content_type=ContentType.objects.get_for_model(Customer),
                object_id=self.customer.id,
                creator="test_user"
            )

            mock_manager.calculate_md5.return_value = "same_md5_hash"
            mock_manager.generate_file_path.return_value = ("customer/2024/07/new.pdf", "new.pdf")

            test_file = self.create_test_file(name="duplicate.pdf")

            url = reverse('attachment-upload')
            data = {
                'file': test_file,
                'content_type': 'customer',
                'object_id': str(self.customer.id),
                'description': '重复文件测试'
            }

            response = self.client.post(url, data, format='multipart')

            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

            # 验证新附件记录使用了已存在文件的路径
            new_attachment = Attachment.objects.get(original_name="duplicate.pdf")
            self.assertEqual(new_attachment.file_path, existing_attachment.file_path)
            self.assertEqual(new_attachment.file_md5, "same_md5_hash")

    def test_invalid_content_type(self):
        """测试无效的内容类型"""
        test_file = self.create_test_file()

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            'content_type': 'invalid_type',
            'object_id': str(self.customer.id)
        }

        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        test_file = self.create_test_file()

        url = reverse('attachment-upload')
        data = {
            'file': test_file,
            # 缺少 content_type 和 object_id
        }

        response = self.client.post(url, data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_pagination(self):
        """测试分页功能"""
        content_type = ContentType.objects.get_for_model(Customer)

        # 创建多个附件
        for i in range(15):
            Attachment.objects.create(
                original_name=f"test_{i}.pdf",
                file_name=f"uuid-test_{i}.pdf",
                file_path=f"customer/2024/07/uuid-test_{i}.pdf",
                file_size=1024,
                content_type=content_type,
                object_id=self.customer.id,
                creator="test_user"
            )

        # 测试第一页
        url = reverse('attachment-list')
        response = self.client.get(url, {'page': 1, 'page_size': 10})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 10)
        self.assertEqual(response.data['data']['total'], 15)

        # 测试第二页
        response = self.client.get(url, {'page': 2, 'page_size': 10})
        self.assertEqual(len(response.data['data']['items']), 5)
