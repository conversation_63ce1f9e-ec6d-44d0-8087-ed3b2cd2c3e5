"""
本地文件存储系统单元测试
"""
import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile

from erp.utils.storage import LocalFileSystemStorage, AttachmentUploadManager, get_storage_backend
from erp.models import Customer, Project, Partner


class LocalFileSystemStorageTestCase(TestCase):
    """本地文件系统存储测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建临时目录作为测试存储路径
        self.test_storage_path = tempfile.mkdtemp()
        self.storage = LocalFileSystemStorage()
        self.storage.base_path = self.test_storage_path

    def tearDown(self):
        """清理测试环境"""
        # 删除临时目录
        if os.path.exists(self.test_storage_path):
            shutil.rmtree(self.test_storage_path)

    def test_storage_initialization(self):
        """测试存储初始化"""
        storage = LocalFileSystemStorage()
        self.assertTrue(os.path.exists(storage.base_path))

    def test_save_file(self):
        """测试保存文件"""
        content = ContentFile(b"test content", name="test.txt")
        file_path = "test-folder/test.txt"
        
        saved_path = self.storage._save(file_path, content)
        
        # 验证文件保存成功
        self.assertEqual(saved_path, file_path)
        full_path = os.path.join(self.test_storage_path, file_path)
        self.assertTrue(os.path.exists(full_path))
        
        # 验证文件内容
        with open(full_path, 'rb') as f:
            self.assertEqual(f.read(), b"test content")

    def test_save_file_with_conflict(self):
        """测试文件名冲突时的处理"""
        content1 = ContentFile(b"content 1", name="test.txt")
        content2 = ContentFile(b"content 2", name="test.txt")
        
        # 保存第一个文件
        saved_path1 = self.storage._save("test.txt", content1)
        self.assertEqual(saved_path1, "test.txt")
        
        # 保存第二个同名文件
        saved_path2 = self.storage._save("test.txt", content2)
        self.assertEqual(saved_path2, "test_1.txt")
        
        # 验证两个文件都存在且内容不同
        full_path1 = os.path.join(self.test_storage_path, saved_path1)
        full_path2 = os.path.join(self.test_storage_path, saved_path2)
        
        self.assertTrue(os.path.exists(full_path1))
        self.assertTrue(os.path.exists(full_path2))
        
        with open(full_path1, 'rb') as f:
            self.assertEqual(f.read(), b"content 1")
        with open(full_path2, 'rb') as f:
            self.assertEqual(f.read(), b"content 2")

    def test_delete_file(self):
        """测试删除文件"""
        content = ContentFile(b"test content", name="test.txt")
        file_path = "test.txt"
        
        # 先保存文件
        self.storage._save(file_path, content)
        full_path = os.path.join(self.test_storage_path, file_path)
        self.assertTrue(os.path.exists(full_path))
        
        # 删除文件
        self.storage.delete(file_path)
        self.assertFalse(os.path.exists(full_path))

    def test_file_exists(self):
        """测试文件存在检查"""
        content = ContentFile(b"test content", name="test.txt")
        file_path = "test.txt"
        
        # 文件不存在
        self.assertFalse(self.storage.exists(file_path))
        
        # 保存文件后存在
        self.storage._save(file_path, content)
        self.assertTrue(self.storage.exists(file_path))

    def test_get_file_size(self):
        """测试获取文件大小"""
        content = ContentFile(b"test content", name="test.txt")
        file_path = "test.txt"
        
        # 文件不存在时返回0
        self.assertEqual(self.storage.size(file_path), 0)
        
        # 保存文件后返回正确大小
        self.storage._save(file_path, content)
        self.assertEqual(self.storage.size(file_path), len(b"test content"))

    def test_get_file_url(self):
        """测试获取文件URL"""
        file_path = "test/test.txt"
        expected_url = f"/api/v1/attachments/download/?file_path={file_path}"
        
        url = self.storage.url(file_path)
        self.assertEqual(url, expected_url)

    def test_external_url(self):
        """测试获取外部访问URL"""
        file_path = "test/test.txt"
        expected_url = f"/api/v1/attachments/download/?file_path={file_path}"
        
        external_url = self.storage.external_url(file_path)
        self.assertEqual(external_url, expected_url)


class AttachmentUploadManagerTestCase(TestCase):
    """附件上传管理器测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建测试数据
        self.customer = Customer.objects.create(
            code="C250606001",
            name="测试客户",
            type="C",
            industry="IT",
            contact_person="张三",
            phone="13800138000",
            creator="test_user"
        )

        # 创建合作伙伴（客户）
        self.partner = Partner.objects.create(
            code="C250606001",
            name="测试客户",
            partner_type="customer",
            type="C",
            industry="IT",
            contact_person="张三",
            contact_phone="13800138000",
            owner_name="张三",
            creator="test_user"
        )

        self.project = Project.objects.create(
            code="P001-C250606001",
            name="测试项目",
            type="software_development",
            reason="signed",
            customer=self.partner,
            end_user_name="最终用户",
            # sales_manager_id 和 sales_manager_name 现在是可选的
            start_date="2024-01-01",
            end_date="2024-12-31",
            expected_profit_rate=25.0,
            creator="test_user"
        )
        
        # 创建临时存储路径
        self.test_storage_path = tempfile.mkdtemp()
        
        # Mock存储后端
        with patch('erp.utils.storage.get_storage_backend') as mock_get_storage:
            mock_storage = LocalFileSystemStorage()
            mock_storage.base_path = self.test_storage_path
            mock_get_storage.return_value = mock_storage
            self.manager = AttachmentUploadManager()

    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_storage_path):
            shutil.rmtree(self.test_storage_path)

    def test_generate_file_path_for_contract(self):
        """测试为合同生成文件路径"""
        # 这里需要创建合同对象，但由于依赖关系复杂，我们用mock
        with patch('erp.models.contract.Contract.objects.get') as mock_get:
            mock_contract = MagicMock()
            mock_contract.code = "SAL001-P001-C250606001"
            mock_get.return_value = mock_contract
            
            file_path, unique_filename = self.manager.generate_file_path(
                'contract', 'test_contract.pdf', 'test-contract-id'
            )
            
            self.assertTrue(file_path.startswith('contract-files/SAL001-P001-C250606001/'))
            self.assertTrue(unique_filename.endswith('.pdf'))

    def test_generate_file_path_for_project(self):
        """测试为项目生成文件路径"""
        file_path, unique_filename = self.manager.generate_file_path(
            'project', 'test_project.xlsx', str(self.project.id)
        )
        
        self.assertTrue(file_path.startswith('project-files/P001-C250606001/'))
        self.assertTrue(unique_filename.endswith('.xlsx'))

    def test_generate_file_path_for_customer(self):
        """测试为客户生成文件路径"""
        file_path, unique_filename = self.manager.generate_file_path(
            'customer', 'test_customer.pdf', str(self.customer.id)
        )
        
        self.assertTrue(file_path.startswith('customer-files/C250606001/'))
        self.assertTrue(unique_filename.endswith('.pdf'))

    def test_generate_file_path_for_other_type(self):
        """测试为其他类型生成文件路径"""
        file_path, unique_filename = self.manager.generate_file_path(
            'other', 'test_other.txt', 'test-id'
        )
        
        self.assertTrue(file_path.startswith('other-files/other/'))
        self.assertTrue(unique_filename.endswith('.txt'))

    def test_calculate_md5(self):
        """测试MD5计算"""
        content = ContentFile(b"test content for md5")
        
        md5_hash = self.manager.calculate_md5(content)
        
        # 验证MD5格式
        self.assertEqual(len(md5_hash), 32)
        self.assertTrue(all(c in '0123456789abcdef' for c in md5_hash))
        
        # 验证文件指针重置
        self.assertEqual(content.tell(), 0)

    def test_save_file(self):
        """测试保存文件"""
        content = ContentFile(b"test content")
        file_path = "test/test.txt"
        
        saved_path = self.manager.save_file(content, file_path)
        
        self.assertEqual(saved_path, file_path)


class StorageBackendFactoryTestCase(TestCase):
    """存储后端工厂函数测试"""

    def test_get_local_storage(self):
        """测试获取本地文件存储"""
        storage = get_storage_backend()
        self.assertIsInstance(storage, LocalFileSystemStorage)

    def test_storage_singleton(self):
        """测试存储后端是否为单例"""
        storage1 = get_storage_backend()
        storage2 = get_storage_backend()
        # 注意：这里不是严格的单例，每次调用都会创建新实例
        # 但类型应该相同
        self.assertEqual(type(storage1), type(storage2))
