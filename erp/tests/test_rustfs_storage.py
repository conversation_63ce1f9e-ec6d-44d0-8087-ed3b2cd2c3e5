"""
RUSTFS存储后端测试
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.core.files.base import ContentFile
from botocore.exceptions import ClientError

from erp.utils.storage import RustFSStorage, get_storage_backend, MinIOStorage


class RustFSStorageTestCase(TestCase):
    """RUSTFS存储测试"""

    @patch('erp.utils.storage.boto3.client')
    def setUp(self, mock_boto3_client):
        """设置测试环境"""
        self.mock_client = MagicMock()
        mock_boto3_client.return_value = self.mock_client
        
        # Mock head_bucket 成功（存储桶存在）
        self.mock_client.head_bucket.return_value = {}
        
        self.storage = RustFSStorage()

    def test_storage_initialization(self):
        """测试存储初始化"""
        # 触发延迟初始化
        client = self.storage.client
        
        self.assertIsNotNone(client)
        self.mock_client.head_bucket.assert_called_once_with(Bucket='erp-attachments')

    @patch('erp.utils.storage.boto3.client')
    def test_bucket_creation_when_not_exists(self, mock_boto3_client):
        """测试存储桶不存在时自动创建"""
        mock_client = MagicMock()
        mock_boto3_client.return_value = mock_client
        
        # Mock head_bucket 抛出404错误（存储桶不存在）
        error_response = {'Error': {'Code': '404'}}
        mock_client.head_bucket.side_effect = ClientError(error_response, 'HeadBucket')
        mock_client.create_bucket.return_value = {}
        
        storage = RustFSStorage()
        # 触发延迟初始化
        client = storage.client
        
        mock_client.head_bucket.assert_called_once()
        mock_client.create_bucket.assert_called_once_with(Bucket='erp-attachments')

    def test_save_file(self):
        """测试保存文件"""
        # 准备测试数据
        content = ContentFile(b"test content", name="test.txt")
        content.content_type = "text/plain"
        
        # Mock put_object
        self.mock_client.put_object.return_value = {}
        
        # 执行保存
        result = self.storage._save("test/file.txt", content)
        
        # 验证结果
        self.assertEqual(result, "test/file.txt")
        self.mock_client.put_object.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_client.put_object.call_args
        self.assertEqual(call_args[1]['Bucket'], 'erp-attachments')
        self.assertEqual(call_args[1]['Key'], 'test/file.txt')
        self.assertEqual(call_args[1]['ContentType'], 'text/plain')

    def test_file_exists(self):
        """测试文件存在检查"""
        # Mock head_object 成功
        self.mock_client.head_object.return_value = {}
        
        result = self.storage.exists("test/file.txt")
        
        self.assertTrue(result)
        self.mock_client.head_object.assert_called_once_with(
            Bucket='erp-attachments', 
            Key='test/file.txt'
        )

    def test_file_not_exists(self):
        """测试文件不存在的情况"""
        # Mock head_object 抛出错误
        self.mock_client.head_object.side_effect = ClientError({}, 'HeadObject')
        
        result = self.storage.exists("test/nonexistent.txt")
        
        self.assertFalse(result)

    def test_delete_file(self):
        """测试删除文件"""
        # Mock delete_object
        self.mock_client.delete_object.return_value = {}
        
        self.storage.delete("test/file.txt")
        
        self.mock_client.delete_object.assert_called_once_with(
            Bucket='erp-attachments',
            Key='test/file.txt'
        )

    def test_get_file_url(self):
        """测试获取文件URL"""
        # Mock generate_presigned_url
        expected_url = "https://example.com/presigned-url"
        self.mock_client.generate_presigned_url.return_value = expected_url
        
        result = self.storage.url("test/file.txt")
        
        self.assertEqual(result, expected_url)
        self.mock_client.generate_presigned_url.assert_called_once()

    def test_get_file_size(self):
        """测试获取文件大小"""
        # Mock head_object
        self.mock_client.head_object.return_value = {'ContentLength': 1024}
        
        result = self.storage.size("test/file.txt")
        
        self.assertEqual(result, 1024)

    def test_multipart_upload(self):
        """测试分片上传"""
        # Mock 分片上传相关方法
        self.mock_client.create_multipart_upload.return_value = {'UploadId': 'test-upload-id'}
        self.mock_client.upload_part.return_value = {'ETag': 'test-etag'}
        self.mock_client.complete_multipart_upload.return_value = {}
        
        # 测试初始化分片上传
        upload_id = self.storage.initiate_multipart_upload("test/large_file.txt", "text/plain")
        self.assertEqual(upload_id, 'test-upload-id')
        
        # 测试上传分片
        etag = self.storage.upload_part("test/large_file.txt", upload_id, 1, b"test data")
        self.assertEqual(etag, 'test-etag')
        
        # 测试完成分片上传
        parts = [{'ETag': 'test-etag', 'PartNumber': 1}]
        result = self.storage.complete_multipart_upload("test/large_file.txt", upload_id, parts)
        self.assertTrue(result)


class StorageBackendFactoryTestCase(TestCase):
    """存储后端工厂函数测试"""

    @override_settings(STORAGE_BACKEND='minio')
    def test_get_minio_storage(self):
        """测试获取MinIO存储"""
        storage = get_storage_backend()
        self.assertIsInstance(storage, MinIOStorage)

    @override_settings(STORAGE_BACKEND='rustfs')
    def test_get_rustfs_storage(self):
        """测试获取RUSTFS存储"""
        storage = get_storage_backend()
        self.assertIsInstance(storage, RustFSStorage)

    @override_settings(STORAGE_BACKEND='unknown')
    def test_get_default_storage(self):
        """测试未知存储类型时返回默认存储"""
        storage = get_storage_backend()
        self.assertIsInstance(storage, MinIOStorage)


class StorageCompatibilityTestCase(TestCase):
    """存储兼容性测试"""

    def test_both_storages_have_same_interface(self):
        """测试两种存储后端具有相同的接口"""
        minio_methods = set(dir(MinIOStorage))
        rustfs_methods = set(dir(RustFSStorage))
        
        # 检查关键方法是否都存在
        required_methods = {
            '_save', 'delete', 'exists', 'url', 'size',
            'initiate_multipart_upload', 'upload_part', 
            'complete_multipart_upload', 'abort_multipart_upload'
        }
        
        self.assertTrue(required_methods.issubset(minio_methods))
        self.assertTrue(required_methods.issubset(rustfs_methods))
