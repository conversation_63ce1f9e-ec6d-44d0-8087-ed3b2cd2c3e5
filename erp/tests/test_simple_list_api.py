from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from erp.models import Customer, Project, Supplier


class SimpleListAPITestCase(TestCase):
    """Simple List API测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        
        # 设置JWT认证token
        self.jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.jwt_token}')
        
        # 创建测试数据
        self.create_test_data()
    
    def create_test_data(self):
        """创建测试数据"""
        # 创建测试客户
        self.customer1 = Customer.objects.create(
            name="北京科技有限公司",
            code="C250101001",
            type="C",
            industry="IT",
            contact_person="张经理",
            phone="010-12345678",
            email="<EMAIL>",
            address="北京市海淀区中关村大街1号",
            owner_name="李总监"
        )
        
        self.customer2 = Customer.objects.create(
            name="上海贸易公司",
            code="C250101002", 
            type="C",
            industry="RETAIL",
            contact_person="王经理",
            phone="021-87654321",
            email="<EMAIL>",
            address="上海市浦东新区陆家嘴金融中心",
            owner_name="赵总监"
        )
        
        # 创建测试供应商
        self.supplier1 = Supplier.objects.create(
            name="深圳制造有限公司",
            code="V250101001",
            type="C",
            industry="MANUFACTURING",
            contact_person="刘经理",
            phone="0755-11111111",
            email="<EMAIL>",
            address="深圳市南山区科技园",
            owner_name="陈总监"
        )
        
        self.supplier2 = Supplier.objects.create(
            name="广州物流公司",
            code="V250101002",
            type="C", 
            industry="LOGISTICS",
            contact_person="周经理",
            phone="020-22222222",
            email="<EMAIL>",
            address="广州市天河区珠江新城",
            owner_name="吴总监"
        )
        
        # 创建测试项目
        self.project1 = Project.objects.create(
            name="ERP系统开发项目",
            customer=self.customer1,
            type="software_development",
            status="preparing",
            reason="signed",
            end_user_name="北京科技有限公司",
            sales_manager_id="MGR001",
            sales_manager_name="销售经理A",
            start_date="2024-01-01",
            end_date="2024-12-31",
            expected_profit_rate=25.00
        )
        
        self.project2 = Project.objects.create(
            name="网站建设项目",
            customer=self.customer2,
            type="website_development", 
            status="in_progress",
            reason="signed",
            end_user_name="上海贸易公司",
            sales_manager_id="MGR002",
            sales_manager_name="销售经理B",
            start_date="2024-02-01",
            end_date="2024-08-31",
            expected_profit_rate=30.00
        )
    
    def test_customer_simple_list_basic(self):
        """测试客户简单列表基本功能"""
        url = reverse('customer-simple-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # 检查返回的字段
        if response.data['data']['items']:
            item = response.data['data']['items'][0]
            self.assertIn('id', item)
            self.assertIn('code', item)
            self.assertIn('name', item)
            self.assertIn('status', item)
            self.assertIn('status_display', item)
    
    def test_customer_simple_list_pagination(self):
        """测试客户简单列表分页功能"""
        url = reverse('customer-simple-list')
        
        # 测试第一页
        response = self.client.get(url, {'page': 1, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 1)
        self.assertEqual(response.data['data']['total'], 2)
        
        # 测试第二页
        response = self.client.get(url, {'page': 2, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 2)
    
    def test_customer_simple_list_search(self):
        """测试客户简单列表搜索功能"""
        url = reverse('customer-simple-list')
        
        # 按名称搜索
        response = self.client.get(url, {'search': '北京'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['name'], '北京科技有限公司')
        
        # 按编码搜索
        response = self.client.get(url, {'search': 'C250101002'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['code'], 'C250101002')
        
        # 搜索不存在的内容
        response = self.client.get(url, {'search': '不存在的客户'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
    
    def test_supplier_simple_list_basic(self):
        """测试供应商简单列表基本功能"""
        url = reverse('supplier-simple-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # 检查返回的字段
        if response.data['data']['items']:
            item = response.data['data']['items'][0]
            self.assertIn('id', item)
            self.assertIn('code', item)
            self.assertIn('name', item)
            self.assertIn('status', item)
            self.assertIn('status_display', item)
    
    def test_supplier_simple_list_pagination(self):
        """测试供应商简单列表分页功能"""
        url = reverse('supplier-simple-list')
        
        # 测试第一页
        response = self.client.get(url, {'page': 1, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 1)
        self.assertEqual(response.data['data']['total'], 2)
        
        # 测试第二页
        response = self.client.get(url, {'page': 2, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 2)
    
    def test_supplier_simple_list_search(self):
        """测试供应商简单列表搜索功能"""
        url = reverse('supplier-simple-list')
        
        # 按名称搜索
        response = self.client.get(url, {'search': '深圳'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['name'], '深圳制造有限公司')
        
        # 按编码搜索
        response = self.client.get(url, {'search': 'V250101002'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['code'], 'V250101002')
        
        # 搜索不存在的内容
        response = self.client.get(url, {'search': '不存在的供应商'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
    
    def test_project_simple_list_basic(self):
        """测试项目简单列表基本功能"""
        url = reverse('project-simple-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        # 检查返回的字段
        if response.data['data']['items']:
            item = response.data['data']['items'][0]
            self.assertIn('id', item)
            self.assertIn('code', item)
            self.assertIn('name', item)
            self.assertIn('status', item)
            self.assertIn('status_display', item)
    
    def test_project_simple_list_pagination(self):
        """测试项目简单列表分页功能"""
        url = reverse('project-simple-list')
        
        # 测试第一页
        response = self.client.get(url, {'page': 1, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 1)
        self.assertEqual(response.data['data']['total'], 2)
        
        # 测试第二页
        response = self.client.get(url, {'page': 2, 'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['page'], 2)
    
    def test_project_simple_list_search(self):
        """测试项目简单列表搜索功能"""
        url = reverse('project-simple-list')
        
        # 按名称搜索
        response = self.client.get(url, {'search': 'ERP'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['name'], 'ERP系统开发项目')
        
        # 按编码搜索（项目编码是自动生成的，我们搜索项目1的编码）
        project_code = self.project1.code
        response = self.client.get(url, {'search': project_code})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 1)
        self.assertEqual(response.data['data']['items'][0]['code'], project_code)
        
        # 搜索不存在的内容
        response = self.client.get(url, {'search': '不存在的项目'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
    
    def test_authentication_required(self):
        """测试认证要求"""
        # 移除认证头
        self.client.credentials()
        
        # 测试客户简单列表
        url = reverse('customer-simple-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 测试供应商简单列表
        url = reverse('supplier-simple-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # 测试项目简单列表
        url = reverse('project-simple-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_empty_search_results(self):
        """测试空搜索结果"""
        # 客户
        url = reverse('customer-simple-list')
        response = self.client.get(url, {'search': 'NONEXISTENT'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
        self.assertEqual(response.data['data']['total'], 0)

        # 供应商
        url = reverse('supplier-simple-list')
        response = self.client.get(url, {'search': 'NONEXISTENT'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
        self.assertEqual(response.data['data']['total'], 0)

        # 项目
        url = reverse('project-simple-list')
        response = self.client.get(url, {'search': 'NONEXISTENT'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)
        self.assertEqual(response.data['data']['total'], 0)

    def test_case_insensitive_search(self):
        """测试大小写不敏感搜索"""
        # 客户 - 测试小写搜索
        url = reverse('customer-simple-list')
        response = self.client.get(url, {'search': '北京'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data['data']['items']), 0)

        # 供应商 - 测试小写搜索
        url = reverse('supplier-simple-list')
        response = self.client.get(url, {'search': '深圳'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data['data']['items']), 0)

    def test_pagination_edge_cases(self):
        """测试分页边界情况"""
        # 测试超出范围的页码
        url = reverse('customer-simple-list')
        response = self.client.get(url, {'page': 999, 'limit': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 0)

        # 测试无效的页码
        response = self.client.get(url, {'page': 0, 'limit': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 测试大的limit值
        response = self.client.get(url, {'page': 1, 'limit': 1000})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLessEqual(len(response.data['data']['items']), 2)  # 我们只有2个客户

    def test_combined_search_and_pagination(self):
        """测试搜索和分页组合使用"""
        # 创建更多测试数据以便测试
        for i in range(3, 8):
            Customer.objects.create(
                name=f"测试客户{i}",
                code=f"C25010100{i}",
                type="C",
                industry="IT",
                contact_person=f"联系人{i}",
                phone=f"010-1234567{i}",
                email=f"test{i}@example.com",
                address=f"地址{i}",
                owner_name=f"负责人{i}"
            )

        url = reverse('customer-simple-list')
        # 搜索"测试"并分页
        response = self.client.get(url, {'search': '测试', 'page': 1, 'limit': 2})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['items']), 2)
        self.assertGreater(response.data['data']['total'], 2)

    def test_response_format_consistency(self):
        """测试响应格式一致性"""
        endpoints = [
            reverse('customer-simple-list'),
            reverse('supplier-simple-list'),
            reverse('project-simple-list')
        ]

        for url in endpoints:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            # 检查响应结构
            self.assertIn('code', response.data)
            self.assertIn('msg', response.data)
            self.assertIn('data', response.data)

            # 检查分页数据结构
            data = response.data['data']
            self.assertIn('items', data)
            self.assertIn('total', data)
            self.assertIn('page', data)
            self.assertIn('pages', data)

            # 检查每个item的基本字段
            if data['items']:
                item = data['items'][0]
                required_fields = ['id', 'code', 'name', 'status', 'status_display']
                for field in required_fields:
                    self.assertIn(field, item, f"Field {field} missing in {url}")

    def tearDown(self):
        """测试后清理 - 保留测试数据"""
        # 根据用户偏好，保留测试数据
        pass
