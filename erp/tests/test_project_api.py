from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from decimal import Decimal
from erp.models import Customer, Project


class ProjectAPITestCase(TestCase):
    """项目API测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()

        # 设置JWT认证token
        self.jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MjExODc4OCwianRpIjoiNmE4YTY4MWEtMjFhYS00NjcxLTk0MWItOGYxNTAyY2Y3NmQ2IiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImFkbWluIiwibmJmIjoxNzUyMTE4Nzg4LCJjc3JmIjoiN2ZlYjhkMTgtNGU4Ni00YWRlLTg2NGQtNDJiYTYyYjg5Zjk4IiwiZXhwIjoxNzUyMjkxNTg4LCJpZCI6IjRhMjI2MDNhLTg4ZDktNDgzMi05ZDU1LTI2OWJkNzRkZWRkNSIsInVzZXJuYW1lIjoiYWRtaW4iLCJ1c2VyX25hbWUiOiJBZG1pbiBVc2VyIiwibWFuYWdlciI6ImFkbWluIiwidXNlcl9jb2RlIjoiYWRtaW4iLCJ1c2VyX3JvbGUiOltdLCJkZXBhcnRtZW50X2NvZGUiOiIiLCJkZXBhcnRtZW50X25hbWUiOiIiLCJlbnRlcnByaXNlX2NvZGUiOiIiLCJlbnRlcnByaXNlX2xldmVsIjowfQ.TiP5MLlH8y8h0PFBI2VkY5MvDxWd8eoUK7_TssEcb0I"
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.jwt_token}')

        # 创建测试客户
        self.customer = Customer.objects.create(
            name="测试客户公司",
            tax_id="91110108MA123456789",
            type="C",
            industry="IT",  # 更新为新的英文代号
            contact_person="张三",
            phone="13812345678",
            email="<EMAIL>",
            address="北京市朝阳区测试路123号",
            province="北京",
            city="朝阳区",
            owner_name="李四",
            remark="这是一个测试客户"
        )
        
        # 创建测试项目
        self.project = Project.objects.create(
            name="测试项目",
            description="这是一个测试项目",
            type="software_development",
            status="preparing",
            reason="signed",
            customer=self.customer,
            end_user_name="最终用户公司",
            end_user_contact="王五",
            end_user_phone="13987654321",
            end_user_address="上海市浦东新区测试路456号",
            sales_manager_id="MGR001",
            sales_manager_name="项目经理张三",
            start_date="2024-01-01",
            end_date="2024-06-30",
            budget=Decimal("500000.00"),
            expected_profit_rate=Decimal("25.50"),
            progress=30
        )

    def test_create_project(self):
        """测试创建项目"""
        data = {
            "name": "新测试项目",
            "description": "这是一个新的测试项目",
            "type": "system_integration",
            "status": "preparing",
            "reason": "bidding",
            "customer_id": str(self.customer.id),
            "end_user_name": "新最终用户",
            "end_user_contact": "赵六",
            "end_user_phone": "13666666666",
            "end_user_address": "广州市天河区测试路789号",
            "sales_manager_id": "MGR002",
            "sales_manager_name": "项目经理李四",
            "start_date": "2024-02-01",
            "end_date": "2024-08-31",
            "budget": "800000.00",
            "expected_profit_rate": "30.00",
            "progress": 0
        }
        
        response = self.client.post('/api/v1/projects/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证返回数据
        response_data = response.json()
        self.assertEqual(response_data['name'], data['name'])
        self.assertEqual(response_data['type'], data['type'])
        # 注意：创建时使用的是ProjectCreateUpdateSerializer，不包含customer_name和customer_code字段
        # 这些字段只在列表和详情序列化器中返回
        
        # 验证数据库中的记录
        project = Project.objects.get(id=response_data['id'])
        self.assertEqual(project.name, data['name'])
        self.assertEqual(project.customer, self.customer)

    def test_get_project_list(self):
        """测试获取项目列表"""
        response = self.client.get('/api/v1/projects/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertIn('data', response_data)
        self.assertIn('items', response_data['data'])
        self.assertEqual(len(response_data['data']['items']), 1)
        self.assertEqual(response_data['data']['items'][0]['name'], self.project.name)

    def test_get_project_detail(self):
        """测试获取项目详情"""
        response = self.client.get(f'/api/v1/projects/{self.project.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertEqual(response_data['name'], self.project.name)
        self.assertEqual(response_data['code'], self.project.code)
        self.assertEqual(response_data['customer_name'], self.customer.name)

    def test_update_project(self):
        """测试更新项目"""
        data = {
            "name": "更新后的项目名称",
            "description": "更新后的项目描述",
            "progress": 50
        }
        
        response = self.client.patch(f'/api/v1/projects/{self.project.id}/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证数据库中的记录已更新
        self.project.refresh_from_db()
        self.assertEqual(self.project.name, data['name'])
        self.assertEqual(self.project.description, data['description'])
        self.assertEqual(self.project.progress, data['progress'])

    def test_delete_project(self):
        """测试删除项目（软删除）"""
        response = self.client.delete(f'/api/v1/projects/{self.project.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # 验证项目被软删除
        self.project.refresh_from_db()
        self.assertIsNotNone(self.project.delete_datetime)

    def test_search_projects(self):
        """测试搜索项目"""
        # 按项目名称搜索
        response = self.client.get('/api/v1/projects/', {'search': '测试项目'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['data']['items']), 1)

        # 按客户名称搜索
        response = self.client.get('/api/v1/projects/', {'search': '测试客户'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['data']['items']), 1)

    def test_filter_projects(self):
        """测试筛选项目"""
        # 按项目类型筛选
        response = self.client.get('/api/v1/projects/', {'type': 'software_development'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['data']['items']), 1)

        # 按项目状态筛选
        response = self.client.get('/api/v1/projects/', {'status': 'preparing'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['data']['items']), 1)

    def test_project_statistics(self):
        """测试项目统计信息"""
        response = self.client.get('/api/v1/projects/statistics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertIn('total_count', response_data)
        self.assertIn('status_stats', response_data)
        self.assertIn('type_stats', response_data)
        self.assertIn('overdue_count', response_data)
        self.assertEqual(response_data['total_count'], 1)

    def test_update_project_status(self):
        """测试更新项目状态"""
        data = {'status': 'in_progress'}
        
        response = self.client.patch(f'/api/v1/projects/{self.project.id}/update_status/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证状态已更新
        self.project.refresh_from_db()
        self.assertEqual(self.project.status, 'in_progress')

    def test_project_simple_list(self):
        """测试项目简单列表"""
        response = self.client.get('/api/v1/projects/simple_list/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertIn('data', response_data)
        self.assertIn('items', response_data['data'])
        self.assertEqual(len(response_data['data']['items']), 1)

        # 检查返回的字段
        item = response_data['data']['items'][0]
        self.assertIn('id', item)
        self.assertIn('code', item)
        self.assertIn('name', item)
        self.assertIn('status', item)
        self.assertIn('status_display', item)

    def test_project_validation(self):
        """测试项目数据验证"""
        # 测试必填字段验证
        data = {
            "name": "",  # 空名称
            "customer_id": str(self.customer.id),
        }
        
        response = self.client.post('/api/v1/projects/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # 测试日期验证
        data = {
            "name": "测试项目",
            "customer_id": str(self.customer.id),
            "start_date": "2024-06-01",
            "end_date": "2024-01-01",  # 结束日期早于开始日期
            "end_user_name": "测试用户",
            "sales_manager_name": "测试经理",
            "type": "software_development",
            "reason": "signed",
            "expected_profit_rate": "25.00"
        }
        
        response = self.client.post('/api/v1/projects/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_project_code_generation(self):
        """测试项目编号自动生成"""
        # 创建项目时应该自动生成编号
        data = {
            "name": "编号测试项目",
            "customer_id": str(self.customer.id),
            "end_user_name": "测试用户",
            "sales_manager_id": "MGR003",
            "sales_manager_name": "测试经理",
            "type": "software_development",
            "status": "preparing",
            "reason": "signed",
            "expected_profit_rate": "25.00",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        
        response = self.client.post('/api/v1/projects/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        response_data = response.json()
        self.assertTrue(response_data['code'].startswith('P'))
        # 新格式：P + 3位序号 + "-" + 客户编号，例如 P001-C250606001
        self.assertRegex(response_data['code'], r'^P\d{3}-[A-Z]\d{9}$')
