"""
Tests for contract code reuse functionality after soft deletion.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from decimal import Decimal

from erp.models.contract import Contract
from erp.models.project import Project
from erp.models.customer import Customer


class ContractCodeReuseTestCase(TestCase):
    """Test cases for contract code reuse after deletion."""

    def setUp(self):
        """Set up test data."""
        # Create a test customer
        self.customer = Customer.objects.create(
            name="Test Customer for Contract",
            type="enterprise",
            industry="TECH",
            contact_person="Test Contact",
            phone="13800138000"
        )
        
        # Create a test project
        self.project = Project.objects.create(
            name="Test Project for Contract",
            type="software_development",
            reason="signed",
            customer=self.customer,
            end_user_name="Test End User",
            sales_manager_id="test_user_id",
            sales_manager_name="Test Sales Manager",
            start_date="2025-01-01",
            end_date="2025-12-31",
            expected_profit_rate=Decimal("25.00"),
            budget=Decimal("1000000.00")
        )

    def test_contract_code_reuse_after_deletion(self):
        """Test that contract codes can be reused after soft deletion."""
        # Create first contract
        contract1 = Contract.objects.create(
            name="First Test Contract",
            category="sales",
            project=self.project,
            customer=self.customer,
            amount=Decimal("100000.00")
        )
        original_code = contract1.code
        
        # Verify contract was created
        self.assertIsNotNone(contract1.code)
        self.assertTrue(contract1.code.startswith("SAL"))
        
        # Delete the contract (soft delete)
        contract1.delete()
        
        # Verify soft deletion
        self.assertIsNotNone(contract1.delete_datetime)
        
        # Create second contract - should get the same code
        contract2 = Contract.objects.create(
            name="Second Test Contract",
            category="sales",
            project=self.project,
            customer=self.customer,
            amount=Decimal("200000.00")
        )
        
        # Verify the code was reused
        self.assertEqual(contract2.code, original_code)
        
        # Verify database state
        all_contracts = Contract.objects.filter(code=original_code)
        active_contracts = all_contracts.filter(delete_datetime__isnull=True)
        deleted_contracts = all_contracts.filter(delete_datetime__isnull=False)
        
        self.assertEqual(active_contracts.count(), 1)
        self.assertEqual(deleted_contracts.count(), 1)
        self.assertEqual(active_contracts.first().id, contract2.id)

    def test_unique_constraint_prevents_duplicate_active_contracts(self):
        """Test that unique constraint still prevents duplicate active contracts."""
        # Create first contract
        contract1 = Contract.objects.create(
            name="First Active Contract",
            category="sales",
            project=self.project,
            customer=self.customer,
            amount=Decimal("100000.00")
        )
        
        # Try to create second contract with same code (should fail)
        contract2 = Contract(
            name="Second Active Contract",
            category="sales",
            project=self.project,
            customer=self.customer,
            amount=Decimal("200000.00"),
            code=contract1.code  # Force same code
        )
        
        # Should raise ValidationError
        with self.assertRaises(ValidationError) as context:
            contract2.save()
        
        # Verify the error message
        error_message = str(context.exception)
        self.assertIn("已存在", error_message)

    def test_procurement_contract_code_reuse(self):
        """Test code reuse works for procurement contracts too."""
        # Create a test supplier
        from erp.models.supplier import Supplier
        supplier = Supplier.objects.create(
            name="Test Supplier",
            type="enterprise",
            industry="TECH",
            contact_person="Supplier Contact",
            phone="13900139000"
        )
        
        # Create first procurement contract
        contract1 = Contract.objects.create(
            name="First Procurement Contract",
            category="procurement",
            project=self.project,
            supplier=supplier,
            amount=Decimal("150000.00")
        )
        original_code = contract1.code
        
        # Verify it's a procurement contract code
        self.assertTrue(contract1.code.startswith("PUR"))
        
        # Delete and recreate
        contract1.delete()
        
        contract2 = Contract.objects.create(
            name="Second Procurement Contract",
            category="procurement",
            project=self.project,
            supplier=supplier,
            amount=Decimal("250000.00")
        )
        
        # Verify code reuse
        self.assertEqual(contract2.code, original_code)

    def test_different_categories_have_independent_sequences(self):
        """Test that sales and procurement contracts have independent code sequences."""
        # Create a supplier for procurement contracts
        from erp.models.supplier import Supplier
        supplier = Supplier.objects.create(
            name="Test Supplier",
            type="enterprise",
            industry="TECH",
            contact_person="Supplier Contact",
            phone="13900139000"
        )
        
        # Create sales contract
        sales_contract = Contract.objects.create(
            name="Sales Contract",
            category="sales",
            project=self.project,
            customer=self.customer,
            amount=Decimal("100000.00")
        )
        
        # Create procurement contract
        procurement_contract = Contract.objects.create(
            name="Procurement Contract",
            category="procurement",
            project=self.project,
            supplier=supplier,
            amount=Decimal("150000.00")
        )
        
        # Verify different prefixes
        self.assertTrue(sales_contract.code.startswith("SAL"))
        self.assertTrue(procurement_contract.code.startswith("PUR"))
        
        # Both should have sequence 001 for this project
        self.assertTrue("001" in sales_contract.code)
        self.assertTrue("001" in procurement_contract.code)
