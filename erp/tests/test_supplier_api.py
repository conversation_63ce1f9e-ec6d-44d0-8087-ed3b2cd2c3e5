from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from erp.models import Supplier, SupplierPaymentInfo


class SupplierAPITestCase(TestCase):
    """供应商API测试用例"""

    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
        
        # 创建测试数据（这些数据在测试后会保留）
        self.create_test_suppliers()

    def create_test_suppliers(self):
        """创建测试供应商数据"""
        
        # 供应商1：北京科技公司
        supplier1 = Supplier.objects.create(
            name='北京科技有限公司',
            tax_id='91110000********9A',
            type='C',
            status='ACTIVE',
            industry='IT',
            province_code='110000',
            province='北京市',
            city_code='110100',
            city='北京市',
            contact_person='张经理',
            phone='010-********',
            email='<EMAIL>',
            address='北京市海淀区中关村大街1号',
            website='https://www.bjtech.com',
            owner_name='李总监',
            owner_id='U001',
            remark='优质IT服务供应商，合作多年',
            invoice_bank='中国银行北京分行',
            invoice_bank_account='********90123456',
            invoice_address='北京市海淀区中关村大街1号',
            invoice_phone='010-********',
            invoice_remark='开票时请注明项目名称'
        )
        
        # 为供应商1创建收款信息
        SupplierPaymentInfo.objects.create(
            supplier=supplier1,
            bank_name='中国银行北京分行',
            account_number='********90123456',
            is_default=True,
            remark='主要收款账户'
        )

        SupplierPaymentInfo.objects.create(
            supplier=supplier1,
            bank_name='工商银行北京分行',
            account_number='****************',
            is_default=False,
            remark='备用收款账户'
        )

        # 供应商2：上海制造公司
        supplier2 = Supplier.objects.create(
            name='上海制造集团有限公司',
            tax_id='913100009********B',
            type='C',
            status='ACTIVE',
            industry='MANUFACTURING',
            province_code='310000',
            province='上海市',
            city_code='310100',
            city='上海市',
            contact_person='王主管',
            phone='021-********',
            email='<EMAIL>',
            address='上海市浦东新区张江高科技园区',
            website='https://www.shmanuf.com',
            owner_name='陈总经理',
            owner_id='U002',
            remark='大型制造业供应商，产品质量稳定',
            invoice_bank='建设银行上海分行',
            invoice_bank_account='****************',
            invoice_address='上海市浦东新区张江高科技园区',
            invoice_phone='021-********',
            invoice_remark='增值税专用发票'
        )
        
        SupplierPaymentInfo.objects.create(
            supplier=supplier2,
            bank_name='建设银行上海分行',
            account_number='****************',
            is_default=True,
            remark='对公账户'
        )

        # 供应商3：广州物流公司
        supplier3 = Supplier.objects.create(
            name='广州物流服务有限公司',
            tax_id='91440000456789123C',
            type='C',
            status='ACTIVE',
            industry='LOGISTICS',
            province_code='440000',
            province='广东省',
            city_code='440100',
            city='广州市',
            contact_person='刘经理',
            phone='020-********',
            email='<EMAIL>',
            address='广州市天河区珠江新城',
            website='https://www.gzlogistics.com',
            owner_name='赵副总',
            owner_id='U003',
            remark='专业物流服务商，覆盖全国',
            invoice_bank='农业银行广州分行',
            invoice_bank_account='****************',
            invoice_address='广州市天河区珠江新城',
            invoice_phone='020-********',
            invoice_remark='物流费用发票'
        )
        
        SupplierPaymentInfo.objects.create(
            supplier=supplier3,
            bank_name='农业银行广州分行',
            account_number='****************',
            is_default=True,
            remark='收款专用账户'
        )

        # 供应商4：深圳金融科技公司（暂停状态）
        supplier4 = Supplier.objects.create(
            name='深圳金融科技有限公司',
            tax_id='91440300789123456D',
            type='C',
            status='SUSPENDED',
            industry='FINANCE',
            province_code='440000',
            province='广东省',
            city_code='440300',
            city='深圳市',
            contact_person='周总监',
            phone='0755-********',
            email='<EMAIL>',
            address='深圳市南山区科技园',
            website='https://www.szfintech.com',
            owner_name='吴总裁',
            owner_id='U004',
            remark='金融科技服务商，目前暂停合作',
            invoice_bank='招商银行深圳分行',
            invoice_bank_account='****************',
            invoice_address='深圳市南山区科技园',
            invoice_phone='0755-********',
            invoice_remark='技术服务费发票'
        )
        
        SupplierPaymentInfo.objects.create(
            supplier=supplier4,
            bank_name='招商银行深圳分行',
            account_number='****************',
            is_default=True,
            remark='暂停使用'
        )

        # 供应商5：杭州教育公司
        supplier5 = Supplier.objects.create(
            name='杭州教育咨询有限公司',
            tax_id='91330100321654987E',
            type='C',
            status='ACTIVE',
            industry='EDUCATION',
            province_code='330000',
            province='浙江省',
            city_code='330100',
            city='杭州市',
            contact_person='孙老师',
            phone='0571-********',
            email='<EMAIL>',
            address='杭州市西湖区文三路',
            website='https://www.hzedu.com',
            owner_name='马校长',
            owner_id='U005',
            remark='专业教育培训服务商',
            invoice_bank='浙商银行杭州分行',
            invoice_bank_account='****************',
            invoice_address='杭州市西湖区文三路',
            invoice_phone='0571-********',
            invoice_remark='培训费发票'
        )
        
        SupplierPaymentInfo.objects.create(
            supplier=supplier5,
            bank_name='浙商银行杭州分行',
            account_number='****************',
            is_default=True,
            remark='教育专用账户'
        )

        print(f"✓ 创建了 {Supplier.objects.count()} 个供应商测试数据")
        print(f"✓ 创建了 {SupplierPaymentInfo.objects.count()} 个收款信息测试数据")

    def test_supplier_list(self):
        """测试获取供应商列表"""
        url = reverse('supplier-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertGreaterEqual(len(response.data['data']), 5)
        
        print("✓ 供应商列表API测试通过")

    def test_supplier_detail(self):
        """测试获取供应商详情"""
        supplier = Supplier.objects.first()
        url = reverse('supplier-detail', kwargs={'pk': supplier.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], supplier.name)
        
        print("✓ 供应商详情API测试通过")

    def test_supplier_search(self):
        """测试供应商搜索"""
        url = reverse('supplier-list')
        response = self.client.get(url, {'search': '北京'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['data']), 1)
        
        print("✓ 供应商搜索API测试通过")

    def test_supplier_filter_by_status(self):
        """测试按状态筛选供应商"""
        url = reverse('supplier-list')
        response = self.client.get(url, {'status': 'ACTIVE'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for supplier in response.data['data']:
            self.assertEqual(supplier['status'], 'ACTIVE')
        
        print("✓ 供应商状态筛选API测试通过")

    def test_supplier_payment_infos(self):
        """测试获取供应商收款信息"""
        supplier = Supplier.objects.first()
        url = reverse('supplier-payment-infos', kwargs={'pk': supplier.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        
        print("✓ 供应商收款信息API测试通过")

    def test_create_supplier(self):
        """测试创建新供应商"""
        url = reverse('supplier-list')
        data = {
            'name': '测试供应商有限公司',
            'tax_id': '91000000TEST12345',
            'type': 'C',
            'status': 'ACTIVE',
            'industry': 'IT',
            'contact_person': '测试联系人',
            'phone': '010-88888888',
            'email': '<EMAIL>',
            'owner_name': '测试负责人'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['name'], data['name'])
        
        print("✓ 创建供应商API测试通过")

    def tearDown(self):
        """测试后清理 - 这里我们不删除数据，保留测试数据"""
        # 不执行任何清理操作，保留测试数据
        pass
