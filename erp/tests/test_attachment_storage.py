import os
import tempfile
from io import BytesIO
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.core.files.base import ContentFile
from minio.error import S3Error

from erp.utils.storage import MinIOStorage, AttachmentUploadManager


class MinIOStorageTestCase(TestCase):
    """MinIO存储测试"""

    @patch('erp.utils.storage.Minio')
    def setUp(self, mock_minio):
        """设置测试环境"""
        self.mock_client = MagicMock()
        mock_minio.return_value = self.mock_client
        self.mock_client.bucket_exists.return_value = True
        
        self.storage = MinIOStorage()

    def test_storage_initialization(self):
        """测试存储初始化"""
        # 触发延迟初始化
        client = self.storage.client
        
        self.assertIsNotNone(client)
        self.mock_client.bucket_exists.assert_called_once_with('erp-attachments')

    def test_bucket_creation_when_not_exists(self):
        """测试存储桶不存在时自动创建"""
        with patch('erp.utils.storage.Minio') as mock_minio:
            mock_client = MagicMock()
            mock_minio.return_value = mock_client
            mock_client.bucket_exists.return_value = False
            
            storage = MinIOStorage()
            # 触发延迟初始化
            client = storage.client
            
            mock_client.bucket_exists.assert_called_once()
            mock_client.make_bucket.assert_called_once_with('erp-attachments')

    def test_save_file(self):
        """测试保存文件"""
        # 准备测试数据
        content = ContentFile(b"test content", name="test.txt")
        file_name = "test/test.txt"
        
        # 执行保存
        saved_name = self.storage._save(file_name, content)
        
        # 验证调用
        self.assertEqual(saved_name, file_name)
        self.mock_client.put_object.assert_called_once()
        
        # 验证调用参数
        call_args = self.mock_client.put_object.call_args
        self.assertEqual(call_args[1]['bucket_name'], 'erp-attachments')
        self.assertEqual(call_args[1]['object_name'], file_name)

    def test_delete_file(self):
        """测试删除文件"""
        file_name = "test/test.txt"
        
        self.storage.delete(file_name)
        
        self.mock_client.remove_object.assert_called_once_with('erp-attachments', file_name)

    def test_file_exists(self):
        """测试文件存在检查"""
        file_name = "test/test.txt"
        
        # 测试文件存在
        self.mock_client.stat_object.return_value = MagicMock()
        exists = self.storage.exists(file_name)
        self.assertTrue(exists)
        
        # 测试文件不存在
        self.mock_client.stat_object.side_effect = S3Error("NoSuchKey", "", "", "", "", "", "")
        exists = self.storage.exists(file_name)
        self.assertFalse(exists)

    def test_get_file_url(self):
        """测试获取文件URL"""
        file_name = "test/test.txt"
        expected_url = "http://minio.example.com/test.txt"
        
        self.mock_client.presigned_get_object.return_value = expected_url
        
        url = self.storage.url(file_name)
        
        self.assertEqual(url, expected_url)
        self.mock_client.presigned_get_object.assert_called_once_with(
            bucket_name='erp-attachments',
            object_name=file_name,
            expires=3600
        )

    def test_get_file_size(self):
        """测试获取文件大小"""
        file_name = "test/test.txt"
        
        # Mock stat对象
        mock_stat = MagicMock()
        mock_stat.size = 1024
        self.mock_client.stat_object.return_value = mock_stat
        
        size = self.storage.size(file_name)
        
        self.assertEqual(size, 1024)
        self.mock_client.stat_object.assert_called_once_with('erp-attachments', file_name)

    def test_get_file_size_not_found(self):
        """测试获取不存在文件的大小"""
        file_name = "test/nonexistent.txt"
        
        self.mock_client.stat_object.side_effect = S3Error("NoSuchKey", "", "", "", "", "", "")
        
        size = self.storage.size(file_name)
        
        self.assertEqual(size, 0)

    def test_save_file_error_handling(self):
        """测试保存文件时的错误处理"""
        content = ContentFile(b"test content", name="test.txt")
        file_name = "test/test.txt"
        
        # Mock S3错误
        self.mock_client.put_object.side_effect = S3Error("AccessDenied", "", "", "", "", "", "")
        
        with self.assertRaises(S3Error):
            self.storage._save(file_name, content)

    def test_delete_file_error_handling(self):
        """测试删除文件时的错误处理"""
        file_name = "test/test.txt"
        
        # Mock S3错误
        self.mock_client.remove_object.side_effect = S3Error("AccessDenied", "", "", "", "", "", "")
        
        with self.assertRaises(S3Error):
            self.storage.delete(file_name)


class AttachmentUploadManagerTestCase(TestCase):
    """附件上传管理器测试"""

    @patch('erp.utils.storage.MinIOStorage')
    def setUp(self, mock_storage_class):
        """设置测试环境"""
        self.mock_storage = MagicMock()
        mock_storage_class.return_value = self.mock_storage
        
        self.manager = AttachmentUploadManager()

    def test_generate_file_path(self):
        """测试生成文件路径"""
        file_path, file_name = self.manager.generate_file_path("contract", "test.pdf")

        # 验证路径格式
        self.assertTrue(file_path.startswith("contract/"))
        self.assertTrue(file_path.endswith(".pdf"))
        self.assertTrue("/" in file_path)  # 包含年月路径

        # 验证文件名格式
        self.assertTrue(file_name.endswith(".pdf"))
        self.assertEqual(len(file_name.split(".")[0]), 36)  # UUID长度

    @patch('erp.models.Project')
    def test_generate_file_path_with_project_code(self, mock_project_model):
        """测试项目附件的文件路径生成（包含项目代码）"""
        # Mock项目对象
        mock_project = MagicMock()
        mock_project.code = "P001-C230315001"
        mock_project_model.objects.get.return_value = mock_project

        # 测试项目附件路径生成
        project_id = "test-project-id"
        file_path, file_name = self.manager.generate_file_path("project", "test.pdf", project_id)

        # 验证路径格式：project/{project_code}/2025-07/{uuid}.pdf
        self.assertTrue(file_path.startswith("project/P001-C230315001/"))
        self.assertTrue(file_path.endswith(".pdf"))
        self.assertIn("2025-", file_path)  # 包含年月格式

        # 验证文件名格式
        self.assertTrue(file_name.endswith(".pdf"))
        self.assertEqual(len(file_name.split(".")[0]), 36)  # UUID长度

        # 验证调用了正确的项目查询
        mock_project_model.objects.get.assert_called_once_with(
            id=project_id, delete_datetime__isnull=True
        )

    @patch('erp.models.Contract')
    def test_generate_file_path_with_contract_code(self, mock_contract_model):
        """测试合同附件的文件路径生成（包含合同代码）"""
        # Mock合同对象
        mock_contract = MagicMock()
        mock_contract.code = "SAL001-P001-C230315001"
        mock_contract_model.objects.get.return_value = mock_contract

        # 测试合同附件路径生成
        contract_id = "test-contract-id"
        file_path, file_name = self.manager.generate_file_path("contract", "test.pdf", contract_id)

        # 验证路径格式：contract/{contract_code}/2025-07/{uuid}.pdf
        self.assertTrue(file_path.startswith("contract/SAL001-P001-C230315001/"))
        self.assertTrue(file_path.endswith(".pdf"))
        self.assertIn("2025-", file_path)  # 包含年月格式

        # 验证文件名格式
        self.assertTrue(file_name.endswith(".pdf"))
        self.assertEqual(len(file_name.split(".")[0]), 36)  # UUID长度

        # 验证调用了正确的合同查询
        mock_contract_model.objects.get.assert_called_once_with(
            id=contract_id, delete_datetime__isnull=True
        )

    def test_generate_file_path_different_extensions(self):
        """测试不同文件扩展名的路径生成"""
        test_cases = [
            ("test.jpg", ".jpg"),
            ("test.docx", ".docx"),
            ("test", ""),
            ("test.PDF", ".pdf")  # 测试大小写转换
        ]
        
        for original_name, expected_ext in test_cases:
            file_path, file_name = self.manager.generate_file_path("project", original_name)
            
            if expected_ext:
                self.assertTrue(file_path.endswith(expected_ext.lower()))
                self.assertTrue(file_name.endswith(expected_ext.lower()))
            else:
                self.assertFalse("." in file_name)

    def test_calculate_md5(self):
        """测试MD5计算"""
        # 测试已知内容的MD5
        content = BytesIO(b"hello world")
        md5_hash = self.manager.calculate_md5(content)
        
        # "hello world"的MD5应该是5d41402abc4b2a76b9719d911017c592
        expected_md5 = "5d41402abc4b2a76b9719d911017c592"
        self.assertEqual(md5_hash, expected_md5)
        
        # 验证文件指针重置
        self.assertEqual(content.tell(), 0)

    def test_calculate_md5_empty_file(self):
        """测试空文件的MD5计算"""
        content = BytesIO(b"")
        md5_hash = self.manager.calculate_md5(content)
        
        # 空文件的MD5应该是d41d8cd98f00b204e9800998ecf8427e
        expected_md5 = "d41d8cd98f00b204e9800998ecf8427e"
        self.assertEqual(md5_hash, expected_md5)

    def test_calculate_md5_large_file(self):
        """测试大文件的MD5计算"""
        # 创建大于4KB的内容（测试分块读取）
        large_content = b"x" * 8192  # 8KB
        content = BytesIO(large_content)
        
        md5_hash = self.manager.calculate_md5(content)
        
        # 验证MD5长度正确
        self.assertEqual(len(md5_hash), 32)
        self.assertTrue(all(c in '0123456789abcdef' for c in md5_hash))

    @patch('erp.utils.storage.AttachmentUploadManager.storage')
    def test_save_file(self, mock_storage):
        """测试保存文件"""
        content = BytesIO(b"test content")
        file_path = "test/test.txt"
        
        mock_storage._save.return_value = file_path
        
        result = self.manager.save_file(content, file_path)
        
        self.assertEqual(result, file_path)
        mock_storage._save.assert_called_once_with(file_path, content)

    def test_initiate_multipart_upload(self):
        """测试初始化分片上传"""
        file_path = "test/large_file.pdf"
        content_type = "application/pdf"
        
        # Mock MinIO客户端
        mock_client = MagicMock()
        self.manager.storage.client = mock_client
        mock_client._create_multipart_upload.return_value = "upload123"
        
        upload_id = self.manager.initiate_multipart_upload(file_path, content_type)
        
        self.assertEqual(upload_id, "upload123")
        mock_client._create_multipart_upload.assert_called_once()

    def test_upload_part(self):
        """测试上传分片"""
        file_path = "test/large_file.pdf"
        upload_id = "upload123"
        part_number = 1
        data = BytesIO(b"chunk data")
        
        # Mock MinIO客户端
        mock_client = MagicMock()
        self.manager.storage.client = mock_client
        mock_response = MagicMock()
        mock_response.etag = "etag123"
        mock_client._upload_part.return_value = mock_response
        
        etag = self.manager.upload_part(file_path, upload_id, part_number, data)
        
        self.assertEqual(etag, "etag123")
        mock_client._upload_part.assert_called_once()

    def test_complete_multipart_upload(self):
        """测试完成分片上传"""
        file_path = "test/large_file.pdf"
        upload_id = "upload123"
        parts = [
            {'PartNumber': 1, 'ETag': 'etag1'},
            {'PartNumber': 2, 'ETag': 'etag2'}
        ]
        
        # Mock MinIO客户端
        mock_client = MagicMock()
        self.manager.storage.client = mock_client
        
        result = self.manager.complete_multipart_upload(file_path, upload_id, parts)
        
        self.assertTrue(result)
        mock_client._complete_multipart_upload.assert_called_once()

    def test_abort_multipart_upload(self):
        """测试取消分片上传"""
        file_path = "test/large_file.pdf"
        upload_id = "upload123"
        
        # Mock MinIO客户端
        mock_client = MagicMock()
        self.manager.storage.client = mock_client
        
        # 应该不抛出异常
        self.manager.abort_multipart_upload(file_path, upload_id)
        
        mock_client._abort_multipart_upload.assert_called_once()

    def test_ensure_temp_bucket_exists(self):
        """测试确保临时存储桶存在"""
        # Mock MinIO客户端
        mock_client = MagicMock()
        mock_client.bucket_exists.return_value = False
        
        with patch('erp.utils.storage.MinIOStorage') as mock_storage_class:
            mock_storage = MagicMock()
            mock_storage.client = mock_client
            mock_storage_class.return_value = mock_storage
            
            manager = AttachmentUploadManager()
            # 触发延迟初始化
            storage = manager.storage
            
            # 验证临时存储桶创建
            mock_client.bucket_exists.assert_called()
            mock_client.make_bucket.assert_called()


class StorageIntegrationTestCase(TestCase):
    """存储集成测试"""

    @override_settings(
        MINIO_ENDPOINT='localhost:9000',
        MINIO_ACCESS_KEY='testkey',
        MINIO_SECRET_KEY='testsecret',
        MINIO_USE_HTTPS=False,
        MINIO_BUCKET_NAME='test-bucket'
    )
    @patch('erp.utils.storage.Minio')
    def test_storage_configuration(self, mock_minio):
        """测试存储配置"""
        mock_client = MagicMock()
        mock_minio.return_value = mock_client
        mock_client.bucket_exists.return_value = True
        
        storage = MinIOStorage()
        # 触发延迟初始化
        client = storage.client
        
        # 验证配置参数
        mock_minio.assert_called_once_with(
            endpoint='localhost:9000',
            access_key='testkey',
            secret_key='testsecret',
            secure=False
        )
        
        self.assertEqual(storage.bucket_name, 'test-bucket')

    def test_file_path_generation_consistency(self):
        """测试文件路径生成的一致性"""
        manager = AttachmentUploadManager()
        
        # 多次生成应该产生不同的路径
        paths = []
        for _ in range(10):
            file_path, file_name = manager.generate_file_path("test", "file.txt")
            paths.append((file_path, file_name))
        
        # 验证所有路径都不同
        unique_paths = set(path[0] for path in paths)
        self.assertEqual(len(unique_paths), 10)
        
        # 验证所有文件名都不同
        unique_names = set(path[1] for path in paths)
        self.assertEqual(len(unique_names), 10)
