# -*- coding: utf-8 -*-

import logging
import traceback

from django.db.models import ProtectedError
from django.http import Http404
from rest_framework.exceptions import APIException as DRFAPIException, AuthenticationFailed, NotAuthenticated, ValidationError
from rest_framework.status import HTTP_401_UNAUTHORIZED
from rest_framework.views import set_rollback, exception_handler

from erp.utils.json_response import ErrorResponse

logger = logging.getLogger(__name__)


class CustomAuthenticationFailed(NotAuthenticated):
    # 设置 status_code 属性为 400
    status_code = 400


def CustomExceptionHandler(ex, context):
    """
    统一异常拦截处理
    目的:(1)取消所有的500异常响应,统一响应为标准错误返回
        (2)准确显示错误信息
        (3)保持验证错误的字典格式
    :param ex:
    :param context:
    :return:
    """
    msg = ''
    code = 4000
    
    # 调用默认的异常处理函数
    response = exception_handler(ex, context)
    
    if isinstance(ex, AuthenticationFailed):
        # 如果是身份验证错误
        if response and response.data.get('detail') == "Given token not valid for any token type":
            code = 401
            msg = ex.detail
        elif response and response.data.get('detail') == "Token is blacklisted":
            # token在黑名单
            return ErrorResponse(status=HTTP_401_UNAUTHORIZED)
        else:
            code = 401
            msg = ex.detail
    elif isinstance(ex, NotAuthenticated):
        code = 401
        msg = ex.detail
    elif isinstance(ex, Http404):
        code = 404
        msg = "资源不存在或者没访问权限"
        return ErrorResponse(msg=msg, code=code, http_status=404)
    elif isinstance(ex, ValidationError):
        # 处理DRF验证错误，保持字典格式
        set_rollback()
        code = 4000
        msg = ex.detail
        # 如果是字典格式的验证错误，直接作为msg返回
        if isinstance(msg, dict):
            return ErrorResponse(msg=msg, code=code)
        # 如果是列表格式，转换为字符串
        elif isinstance(msg, list):
            msg = '; '.join(str(item) for item in msg)
        else:
            msg = str(msg)
    elif isinstance(ex, DRFAPIException):
        set_rollback()
        code = 4000
        msg = ex.detail
        # 如果是字典格式的错误，保持字典格式
        if isinstance(msg, dict):
            return ErrorResponse(msg=msg, code=code)
        # 如果是列表格式，转换为字符串
        elif isinstance(msg, list):
            msg = '; '.join(str(item) for item in msg)
        else:
            msg = str(msg)
    elif isinstance(ex, ProtectedError):
        set_rollback()
        msg = "删除失败:该条数据与其他数据有相关绑定"
    elif isinstance(ex, Exception):
        logger.exception(traceback.format_exc())
        msg = str(ex)
    
    return ErrorResponse(msg=msg, code=code)


def format_validation_errors(errors):
    """
    格式化验证错误信息
    将DRF的验证错误转换为统一格式
    """
    if isinstance(errors, dict):
        formatted_errors = {}
        for field, messages in errors.items():
            if isinstance(messages, list):
                # 如果是列表，取第一个错误信息
                formatted_errors[field] = messages
            else:
                formatted_errors[field] = [str(messages)]
        return formatted_errors
    elif isinstance(errors, list):
        # 如果是非字段错误的列表
        return {"non_field_errors": errors}
    else:
        # 其他情况转换为字符串
        return {"error": [str(errors)]}
