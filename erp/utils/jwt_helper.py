import requests
import logging
import json
import base64
import threading
from django.conf import settings
from rest_framework.exceptions import AuthenticationFailed

logger = logging.getLogger(__name__)

# 线程本地存储
_thread_locals = threading.local()


class JWTHelper:
    """JWT令牌处理工具类"""

    @staticmethod
    def set_current_user(user_code=None, user_name=None, user_role=None, enterprise_code=None,
                        enterprise_name=None, enterprise_level=None, department_name=None,
                        department_code=None, jwt_token=None):
        """设置当前用户信息到线程本地存储"""
        _thread_locals.user_code = user_code
        _thread_locals.user_name = user_name
        _thread_locals.user_role = user_role
        _thread_locals.enterprise_code = enterprise_code
        _thread_locals.enterprise_name = enterprise_name
        _thread_locals.enterprise_level = enterprise_level
        _thread_locals.department_name = department_name
        _thread_locals.department_code = department_code
        _thread_locals.jwt_token = jwt_token

    @staticmethod
    def get_current_user_id():
        """获取当前用户ID"""
        return getattr(_thread_locals, 'user_code', None)

    @staticmethod
    def get_current_user_name():
        """获取当前用户名"""
        return getattr(_thread_locals, 'user_name', None)

    @staticmethod
    def get_current_user_roles():
        """获取当前用户角色"""
        return getattr(_thread_locals, 'user_role', None)

    @staticmethod
    def get_current_enterprise_id():
        """获取当前企业ID"""
        return getattr(_thread_locals, 'enterprise_code', None)

    @staticmethod
    def get_current_department_name():
        """获取当前部门名称"""
        return getattr(_thread_locals, 'department_name', None)

    @staticmethod
    def get_current_department_id():
        """获取当前部门ID"""
        return getattr(_thread_locals, 'department_code', None)

    @staticmethod
    def get_jwt_payload(request):
        """从请求中解析JWT payload"""
        jwt_token = request.META.get('HTTP_AUTHORIZATION')

        if not jwt_token:
            raise AuthenticationFailed('未提供JWT token')

        try:
            # 处理不同格式的token
            if jwt_token.startswith('Bearer '):
                jwt_token = jwt_token[7:]
            elif '#' in jwt_token:
                jwt_token = jwt_token.split('#')[-1]

            # 解析JWT payload
            _, payload, _ = jwt_token.split('.')
            payload = payload.rstrip('=')
            payload += '=' * ((4 - len(payload) % 4) % 4)
            decoded_payload = base64.urlsafe_b64decode(payload)
            return json.loads(decoded_payload)
        except Exception as e:
            logger.error(f"JWT解析失败: {str(e)}")
            raise AuthenticationFailed('JWT解析失败')

    @staticmethod
    def get_current_jwt_token(request):
        """获取当前JWT token"""
        jwt_token = request.META.get('HTTP_AUTHORIZATION')
        if jwt_token:
            if jwt_token.startswith('Bearer '):
                return jwt_token[7:]
            elif '#' in jwt_token:
                return jwt_token.split('#')[-1]
            else:
                return jwt_token
        return None

    @staticmethod
    def get_jwt_token_from_request(request):
        """从请求中获取JWT令牌"""
        if not request:
            return None

        jwt_token = request.META.get('HTTP_AUTHORIZATION')
        if jwt_token:
            # 处理 "Bearer token" 或 "token#xxx" 格式
            if jwt_token.startswith('Bearer '):
                return jwt_token[7:]
            elif '#' in jwt_token:
                return jwt_token.split('#')[-1]
            else:
                return jwt_token
        return None
    
    @staticmethod
    def get_user_info_from_token(jwt_token):
        """从JWT令牌获取用户信息"""
        try:
            headers = {
                'x-rbac-token': jwt_token,
                'Content-Type': 'application/json'
            }
            
            user_center_url = settings.USER_CENTER_HOST
            response = requests.get(
                f"{user_center_url}/wolf/user/getUserInfo",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and 'data' in result:
                    return result['data']
            
            logger.error(f"获取用户信息失败: {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"获取用户信息时发生错误: {str(e)}")
            return None
    
    @staticmethod
    def validate_token(jwt_token):
        """验证JWT令牌是否有效"""
        try:
            headers = {
                'x-rbac-token': jwt_token,
                'Content-Type': 'application/json'
            }
            
            user_center_url = settings.USER_CENTER_HOST
            response = requests.get(
                f"{user_center_url}/wolf/user/validateToken",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('success', False)
            
            return False
            
        except Exception as e:
            logger.error(f"验证令牌时发生错误: {str(e)}")
            return False


class JWTMiddleware:
    """JWT中间件，自动解析请求中的JWT并设置用户信息（可选）"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            # 尝试解析JWT payload
            payload = JWTHelper.get_jwt_payload(request)
            # 获取JWT令牌
            jwt_token = JWTHelper.get_current_jwt_token(request)

            # 设置当前用户信息
            JWTHelper.set_current_user(
                user_code=payload.get('user_code'),
                user_name=payload.get('user_name'),
                user_role=payload.get('user_role'),
                enterprise_code=payload.get('enterprise_code'),
                enterprise_name=payload.get('enterprise_name'),
                enterprise_level=payload.get('enterprise_level'),
                department_code=payload.get('department_code'),
                department_name=payload.get('department_name'),
                jwt_token=jwt_token
            )
        except:
            # JWT解析失败，不设置用户信息，但继续处理请求
            # 具体的认证要求由装饰器或Mixin来处理
            pass

        response = self.get_response(request)
        return response
