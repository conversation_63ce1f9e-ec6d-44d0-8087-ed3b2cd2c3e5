"""
通用验证器模块

提供各种字段的通用验证函数，避免代码重复。
"""

import re
from rest_framework import serializers


def validate_phone_number(value, allow_null=False, field_name="联系电话"):
    """
    通用电话号码验证函数
    
    支持以下格式：
    - 手机号码：11位数字，以1开头，第二位为3-9
    - 座机号码：带区号（010-********）、不带区号（********）、带分机号（010-********-123）
    - 400电话：************ 格式
    
    Args:
        value: 待验证的电话号码
        allow_null: 是否允许空值（用于PATCH操作）
        field_name: 字段名称（用于错误消息）
    
    Returns:
        str: 清理后的电话号码
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 如果允许空值且值为 None，直接返回
    if allow_null and value is None:
        return value
    
    # 检查空值
    if not value or not value.strip():
        raise serializers.ValidationError(f"{field_name}不能为空")
    
    # 清理输入：去除空格和多余字符
    phone = re.sub(r'\s+', '', value.strip())
    
    # 定义验证模式
    patterns = {
        'mobile': r'^1[3-9]\d{9}$',                    # 手机号码：11位，以1开头
        'landline_with_area': r'^0\d{2,3}-\d{7,8}$',  # 带区号座机：010-********
        'landline_with_ext': r'^0\d{2,3}-\d{7,8}-\d{1,4}$',  # 带分机号：010-********-123
        'landline_simple': r'^\d{7,8}$',              # 不带区号座机：********
        'service_400': r'^400-?\d{3}-?\d{4}$',        # 400电话：************
    }
    
    # 验证各种格式
    for pattern_name, pattern in patterns.items():
        if re.match(pattern, phone):
            return phone
    
    # 如果都不匹配，返回错误
    raise serializers.ValidationError(f"请输入有效的{field_name}")


def validate_bank_account(value, allow_null=False, field_name="银行账号", min_length=10, max_length=30):
    """
    通用银行账号验证函数
    
    Args:
        value: 待验证的银行账号
        allow_null: 是否允许空值
        field_name: 字段名称（用于错误消息）
        min_length: 最小长度
        max_length: 最大长度
    
    Returns:
        str: 清理后的银行账号
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 如果允许空值且值为 None，直接返回
    if allow_null and value is None:
        return value
    
    # 如果提供了值，去除首尾空格
    if isinstance(value, str):
        value = value.strip()
    
    # 如果去除空格后为空字符串，根据 allow_null 决定是否允许
    if value == '':
        if allow_null:
            return None
        else:
            raise serializers.ValidationError(f"{field_name}不能为空")
    
    # 验证格式：只能包含数字
    if not value.isdigit():
        raise serializers.ValidationError(f"{field_name}只能包含数字")
    
    # 验证长度
    if len(value) < min_length or len(value) > max_length:
        raise serializers.ValidationError(f"{field_name}长度应在{min_length}-{max_length}位之间")
    
    return value


def validate_email_address(value, allow_null=False, field_name="邮箱地址"):
    """
    通用邮箱地址验证函数
    
    Args:
        value: 待验证的邮箱地址
        allow_null: 是否允许空值
        field_name: 字段名称（用于错误消息）
    
    Returns:
        str: 清理后的邮箱地址
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 如果允许空值且值为 None，直接返回
    if allow_null and value is None:
        return value
    
    # 如果提供了值，去除首尾空格
    if isinstance(value, str):
        value = value.strip()
    
    # 如果去除空格后为空字符串，根据 allow_null 决定是否允许
    if value == '':
        if allow_null:
            return None
        else:
            raise serializers.ValidationError(f"{field_name}不能为空")
    
    # 简单的邮箱格式验证
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, value):
        raise serializers.ValidationError(f"请输入有效的{field_name}")
    
    return value


def validate_tax_id(value, allow_null=False, field_name="纳税人识别号"):
    """
    通用纳税人识别号验证函数
    
    Args:
        value: 待验证的纳税人识别号
        allow_null: 是否允许空值
        field_name: 字段名称（用于错误消息）
    
    Returns:
        str: 清理后的纳税人识别号
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    # 如果允许空值且值为 None，直接返回
    if allow_null and value is None:
        return value
    
    # 如果提供了值，去除首尾空格
    if isinstance(value, str):
        value = value.strip()
    
    # 如果去除空格后为空字符串，根据 allow_null 决定是否允许
    if value == '':
        if allow_null:
            return None
        else:
            raise serializers.ValidationError(f"{field_name}不能为空")
    
    # 基本的税号格式验证（15位或18位数字字母组合）
    tax_id_pattern = r'^[A-Z0-9]{15}$|^[A-Z0-9]{18}$'
    if not re.match(tax_id_pattern, value.upper()):
        raise serializers.ValidationError(f"请输入有效的{field_name}（15位或18位）")
    
    return value.upper()
