"""
本地文件系统存储配置和工具类
"""
import os
import hashlib
import shutil
import re
from abc import ABC, abstractmethod
from datetime import datetime
from django.conf import settings
from django.core.files.storage import Storage
from django.core.files.base import ContentFile
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)


def sanitize_filename(filename):
    """
    清理文件名，移除或替换特殊字符
    保留原始文件名的可读性，同时确保文件系统兼容性
    """
    if not filename:
        return "unnamed_file"

    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)

    # 替换特殊字符为横杠
    # 保留中文、英文、数字、下划线、点号，其他字符替换为横杠
    name = re.sub(r'[^\w\u4e00-\u9fff.-]', '-', name)

    # 移除连续的横杠
    name = re.sub(r'-+', '-', name)

    # 移除开头和结尾的横杠
    name = name.strip('-')

    # 如果清理后的名称为空，使用默认名称
    if not name:
        name = "unnamed_file"

    # 限制文件名长度（不包括扩展名）
    if len(name) > 80:
        name = name[:80]

    return name + ext


class LocalFileSystemStorage(Storage):
    """本地文件系统存储"""

    def __init__(self):
        self.base_path = getattr(settings, 'LOCAL_FILE_STORAGE_PATH',
                                os.path.join(settings.BASE_DIR, 'media', 'attachments'))
        # 确保基础目录存在
        os.makedirs(self.base_path, exist_ok=True)

    def _save(self, name, content):
        """保存文件到本地文件系统"""
        full_path = os.path.join(self.base_path, name)

        # 确保目录存在
        directory = os.path.dirname(full_path)
        os.makedirs(directory, exist_ok=True)

        # 处理文件名冲突
        if os.path.exists(full_path):
            base_name, ext = os.path.splitext(name)
            counter = 1
            while os.path.exists(full_path):
                new_name = f"{base_name}_({counter}){ext}"
                full_path = os.path.join(self.base_path, new_name)
                counter += 1
            name = new_name

        # 保存文件
        with open(full_path, 'wb') as f:
            if hasattr(content, 'read'):
                shutil.copyfileobj(content, f)
            else:
                f.write(content)

        return name

    def delete(self, name):
        """删除文件"""
        full_path = os.path.join(self.base_path, name)
        if os.path.exists(full_path):
            try:
                os.remove(full_path)
            except OSError as e:
                logger.error(f"Error deleting file {full_path}: {e}")

    def exists(self, name):
        """检查文件是否存在"""
        full_path = os.path.join(self.base_path, name)
        return os.path.exists(full_path)

    def url(self, name):
        """获取文件的访问URL"""
        # 返回下载接口的URL
        return f"/api/v1/attachments/download/?file_path={name}"

    def external_url(self, name):
        """获取文件的外部访问URL"""
        return self.url(name)

    def size(self, name):
        """获取文件大小"""
        full_path = os.path.join(self.base_path, name)
        if os.path.exists(full_path):
            return os.path.getsize(full_path)
        return 0


class AttachmentUploadManager:
    """附件上传管理器 - 本地文件系统版本"""

    def __init__(self):
        self.storage = get_storage_backend()

    def generate_file_path(self, content_type, original_filename, object_id):
        """生成文件存储路径"""
        # 清理原始文件名，移除特殊字符
        sanitized_filename = sanitize_filename(original_filename)

        # 为了避免文件名冲突，在文件名前添加时间戳
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name, ext = os.path.splitext(sanitized_filename)
        timestamped_filename = f"{timestamp}_{name}{ext}"

        # 根据内容类型和对象ID生成路径
        if content_type == 'contract':
            # 获取合同编号作为文件夹名
            try:
                from erp.models.contract import Contract
                contract = Contract.objects.get(id=object_id, delete_datetime__isnull=True)
                folder_name = contract.code
            except:
                folder_name = str(object_id)
            file_path = f"contract-files/{folder_name}/{timestamped_filename}"
        elif content_type == 'project':
            # 获取项目编号作为文件夹名
            try:
                from erp.models.project import Project
                project = Project.objects.get(id=object_id, delete_datetime__isnull=True)
                folder_name = project.code
            except:
                folder_name = str(object_id)
            file_path = f"project-files/{folder_name}/{timestamped_filename}"
        elif content_type == 'customer':
            # 获取客户编号作为文件夹名
            try:
                from erp.models.customer import Customer
                customer = Customer.objects.get(id=object_id, delete_datetime__isnull=True)
                folder_name = customer.code
            except:
                folder_name = str(object_id)
            file_path = f"customer-files/{folder_name}/{timestamped_filename}"
        else:
            # 其他类型使用通用路径
            file_path = f"other-files/{content_type}/{timestamped_filename}"

        return file_path, timestamped_filename

    def save_file(self, file_obj, file_path):
        """保存文件"""
        return self.storage._save(file_path, file_obj)

    def calculate_md5(self, file_obj):
        """计算文件MD5值"""
        md5_hash = hashlib.md5()
        file_obj.seek(0)  # 重置文件指针
        for chunk in iter(lambda: file_obj.read(4096), b""):
            md5_hash.update(chunk)
        file_obj.seek(0)  # 重置文件指针
        return md5_hash.hexdigest()



def get_storage_backend():
    """获取存储后端实例"""
    return LocalFileSystemStorage()


# 创建全局附件上传管理器实例
attachment_upload_manager = AttachmentUploadManager()

