from rest_framework import status
from rest_framework.response import Response
from erp.utils.jwt_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from erp.utils.json_response import StandardResponse, ListResponse, ErrorResponse, CreatedResponse


class CreateUpdateMixin:
    """创建和更新的Mixin，自动处理用户信息"""
    
    def create(self, request, *args, **kwargs):
        """创建资源"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return CreatedResponse(data=serializer.data, msg="创建成功")

    def retrieve(self, request, *args, **kwargs):
        """获取单个资源"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return StandardResponse(data=serializer.data, msg="获取成功")

    def update(self, request, *args, **kwargs):
        """完整更新资源"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return StandardResponse(data=serializer.data, msg="更新成功")

    def partial_update(self, request, *args, **kwargs):
        """部分更新资源"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """删除资源（软删除）"""
        instance = self.get_object()
        self.perform_destroy(instance)
        return StandardResponse(msg="删除成功")

    def perform_create(self, serializer):
        """执行创建操作，自动设置用户信息"""
        # CoreModel的save方法会自动设置creator和updater
        serializer.save()

    def perform_update(self, serializer):
        """执行更新操作，自动设置用户信息"""
        # CoreModel的save方法会自动设置updater
        serializer.save()

    def perform_destroy(self, instance):
        """执行删除操作（软删除）"""
        # 设置删除者信息
        instance.updater = JWTHelper.get_current_user_id()
        instance.delete()  # 这会调用软删除
