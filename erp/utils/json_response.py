from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import JSONRenderer


class ListResponse(Response):
    """列表响应 - 用于列表接口，支持分页格式"""
    def __init__(self, data=None, msg='success', code=2000, page=1, limit=10, total=1, **kwargs):
        # 如果传入了分页参数，使用分页格式
        if any(k in kwargs for k in ['page', 'limit', 'total']) or page != 1 or limit != 10 or total != 1:
            response_data = {
                "code": code,
                "msg": msg,
                "data": {
                    "items": data,
                    "total": total,
                    "pages": (total + limit - 1) // limit,
                    "page": page
                }
            }
        else:
            # 非分页格式
            response_data = {
                "code": code,
                "msg": msg,
                "data": data
            }

        super().__init__(data=response_data, status=status.HTTP_200_OK, **kwargs)
        # 确保设置渲染器
        self.accepted_renderer = getattr(self, 'accepted_renderer', JSONRenderer())
        self.accepted_media_type = getattr(self, 'accepted_media_type', 'application/json')
        self.renderer_context = getattr(self, 'renderer_context', {})


# 保持向后兼容性的别名
SuccessResponse = ListResponse


class StandardResponse(Response):
    """标准响应 - 用于单条数据操作（获取详情、创建、更新、删除）"""
    def __init__(self, data=None, msg='success', code=2000, **kwargs):
        response_data = {
            "code": code,
            "data": data,
            "msg": msg
        }
        super().__init__(data=response_data, status=status.HTTP_200_OK, **kwargs)
        # 确保设置渲染器
        self.accepted_renderer = getattr(self, 'accepted_renderer', JSONRenderer())
        self.accepted_media_type = getattr(self, 'accepted_media_type', 'application/json')
        self.renderer_context = getattr(self, 'renderer_context', {})


# 保持向后兼容性的别名
DetailResponse = StandardResponse


class CreatedResponse(Response):
    """创建成功响应 - 返回201状态码"""
    def __init__(self, data=None, msg='创建成功', code=2000, **kwargs):
        response_data = {
            "code": code,
            "data": data,
            "msg": msg
        }
        super().__init__(data=response_data, status=status.HTTP_201_CREATED, **kwargs)
        # 确保设置渲染器
        self.accepted_renderer = getattr(self, 'accepted_renderer', JSONRenderer())
        self.accepted_media_type = getattr(self, 'accepted_media_type', 'application/json')
        self.renderer_context = getattr(self, 'renderer_context', {})


class ErrorResponse(Response):
    """错误响应"""
    def __init__(self, data=None, msg='error', code=4000, http_status=None, **kwargs):
        response_data = {
            "code": code,
            "data": data,
            "msg": msg
        }
        if http_status is None:
            if code >= 5000:
                http_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            elif code >= 4000:
                http_status = status.HTTP_400_BAD_REQUEST
            else:
                http_status = status.HTTP_200_OK

        super().__init__(data=response_data, status=http_status, **kwargs)
        # 确保设置渲染器
        self.accepted_renderer = getattr(self, 'accepted_renderer', JSONRenderer())
        self.accepted_media_type = getattr(self, 'accepted_media_type', 'application/json')
        self.renderer_context = getattr(self, 'renderer_context', {})
