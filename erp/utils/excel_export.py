import io
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from django.http import HttpResponse


class ExcelExporter:
    """Excel导出工具类"""
    
    def __init__(self):
        self.workbook = Workbook()
        # 删除默认工作表
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
    
    def create_response(self, filename):
        """创建HTTP响应"""
        # 创建内存中的Excel文件
        excel_file = io.BytesIO()
        self.workbook.save(excel_file)
        excel_file.seek(0)
        
        # 创建HTTP响应
        response = HttpResponse(
            excel_file.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # 设置文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="{filename}_{timestamp}.xlsx"'
        
        return response
    
    def setup_header_style(self, worksheet, headers, start_row=1):
        """设置表头样式"""
        # 表头样式
        header_font = Font(bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=start_row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        return start_row + 1
    
    def auto_adjust_column_width(self, worksheet):
        """自动调整列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 设置列宽，最小10，最大50
            adjusted_width = min(max(max_length + 2, 10), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width


class CustomerExcelExporter(ExcelExporter):
    """客户数据Excel导出器"""
    
    def export_customers(self, customers):
        """导出客户数据"""
        worksheet = self.workbook.create_sheet(title="客户数据")
        
        # 定义表头
        headers = [
            '客户编码', '客户名称', '纳税人识别号', '客户类型', '所属行业',
            '省份', '城市', '联系人', '联系电话', '电子邮箱', '联系地址',
            '负责人姓名', '开户银行', '银行账号', '开票地址', '开票电话',
            '备注', '创建时间', '更新时间'
        ]
        
        # 设置表头
        current_row = self.setup_header_style(worksheet, headers)
        
        # 写入数据
        for customer in customers:
            row_data = [
                customer.code,
                customer.name,
                customer.tax_id or '',
                customer.get_type_display(),
                customer.get_industry_display(),
                customer.province or '',
                customer.city or '',
                customer.contact_person or '',
                customer.contact_phone or '',  # 修复字段名
                customer.contact_email or '',  # 修复字段名
                customer.contact_address or '',  # 修复字段名
                customer.owner_name or '',
                customer.invoice_bank or '',
                customer.invoice_bank_account or '',
                customer.invoice_address or '',
                customer.invoice_phone or '',
                customer.remark or '',
                customer.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if customer.create_datetime else '',
                customer.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if customer.update_datetime else ''
            ]
            
            for col, value in enumerate(row_data, 1):
                worksheet.cell(row=current_row, column=col, value=value)
            
            current_row += 1
        
        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)
        
        return self.create_response('客户数据导出')


class SupplierExcelExporter(ExcelExporter):
    """供应商数据Excel导出器"""
    
    def export_suppliers_single_sheet(self, suppliers):
        """方案一：单工作表，收款信息合并在一个单元格"""
        worksheet = self.workbook.create_sheet(title="供应商数据")
        
        # 定义表头
        headers = [
            '供应商编码', '供应商名称', '纳税人识别号', '供应商类型', '合作状态', '所属行业',
            '省份', '城市', '联系人', '联系电话', '电子邮箱', '联系地址', '官网地址',
            '负责人姓名', '负责人ID', '开户银行', '银行账号', '开票地址', '开票电话', '开票备注',
            '收款信息', '备注', '创建时间', '更新时间'
        ]
        
        # 设置表头
        current_row = self.setup_header_style(worksheet, headers)
        
        # 写入数据
        for supplier in suppliers:
            # 处理收款信息
            payment_infos = []
            for payment in supplier.payment_infos.filter(delete_datetime__isnull=True):
                info = f"{payment.bank_name} - {payment.account_number}"
                if payment.is_default:
                    info += " (默认)"
                if payment.status == 'INACTIVE':
                    info += " (禁用)"
                if payment.remark:
                    info += f" [{payment.remark}]"
                payment_infos.append(info)
            
            payment_info_text = '\n'.join(payment_infos) if payment_infos else ''
            
            row_data = [
                supplier.code,
                supplier.name,
                supplier.tax_id or '',
                supplier.get_type_display(),
                supplier.get_status_display(),
                supplier.get_industry_display(),
                supplier.province or '',
                supplier.city or '',
                supplier.contact_person or '',
                supplier.phone or '',
                supplier.email or '',
                supplier.address or '',
                supplier.website or '',
                supplier.owner_name or '',
                supplier.owner_id or '',
                supplier.invoice_bank or '',
                supplier.invoice_bank_account or '',
                supplier.invoice_address or '',
                supplier.invoice_phone or '',
                supplier.invoice_remark or '',
                payment_info_text,
                supplier.remark or '',
                supplier.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.create_datetime else '',
                supplier.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.update_datetime else ''
            ]
            
            for col, value in enumerate(row_data, 1):
                cell = worksheet.cell(row=current_row, column=col, value=value)
                # 收款信息列设置自动换行
                if col == 21:  # 收款信息列
                    cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            current_row += 1
        
        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)
        
        return self.create_response('供应商数据导出')
    
    def export_suppliers_multi_row(self, suppliers):
        """方案二：每个收款信息创建单独行（推荐）"""
        worksheet = self.workbook.create_sheet(title="供应商数据")
        
        # 定义表头
        headers = [
            '供应商编码', '供应商名称', '纳税人识别号', '供应商类型', '合作状态', '所属行业',
            '省份', '城市', '联系人', '联系电话', '电子邮箱', '联系地址', '官网地址',
            '负责人姓名', '负责人ID', '开户银行', '银行账号', '开票地址', '开票电话', '开票备注',
            '收款银行', '收款账号', '是否默认账户', '账户状态', '账户备注',
            '备注', '创建时间', '更新时间'
        ]
        
        # 设置表头
        current_row = self.setup_header_style(worksheet, headers)
        
        # 写入数据
        for supplier in suppliers:
            payment_infos = supplier.payment_infos.filter(delete_datetime__isnull=True)
            
            if payment_infos.exists():
                # 有收款信息，为每个收款信息创建一行
                for payment in payment_infos:
                    row_data = [
                        supplier.code,
                        supplier.name,
                        supplier.tax_id or '',
                        supplier.get_type_display(),
                        supplier.get_status_display(),
                        supplier.get_industry_display(),
                        supplier.province or '',
                        supplier.city or '',
                        supplier.contact_person or '',
                        supplier.phone or '',
                        supplier.email or '',
                        supplier.address or '',
                        supplier.website or '',
                        supplier.owner_name or '',
                        supplier.owner_id or '',
                        supplier.invoice_bank or '',
                        supplier.invoice_bank_account or '',
                        supplier.invoice_address or '',
                        supplier.invoice_phone or '',
                        supplier.invoice_remark or '',
                        payment.bank_name,
                        payment.account_number,
                        '是' if payment.is_default else '否',
                        '启用' if payment.status == 'ACTIVE' else '禁用',
                        payment.remark or '',
                        supplier.remark or '',
                        supplier.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.create_datetime else '',
                        supplier.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.update_datetime else ''
                    ]
                    
                    for col, value in enumerate(row_data, 1):
                        worksheet.cell(row=current_row, column=col, value=value)
                    
                    current_row += 1
            else:
                # 没有收款信息，创建一行空的收款信息
                row_data = [
                    supplier.code,
                    supplier.name,
                    supplier.tax_id or '',
                    supplier.get_type_display(),
                    supplier.get_status_display(),
                    supplier.get_industry_display(),
                    supplier.province or '',
                    supplier.city or '',
                    supplier.contact_person or '',
                    supplier.phone or '',
                    supplier.email or '',
                    supplier.address or '',
                    supplier.website or '',
                    supplier.owner_name or '',
                    supplier.owner_id or '',
                    supplier.invoice_bank or '',
                    supplier.invoice_bank_account or '',
                    supplier.invoice_address or '',
                    supplier.invoice_phone or '',
                    supplier.invoice_remark or '',
                    '', '', '', '', '',  # 空的收款信息
                    supplier.remark or '',
                    supplier.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.create_datetime else '',
                    supplier.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.update_datetime else ''
                ]
                
                for col, value in enumerate(row_data, 1):
                    worksheet.cell(row=current_row, column=col, value=value)
                
                current_row += 1
        
        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)
        
        return self.create_response('供应商数据导出')

    def export_suppliers_multi_sheet(self, suppliers):
        """方案三：创建单独的收款信息工作表"""
        # 创建供应商基本信息工作表
        supplier_sheet = self.workbook.create_sheet(title="供应商基本信息")

        # 供应商基本信息表头
        supplier_headers = [
            '供应商编码', '供应商名称', '纳税人识别号', '供应商类型', '合作状态', '所属行业',
            '省份', '城市', '联系人', '联系电话', '电子邮箱', '联系地址', '官网地址',
            '负责人姓名', '负责人ID', '开户银行', '银行账号', '开票地址', '开票电话', '开票备注',
            '备注', '创建时间', '更新时间'
        ]

        # 设置供应商表头
        current_row = self.setup_header_style(supplier_sheet, supplier_headers)

        # 写入供应商基本信息
        for supplier in suppliers:
            row_data = [
                supplier.code,
                supplier.name,
                supplier.tax_id or '',
                supplier.get_type_display(),
                supplier.get_status_display(),
                supplier.get_industry_display(),
                supplier.province or '',
                supplier.city or '',
                supplier.contact_person or '',
                supplier.phone or '',
                supplier.email or '',
                supplier.address or '',
                supplier.website or '',
                supplier.owner_name or '',
                supplier.owner_id or '',
                supplier.invoice_bank or '',
                supplier.invoice_bank_account or '',
                supplier.invoice_address or '',
                supplier.invoice_phone or '',
                supplier.invoice_remark or '',
                supplier.remark or '',
                supplier.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.create_datetime else '',
                supplier.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if supplier.update_datetime else ''
            ]

            for col, value in enumerate(row_data, 1):
                supplier_sheet.cell(row=current_row, column=col, value=value)

            current_row += 1

        # 自动调整列宽
        self.auto_adjust_column_width(supplier_sheet)

        # 创建收款信息工作表
        payment_sheet = self.workbook.create_sheet(title="供应商收款信息")

        # 收款信息表头
        payment_headers = [
            '供应商编码', '供应商名称', '开户银行', '银行账号',
            '是否默认账户', '账户状态', '账户备注', '创建时间', '更新时间'
        ]

        # 设置收款信息表头
        current_row = self.setup_header_style(payment_sheet, payment_headers)

        # 写入收款信息
        for supplier in suppliers:
            payment_infos = supplier.payment_infos.filter(delete_datetime__isnull=True)

            for payment in payment_infos:
                row_data = [
                    supplier.code,
                    supplier.name,
                    payment.bank_name,
                    payment.account_number,
                    '是' if payment.is_default else '否',
                    '启用' if payment.status == 'ACTIVE' else '禁用',
                    payment.remark or '',
                    payment.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if payment.create_datetime else '',
                    payment.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if payment.update_datetime else ''
                ]

                for col, value in enumerate(row_data, 1):
                    payment_sheet.cell(row=current_row, column=col, value=value)

                current_row += 1

        # 自动调整列宽
        self.auto_adjust_column_width(payment_sheet)

        return self.create_response('供应商数据导出')


class PartnerExcelExporter(ExcelExporter):
    """相对方数据Excel导出器"""

    def _get_custom_fields_for_partner(self):
        """获取相对方的所有自定义字段定义"""
        from erp.models import CustomField
        return CustomField.objects.filter(
            target_model='partner',
            is_active=True,
            delete_datetime__isnull=True
        ).order_by('create_datetime')

    def export_partners(self, partners):
        """导出相对方数据（包含自定义字段）"""
        worksheet = self.workbook.create_sheet(title="相对方数据")

        # 获取自定义字段定义
        custom_fields = self._get_custom_fields_for_partner()

        # 定义基础表头
        base_headers = [
            '相对方编码', '相对方名称', '相对方类型', '状态',
            '行业', '省份', '城市', '联系人', '联系电话', '电子邮箱',
            '联系地址', '联系备注', '官网地址', '负责人姓名', '负责人ID', '备注',
            '开户银行', '银行账号', '开票地址', '开票电话', '开票备注',
            '纳税人识别号', '创建时间', '更新时间'
        ]

        # 添加自定义字段表头
        custom_field_headers = [field.field_name for field in custom_fields]
        headers = base_headers + custom_field_headers

        # 设置表头样式
        self.setup_header_style(worksheet, headers)

        # 写入数据
        for row_num, partner in enumerate(partners, start=2):
            # 基本信息
            worksheet.cell(row=row_num, column=1, value=partner.code)
            worksheet.cell(row=row_num, column=2, value=partner.name)
            worksheet.cell(row=row_num, column=3, value=partner.get_type_display())
            worksheet.cell(row=row_num, column=4, value=partner.get_status_display())
            worksheet.cell(row=row_num, column=5, value=partner.get_industry_display())

            # 地区信息
            worksheet.cell(row=row_num, column=6, value=partner.province)
            worksheet.cell(row=row_num, column=7, value=partner.city)

            # 联系信息
            worksheet.cell(row=row_num, column=8, value=partner.contact_person)
            worksheet.cell(row=row_num, column=9, value=partner.contact_phone)
            worksheet.cell(row=row_num, column=10, value=partner.contact_email)
            worksheet.cell(row=row_num, column=11, value=partner.contact_address)
            worksheet.cell(row=row_num, column=12, value=partner.contact_remark)
            worksheet.cell(row=row_num, column=13, value=partner.website)

            # 业务信息
            worksheet.cell(row=row_num, column=14, value=partner.owner_name)
            worksheet.cell(row=row_num, column=15, value=partner.owner_id)
            worksheet.cell(row=row_num, column=16, value=partner.remark)

            # 开票信息
            worksheet.cell(row=row_num, column=17, value=partner.invoice_bank)
            worksheet.cell(row=row_num, column=18, value=partner.invoice_bank_account)
            worksheet.cell(row=row_num, column=19, value=partner.invoice_address)
            worksheet.cell(row=row_num, column=20, value=partner.invoice_phone)
            worksheet.cell(row=row_num, column=21, value=partner.invoice_remark)

            # 其他信息
            worksheet.cell(row=row_num, column=22, value=partner.tax_id)
            worksheet.cell(row=row_num, column=23, value=partner.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if partner.create_datetime else '')
            worksheet.cell(row=row_num, column=24, value=partner.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if partner.update_datetime else '')

            # 自定义字段值
            if custom_fields:
                from erp.models import CustomFieldValue
                custom_field_values = CustomFieldValue.get_values_for_object(partner.id, 'partner')

                for idx, custom_field in enumerate(custom_fields):
                    col_num = 25 + idx  # 从第25列开始写入自定义字段
                    field_info = custom_field_values.get(custom_field.field_name, {})
                    value = field_info.get('value', '')

                    # 格式化不同类型的值
                    if value is not None:
                        if custom_field.field_type == 'date' and hasattr(value, 'strftime'):
                            value = value.strftime('%Y-%m-%d')
                        elif custom_field.field_type in ['select', 'multiselect'] and isinstance(value, (list, dict)):
                            # 将列表或字典转换为字符串
                            if isinstance(value, list):
                                value = ', '.join(str(v) for v in value)
                            else:
                                value = str(value)
                        elif custom_field.field_type == 'boolean':
                            value = '是' if value else '否'
                        else:
                            value = str(value) if value is not None else ''
                    else:
                        value = ''

                    worksheet.cell(row=row_num, column=col_num, value=value)

        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)

        return self.create_response('相对方数据导出')

    def export_partners_with_payment_info(self, partners):
        """导出相对方数据（包含收款信息和自定义字段，供应商类型的相对方每个收款账户一行）"""
        worksheet = self.workbook.create_sheet(title="相对方数据（含收款信息）")

        # 获取自定义字段定义
        custom_fields = self._get_custom_fields_for_partner()

        # 定义基础表头
        base_headers = [
            '相对方编码', '相对方名称', '相对方类型', '状态',
            '行业', '省份', '城市', '联系人', '联系电话', '电子邮箱',
            '联系地址', '联系备注', '官网地址', '负责人姓名', '负责人ID', '备注',
            '开户银行', '银行账号', '开票地址', '开票电话', '开票备注',
            '纳税人识别号', '创建时间', '更新时间',
            '收款银行', '收款账号', '是否默认账户', '收款备注'
        ]

        # 添加自定义字段表头
        custom_field_headers = [field.field_name for field in custom_fields]
        headers = base_headers + custom_field_headers

        # 设置表头样式
        self.setup_header_style(worksheet, headers)

        # 写入数据
        row_num = 2
        for partner in partners:
            # 获取收款信息（仅供应商类型）
            payment_infos = []
            if partner.is_supplier:
                payment_infos = partner.payment_infos.filter(delete_datetime__isnull=True)

            # 如果没有收款信息，至少写入一行基本信息
            if not payment_infos:
                payment_infos = [None]

            # 为每个收款信息写入一行
            for payment_info in payment_infos:
                # 基本信息
                worksheet.cell(row=row_num, column=1, value=partner.code)
                worksheet.cell(row=row_num, column=2, value=partner.name)
                worksheet.cell(row=row_num, column=3, value=partner.get_type_display())
                worksheet.cell(row=row_num, column=4, value=partner.get_status_display())
                worksheet.cell(row=row_num, column=5, value=partner.get_industry_display())

                # 地区信息
                worksheet.cell(row=row_num, column=6, value=partner.province)
                worksheet.cell(row=row_num, column=7, value=partner.city)

                # 联系信息
                worksheet.cell(row=row_num, column=8, value=partner.contact_person)
                worksheet.cell(row=row_num, column=9, value=partner.contact_phone)
                worksheet.cell(row=row_num, column=10, value=partner.contact_email)
                worksheet.cell(row=row_num, column=11, value=partner.contact_address)
                worksheet.cell(row=row_num, column=12, value=partner.contact_remark)
                worksheet.cell(row=row_num, column=13, value=partner.website)

                # 业务信息
                worksheet.cell(row=row_num, column=14, value=partner.owner_name)
                worksheet.cell(row=row_num, column=15, value=partner.owner_id)
                worksheet.cell(row=row_num, column=16, value=partner.remark)

                # 开票信息
                worksheet.cell(row=row_num, column=17, value=partner.invoice_bank)
                worksheet.cell(row=row_num, column=18, value=partner.invoice_bank_account)
                worksheet.cell(row=row_num, column=19, value=partner.invoice_address)
                worksheet.cell(row=row_num, column=20, value=partner.invoice_phone)
                worksheet.cell(row=row_num, column=21, value=partner.invoice_remark)

                # 其他信息
                worksheet.cell(row=row_num, column=22, value=partner.tax_id)
                worksheet.cell(row=row_num, column=23, value=partner.create_datetime.strftime('%Y-%m-%d %H:%M:%S') if partner.create_datetime else '')
                worksheet.cell(row=row_num, column=24, value=partner.update_datetime.strftime('%Y-%m-%d %H:%M:%S') if partner.update_datetime else '')

                # 收款信息
                if payment_info:
                    worksheet.cell(row=row_num, column=25, value=payment_info.bank_name)
                    worksheet.cell(row=row_num, column=26, value=payment_info.account_number)
                    worksheet.cell(row=row_num, column=27, value='是' if payment_info.is_default else '否')
                    worksheet.cell(row=row_num, column=28, value=payment_info.remark)

                # 自定义字段值
                if custom_fields:
                    from erp.models import CustomFieldValue
                    custom_field_values = CustomFieldValue.get_values_for_object(partner.id, 'partner')

                    for idx, custom_field in enumerate(custom_fields):
                        col_num = 29 + idx  # 从第29列开始写入自定义字段
                        field_info = custom_field_values.get(custom_field.field_name, {})
                        value = field_info.get('value', '')

                        # 格式化不同类型的值
                        if value is not None:
                            if custom_field.field_type == 'date' and hasattr(value, 'strftime'):
                                value = value.strftime('%Y-%m-%d')
                            elif custom_field.field_type in ['select', 'multiselect'] and isinstance(value, (list, dict)):
                                # 将列表或字典转换为字符串
                                if isinstance(value, list):
                                    value = ', '.join(str(v) for v in value)
                                else:
                                    value = str(value)
                            elif custom_field.field_type == 'boolean':
                                value = '是' if value else '否'
                            else:
                                value = str(value) if value is not None else ''
                        else:
                            value = ''

                        worksheet.cell(row=row_num, column=col_num, value=value)

                row_num += 1

        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)

        return self.create_response('相对方数据导出（含收款信息）')
