import requests
import base64
import json
import logging
from django.conf import settings
from decimal import Decimal
import re
from datetime import datetime

logger = logging.getLogger(__name__)


class OCRServiceException(Exception):
    """OCR服务异常"""
    pass


class BaiduOCRService:
    """百度OCR服务封装"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'BAIDU_OCR_API_KEY', '')
        self.secret_key = getattr(settings, 'BAIDU_OCR_SECRET_KEY', '')
        self.access_token = None
        
        if not self.api_key or not self.secret_key:
            logger.warning("百度OCR API密钥未配置，将使用模拟数据")
    
    def _get_access_token(self):
        """获取百度OCR访问令牌"""
        if not self.api_key or not self.secret_key:
            return None
            
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            'grant_type': 'client_credentials',
            'client_id': self.api_key,
            'client_secret': self.secret_key
        }
        
        try:
            response = requests.post(url, params=params, timeout=10)
            result = response.json()
            
            if 'access_token' in result:
                return result['access_token']
            else:
                raise OCRServiceException(f"获取访问令牌失败：{result.get('error_description', '未知错误')}")
                
        except requests.RequestException as e:
            raise OCRServiceException(f"网络请求失败：{str(e)}")
    
    def recognize_invoice(self, file):
        """识别发票"""
        try:
            # 如果没有配置API密钥，返回模拟数据
            if not self.api_key or not self.secret_key:
                return self._get_mock_result()
            
            # 获取访问令牌
            if not self.access_token:
                self.access_token = self._get_access_token()
            
            # 将文件转换为base64
            file.seek(0)  # 重置文件指针
            file_content = file.read()
            file_base64 = base64.b64encode(file_content).decode('utf-8')
            
            # 调用百度发票识别API
            url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/invoice?access_token={self.access_token}"
            
            payload = {
                'image': file_base64,
                'invoice_type': 'auto'  # 自动识别发票类型
            }
            
            response = requests.post(url, data=payload, timeout=30)
            result = response.json()
            
            if 'error_code' in result:
                raise OCRServiceException(f"OCR识别失败：{result.get('error_msg', '未知错误')}")
            
            # 解析识别结果
            return self._parse_baidu_result(result)
            
        except Exception as e:
            logger.error(f"OCR识别异常：{str(e)}")
            # 如果识别失败，返回模拟数据作为fallback
            return self._get_mock_result()
    
    def _parse_baidu_result(self, result):
        """解析百度OCR结果"""
        words_result = result.get('words_result', {})
        
        # 提取字段信息
        fields = {
            'invoice_code': self._clean_text(words_result.get('InvoiceCode', '')),
            'invoice_number': self._clean_text(words_result.get('InvoiceNum', '')),
            'invoice_date': self._parse_date(words_result.get('InvoiceDate', '')),
            'invoice_type': self._determine_invoice_type(words_result),
            'verification_code': self._clean_text(words_result.get('CheckCode', '')),
            'invoice_amount': self._parse_amount(words_result.get('TotalAmount', '')),
            'tax_rate': self._parse_tax_rate(words_result.get('TaxRate', '')),
            'tax_amount': self._parse_amount(words_result.get('TotalTax', '')),
            'payee': self._clean_text(words_result.get('SellerName', '')),
            'payee_tax_id': self._clean_text(words_result.get('SellerRegisterNum', '')),
            'payer': self._clean_text(words_result.get('PurchaserName', '')),
            'payer_tax_id': self._clean_text(words_result.get('PurchaserRegisterNum', '')),
            'description': self._extract_description(words_result)
        }
        
        # 计算置信度
        confidence = self._calculate_confidence(words_result)
        
        return {
            'fields': fields,
            'confidence': confidence
        }
    
    def _clean_text(self, text):
        """清理文本"""
        if not text:
            return ''
        return str(text).strip()
    
    def _parse_date(self, date_str):
        """解析日期"""
        if not date_str:
            return ''
        
        # 尝试多种日期格式
        date_formats = ['%Y年%m月%d日', '%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d']
        
        for fmt in date_formats:
            try:
                date_obj = datetime.strptime(str(date_str), fmt)
                return date_obj.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        return str(date_str)
    
    def _parse_amount(self, amount_str):
        """解析金额"""
        if not amount_str:
            return ''
        
        # 提取数字
        amount_match = re.search(r'[\d,]+\.?\d*', str(amount_str))
        if amount_match:
            amount = amount_match.group().replace(',', '')
            try:
                return str(Decimal(amount))
            except:
                pass
        
        return ''
    
    def _parse_tax_rate(self, tax_rate_str):
        """解析税率"""
        if not tax_rate_str:
            return ''
        
        # 提取百分比数字
        rate_match = re.search(r'(\d+(?:\.\d+)?)%?', str(tax_rate_str))
        if rate_match:
            rate = float(rate_match.group(1))
            # 如果是百分比形式，转换为小数
            if rate > 1:
                rate = rate / 100
            return str(rate)
        
        return ''
    
    def _determine_invoice_type(self, words_result):
        """判断发票类型"""
        invoice_type_str = words_result.get('InvoiceType', '')
        
        if '专用' in str(invoice_type_str) or '专票' in str(invoice_type_str):
            return 'special'
        elif '普通' in str(invoice_type_str) or '普票' in str(invoice_type_str):
            return 'ordinary'
        
        # 默认返回普票
        return 'ordinary'
    
    def _extract_description(self, words_result):
        """提取商品描述"""
        # 尝试从商品信息中提取描述
        commodity_info = words_result.get('CommodityName', '')
        if commodity_info:
            return self._clean_text(commodity_info)
        
        return ''
    
    def _calculate_confidence(self, words_result):
        """计算置信度"""
        # 模拟置信度计算
        confidence = {}
        
        for field in ['InvoiceCode', 'InvoiceNum', 'InvoiceDate', 'TotalAmount', 'TotalTax']:
            if field in words_result:
                # 根据字段内容长度和格式估算置信度
                value = str(words_result[field])
                if len(value) > 0:
                    confidence[field.lower()] = min(0.95, 0.7 + len(value) * 0.02)
                else:
                    confidence[field.lower()] = 0.0
            else:
                confidence[field.lower()] = 0.0
        
        return confidence
    
    def _get_mock_result(self):
        """获取模拟识别结果（用于测试或API未配置时）"""
        return {
            'fields': {
                'invoice_code': '144031909111',
                'invoice_number': '12345678',
                'invoice_date': '2025-08-02',
                'invoice_type': 'special',
                'verification_code': '12345678901234567890',
                'invoice_amount': '100000.00',
                'tax_rate': '0.13',
                'tax_amount': '13000.00',
                'payee': '测试收款方公司',
                'payee_tax_id': '91110000123456789X',
                'payer': '测试付款方公司',
                'payer_tax_id': '91110000987654321Y',
                'description': 'OCR识别的发票内容（模拟数据）'
            },
            'confidence': {
                'invoice_code': 0.95,
                'invoice_number': 0.98,
                'invoice_date': 0.92,
                'invoice_amount': 0.96,
                'tax_amount': 0.94,
                'payee': 0.89,
                'payer': 0.87,
                'tax_rate': 0.91
            }
        }


class OCRServiceFactory:
    """OCR服务工厂"""
    
    @staticmethod
    def get_service(service_type='baidu'):
        """获取OCR服务实例"""
        if service_type == 'baidu':
            return BaiduOCRService()
        else:
            raise OCRServiceException(f"不支持的OCR服务类型：{service_type}")
