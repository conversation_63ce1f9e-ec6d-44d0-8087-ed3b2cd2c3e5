"""
认证装饰器模块
提供JWT认证相关的装饰器
"""

from functools import wraps
from django.http import JsonResponse
from rest_framework.exceptions import AuthenticationFailed
from erp.utils.jwt_helper import J<PERSON>THelper


def jwt_required(view_func):
    """
    JWT认证装饰器
    用于函数视图，要求请求必须包含有效的JWT token
    
    使用方法:
    @jwt_required
    def my_view(request):
        # 这里可以安全地使用 JWTHelper.get_current_user_id() 等方法
        pass
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # 尝试解析JWT payload
            payload = JWTHelper.get_jwt_payload(request)
            # 获取JWT令牌
            jwt_token = JWTHelper.get_current_jwt_token(request)
            
            # 设置当前用户信息
            JWTHelper.set_current_user(
                user_code=payload.get('user_code'),
                user_name=payload.get('user_name'),
                user_role=payload.get('user_role'),
                enterprise_code=payload.get('enterprise_code'),
                enterprise_name=payload.get('enterprise_name'),
                enterprise_level=payload.get('enterprise_level'),
                department_code=payload.get('department_code'),
                department_name=payload.get('department_name'),
                jwt_token=jwt_token
            )
            
            # 调用原始视图函数
            return view_func(request, *args, **kwargs)
            
        except AuthenticationFailed as e:
            return JsonResponse({
                'code': 4001,
                'msg': str(e),
                'data': None
            }, status=401)
        except Exception as e:
            return JsonResponse({
                'code': 4001,
                'msg': '认证失败，请提供有效的JWT token',
                'data': None
            }, status=401)
    
    return wrapper


class JWTRequiredMixin:
    """
    JWT认证Mixin类
    用于ViewSet，要求所有请求必须包含有效的JWT token
    
    使用方法:
    class MyViewSet(JWTRequiredMixin, viewsets.ModelViewSet):
        # 所有方法都会自动进行JWT认证
        pass
    """
    
    def dispatch(self, request, *args, **kwargs):
        """重写dispatch方法，在处理请求前进行JWT认证"""
        try:
            # 尝试解析JWT payload
            payload = JWTHelper.get_jwt_payload(request)
            # 获取JWT令牌
            jwt_token = JWTHelper.get_current_jwt_token(request)
            
            # 设置当前用户信息
            JWTHelper.set_current_user(
                user_code=payload.get('user_code'),
                user_name=payload.get('user_name'),
                user_role=payload.get('user_role'),
                enterprise_code=payload.get('enterprise_code'),
                enterprise_name=payload.get('enterprise_name'),
                enterprise_level=payload.get('enterprise_level'),
                department_code=payload.get('department_code'),
                department_name=payload.get('department_name'),
                jwt_token=jwt_token
            )
            
            # 调用父类的dispatch方法
            return super().dispatch(request, *args, **kwargs)
            
        except AuthenticationFailed as e:
            return JsonResponse({
                'code': 4001,
                'msg': str(e),
                'data': None
            }, status=401)
        except Exception as e:
            return JsonResponse({
                'code': 4001,
                'msg': '认证失败，请提供有效的JWT token',
                'data': None
            }, status=401)


def jwt_required_method(method_func):
    """
    JWT认证方法装饰器
    用于ViewSet中的特定方法，要求该方法的请求必须包含有效的JWT token
    
    使用方法:
    class MyViewSet(viewsets.ModelViewSet):
        @jwt_required_method
        def list(self, request):
            # 这个方法需要JWT认证
            pass
        
        def retrieve(self, request, pk=None):
            # 这个方法不需要JWT认证
            pass
    """
    @wraps(method_func)
    def wrapper(self, request, *args, **kwargs):
        try:
            # 尝试解析JWT payload
            payload = JWTHelper.get_jwt_payload(request)
            # 获取JWT令牌
            jwt_token = JWTHelper.get_current_jwt_token(request)
            
            # 设置当前用户信息
            JWTHelper.set_current_user(
                user_code=payload.get('user_code'),
                user_name=payload.get('user_name'),
                user_role=payload.get('user_role'),
                enterprise_code=payload.get('enterprise_code'),
                enterprise_name=payload.get('enterprise_name'),
                enterprise_level=payload.get('enterprise_level'),
                department_code=payload.get('department_code'),
                department_name=payload.get('department_name'),
                jwt_token=jwt_token
            )
            
            # 调用原始方法
            return method_func(self, request, *args, **kwargs)
            
        except AuthenticationFailed as e:
            return JsonResponse({
                'code': 4001,
                'msg': str(e),
                'data': None
            }, status=401)
        except Exception as e:
            return JsonResponse({
                'code': 4001,
                'msg': '认证失败，请提供有效的JWT token',
                'data': None
            }, status=401)
    
    return wrapper


def optional_jwt(view_func):
    """
    可选JWT认证装饰器
    尝试解析JWT token，但不强制要求
    如果有有效的JWT token，会设置用户信息；如果没有或无效，不会报错
    
    使用方法:
    @optional_jwt
    def my_view(request):
        user_id = JWTHelper.get_current_user_id()  # 可能为None
        if user_id:
            # 用户已登录的逻辑
            pass
        else:
            # 匿名用户的逻辑
            pass
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # 尝试解析JWT payload
            payload = JWTHelper.get_jwt_payload(request)
            # 获取JWT令牌
            jwt_token = JWTHelper.get_current_jwt_token(request)
            
            # 设置当前用户信息
            JWTHelper.set_current_user(
                user_code=payload.get('user_code'),
                user_name=payload.get('user_name'),
                user_role=payload.get('user_role'),
                enterprise_code=payload.get('enterprise_code'),
                enterprise_name=payload.get('enterprise_name'),
                enterprise_level=payload.get('enterprise_level'),
                department_code=payload.get('department_code'),
                department_name=payload.get('department_name'),
                jwt_token=jwt_token
            )
        except:
            # JWT解析失败，不设置用户信息，但继续处理请求
            pass
        
        # 调用原始视图函数
        return view_func(request, *args, **kwargs)
    
    return wrapper
