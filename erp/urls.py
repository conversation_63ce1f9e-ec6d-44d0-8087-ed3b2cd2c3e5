from django.urls import path, include
from rest_framework import routers

# 导入视图集
from erp.views.customer import CustomerViewSet
from erp.views.project import ProjectViewSet
from erp.views.supplier import SupplierViewSet
from erp.views.partner import PartnerViewSet
from erp.views.custom_field import CustomFieldViewSet
from erp.views.contract import ContractViewSet
from erp.views.attachment import AttachmentViewSet
from erp.views.account import AccountViewSet
from erp.views.invoice import InvoiceViewSet
from erp.views.contract_document import ContractDocumentViewSet
from erp.views.administrative_division import province_list, city_list
# from erp.views.dashboard import DashboardViewSet

# 创建路由器
router = routers.DefaultRouter()

# 注册视图集
router.register(r'customers', CustomerViewSet, basename='customer')
router.register(r'projects', ProjectViewSet, basename='project')
router.register(r'suppliers', SupplierViewSet, basename='supplier')
router.register(r'partners', PartnerViewSet, basename='partner')
router.register(r'custom-fields', CustomFieldViewSet, basename='custom-field')
router.register(r'contracts', ContractViewSet, basename='contract')
router.register(r'attachments', AttachmentViewSet, basename='attachment')
router.register(r'accounts', AccountViewSet, basename='account')
router.register(r'invoices', InvoiceViewSet, basename='invoice')
router.register(r'contract-documents', ContractDocumentViewSet, basename='contract-document')
# router.register(r'files', FileManagementViewSet, basename='file')
# router.register(r'dashboard', DashboardViewSet, basename='dashboard')

urlpatterns = [
    # API路由
    path('', include(router.urls)),

    # 行政区划相关接口
    path('provinces/', province_list, name='province-list'),
    path('provinces/<str:province_code>/cities/', city_list, name='city-list'),
    # path('cities/<str:city_code>/districts/', district_list, name='district-list'),

    # 其他自定义路由可以在这里添加
    # path('custom/', CustomView.as_view(), name='custom'),
]
