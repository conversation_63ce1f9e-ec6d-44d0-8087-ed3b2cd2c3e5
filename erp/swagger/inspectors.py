"""
自定义Swagger检查器
"""
from drf_yasg.inspectors import FilterInspector, CoreAPICompatInspector, PaginatorInspector
from drf_yasg import openapi
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON>Filter, OrderingFilter


class DjangoFilterDescriptionInspector(CoreAPICompatInspector):
    """
    自定义过滤器检查器，用于正确显示django-filter的help_text
    """

    def get_filter_parameters(self, filter_backend):
        """获取过滤器参数，包含正确的描述信息"""
        if isinstance(filter_backend, DjangoFilterBackend):
            result = []
            filterset_class = self.get_filterset_class(filter_backend)

            if filterset_class:
                for field_name, filter_field in filterset_class.base_filters.items():
                    # 获取help_text作为描述
                    description = getattr(filter_field, 'extra', {}).get('help_text', None)
                    if not description:
                        description = getattr(filter_field, 'help_text', None)
                    if not description:
                        description = f"Filter by {field_name}"

                    # 确定参数类型
                    param_type = openapi.TYPE_STRING
                    param_format = None

                    # 根据过滤器类型设置参数类型
                    if hasattr(filter_field, 'field'):
                        field = filter_field.field
                        if hasattr(field, '__class__'):
                            field_class_name = field.__class__.__name__
                            if 'Date' in field_class_name:
                                param_format = openapi.FORMAT_DATE
                            elif 'DateTime' in field_class_name:
                                param_format = openapi.FORMAT_DATETIME
                            elif 'Integer' in field_class_name:
                                param_type = openapi.TYPE_INTEGER
                            elif 'Float' in field_class_name or 'Decimal' in field_class_name:
                                param_type = openapi.TYPE_NUMBER
                            elif 'Boolean' in field_class_name:
                                param_type = openapi.TYPE_BOOLEAN

                    # 创建参数定义
                    parameter = openapi.Parameter(
                        name=field_name,
                        in_=openapi.IN_QUERY,
                        description=description,
                        type=param_type,
                        format=param_format,
                        required=False
                    )

                    result.append(parameter)

            return result

        # 处理其他类型的过滤器
        elif isinstance(filter_backend, (SearchFilter, OrderingFilter)):
            return super().get_filter_parameters(filter_backend)

        return []

    def get_filterset_class(self, filter_backend):
        """获取filterset类"""
        if hasattr(self.view, 'filterset_class') and self.view.filterset_class:
            return self.view.filterset_class

        if hasattr(self.view, 'get_filterset_class'):
            return self.view.get_filterset_class()

        return None


class CustomPaginatorInspector(PaginatorInspector):
    """
    自定义分页检查器，用于排除特定action的分页参数
    """

    def get_paginator_parameters(self, paginator):
        """获取分页参数，对特定action返回空列表"""
        # 检查当前视图的action
        if hasattr(self.view, 'action'):
            # 对于这些action，不显示分页参数
            if self.view.action in ['download', 'upload', 'category_choices']:
                return []

        # 其他action使用默认的分页参数
        return super().get_paginator_parameters(paginator)
