from drf_yasg import openapi

# 账款管理相关响应定义
account_summary_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'total_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='总金额'),
        'received_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='已收金额（销售合同）'),
        'paid_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='已付金额（采购合同）'),
        'pending_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='待收/待付金额'),
        'received_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='已收期数（销售合同）'),
        'paid_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='已付期数（采购合同）'),
        'pending_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='待收/待付期数'),
        'progress': openapi.Schema(type=openapi.TYPE_NUMBER, description='进度百分比'),
        'next_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='下期金额'),
        'next_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='下期日期', nullable=True),
        'type': openapi.Schema(type=openapi.TYPE_STRING, description='类型', enum=['receivable', 'payable']),
    }
)

contract_partner_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, description='相对方ID'),
        'name': openapi.Schema(type=openapi.TYPE_STRING, description='相对方名称'),
    }
)

contract_account_summary_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, description='合同ID'),
        'contract_name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
        'contract_code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
        'contract_category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类型', enum=['sales', 'procurement']),
        'contract_category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类型显示名'),
        'contract_total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额'),
        'project_id': openapi.Schema(type=openapi.TYPE_STRING, description='项目ID'),
        'project_name': openapi.Schema(type=openapi.TYPE_STRING, description='项目名称'),
        'contract_partners': openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=contract_partner_schema,
            description='合同相对方列表'
        ),
        'period_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='期数数量'),
        'last_period_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='最后一期日期', nullable=True),
        'last_period_status': openapi.Schema(type=openapi.TYPE_STRING, description='最后一期状态', enum=['pending', 'completed'], nullable=True),
        'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间'),
        'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间'),
    }
)

# 账款管理响应定义
account_list_responses = {
    200: openapi.Response(
        description='账款记录列表',
        schema=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应码'),
                'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                'data': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'items': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=contract_account_summary_schema,
                            description='账款记录列表'
                        ),
                        'page': openapi.Schema(type=openapi.TYPE_INTEGER, description='当前页码'),
                        'pages': openapi.Schema(type=openapi.TYPE_INTEGER, description='总页数'),
                        'limit': openapi.Schema(type=openapi.TYPE_INTEGER, description='每页数量'),
                        'total': openapi.Schema(type=openapi.TYPE_INTEGER, description='总记录数'),
                        'is_next': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否有下一页'),
                        'is_previous': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否有上一页'),
                    }
                )
            }
        )
    ),
    400: openapi.Response(description='请求参数错误'),
    401: openapi.Response(description='未授权'),
}

# 期数信息定义
payment_period_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, description='期数ID'),
        'period': openapi.Schema(type=openapi.TYPE_INTEGER, description='期数'),
        'amount': openapi.Schema(type=openapi.TYPE_STRING, description='金额'),
        'date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='日期'),
        'status': openapi.Schema(type=openapi.TYPE_STRING, description='状态', enum=['pending', 'completed']),
        'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='状态显示名'),
        'description': openapi.Schema(type=openapi.TYPE_STRING, description='说明', nullable=True),
        'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间'),
        'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间'),
    }
)

# 合同账款详情响应定义（与列表页一致）
account_detail_responses = {
    200: openapi.Response(
        description='合同账款详情',
        schema=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应码'),
                'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                'data': contract_account_summary_schema
            }
        )
    ),
    404: openapi.Response(description='合同不存在'),
    401: openapi.Response(description='未授权'),
}

# 合同账款状态跟踪响应定义（保留用于其他可能的接口）
account_status_responses = {
    200: openapi.Response(
        description='合同账款状态跟踪',
        schema=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应码'),
                'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
                'data': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'contract_info': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, description='合同ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
                                'code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
                                'category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类型'),
                                'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类型显示名'),
                                'total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额'),
                                'partners': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=contract_partner_schema,
                                    description='相对方列表'
                                ),
                            }
                        ),
                        'account_status': account_summary_schema,
                        'periods': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=payment_period_schema,
                            description='期数列表'
                        ),
                    }
                )
            }
        )
    ),
    404: openapi.Response(description='合同不存在'),
    401: openapi.Response(description='未授权'),
}

# 通用响应基础结构
base_success_response = {
    'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='响应代码', example=2000),
    'msg': openapi.Schema(type=openapi.TYPE_STRING, description='响应消息'),
}

# 通用错误响应
error_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'code': openapi.Schema(type=openapi.TYPE_INTEGER, description='错误代码', example=4000),
        'msg': openapi.Schema(type=openapi.TYPE_STRING, description='错误消息'),
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, description='错误详情', nullable=True)
    }
)

# 客户基础字段定义
customer_base_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='客户ID'),
    'code': openapi.Schema(type=openapi.TYPE_STRING, description='客户编码', example='C25070601'),
    'name': openapi.Schema(type=openapi.TYPE_STRING, description='客户名称'),
    'tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='纳税人识别号', nullable=True),
    'type': openapi.Schema(type=openapi.TYPE_STRING, description='客户类型（支持多选，逗号分隔）', example='C,G'),
    'status': openapi.Schema(type=openapi.TYPE_STRING, description='客户状态', enum=['ACTIVE', 'BLACKLISTED', 'DEACTIVATED']),
    'industry': openapi.Schema(type=openapi.TYPE_STRING, description='所属行业（支持多选，逗号分隔）',
                              example='FINANCE,GOVT'),
    'province_code': openapi.Schema(type=openapi.TYPE_STRING, description='省份代码', nullable=True),
    'province': openapi.Schema(type=openapi.TYPE_STRING, description='省份名称', nullable=True),
    'city_code': openapi.Schema(type=openapi.TYPE_STRING, description='城市代码', nullable=True),
    'city': openapi.Schema(type=openapi.TYPE_STRING, description='城市名称', nullable=True),
    'contact_person': openapi.Schema(type=openapi.TYPE_STRING, description='联系人'),
    'phone': openapi.Schema(type=openapi.TYPE_STRING, description='联系电话'),
    'email': openapi.Schema(type=openapi.TYPE_STRING, description='电子邮箱', nullable=True),
    'address': openapi.Schema(type=openapi.TYPE_STRING, description='联系地址', nullable=True),
    'contact_remark': openapi.Schema(type=openapi.TYPE_STRING, description='联系备注', nullable=True),
    'owner_name': openapi.Schema(type=openapi.TYPE_STRING, description='负责人姓名', nullable=True),
    'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注', nullable=True),
    'invoice_bank': openapi.Schema(type=openapi.TYPE_STRING, description='开户银行', nullable=True),
    'invoice_bank_account': openapi.Schema(type=openapi.TYPE_STRING, description='银行账号', nullable=True),
    'invoice_address': openapi.Schema(type=openapi.TYPE_STRING, description='开票地址', nullable=True),
    'invoice_phone': openapi.Schema(type=openapi.TYPE_STRING, description='开票电话', nullable=True),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间', example='2025-07-13 10:30:00'),
    'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间', example='2025-07-13 10:30:00'),
    'creator': openapi.Schema(type=openapi.TYPE_STRING, description='创建者', nullable=True),
    'updater': openapi.Schema(type=openapi.TYPE_STRING, description='更新者', nullable=True),
}

# 客户详情响应（包含额外字段和统计信息）
customer_detail_fields = {
    **customer_base_fields,
    'display_name': openapi.Schema(type=openapi.TYPE_STRING, description='显示名称'),
    'type_display': openapi.Schema(type=openapi.TYPE_STRING, description='客户类型显示名称'),
    'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='客户状态显示名称'),
    'industry_display': openapi.Schema(type=openapi.TYPE_STRING, description='行业显示名称'),
    'project_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='关联项目总数量（未删除）'),
    'sales_contract_count': openapi.Schema(type=openapi.TYPE_INTEGER, description='销售合同数量（未删除）'),
    'sales_contract_total_amount': openapi.Schema(type=openapi.TYPE_NUMBER, description='销售合同总金额'),
}

# 客户列表项字段（简化版）
customer_list_item_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='客户ID'),
    'code': openapi.Schema(type=openapi.TYPE_STRING, description='客户编码'),
    'name': openapi.Schema(type=openapi.TYPE_STRING, description='客户名称'),
    'tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='纳税人识别号', nullable=True),
    'type': openapi.Schema(type=openapi.TYPE_STRING, description='客户类型'),
    'status': openapi.Schema(type=openapi.TYPE_STRING, description='客户状态'),
    'industry': openapi.Schema(type=openapi.TYPE_STRING, description='所属行业'),
    'contact_person': openapi.Schema(type=openapi.TYPE_STRING, description='联系人'),
    'phone': openapi.Schema(type=openapi.TYPE_STRING, description='联系电话'),
    'email': openapi.Schema(type=openapi.TYPE_STRING, description='电子邮箱', nullable=True),
    'owner_name': openapi.Schema(type=openapi.TYPE_STRING, description='负责人姓名', nullable=True),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间'),
    'type_display': openapi.Schema(type=openapi.TYPE_STRING, description='客户类型显示名称'),
    'status_display': openapi.Schema(type=openapi.TYPE_STRING, description='客户状态显示名称'),
    'industry_display': openapi.Schema(type=openapi.TYPE_STRING, description='行业显示名称'),
}

# 分页信息
pagination_fields = {
    'total': openapi.Schema(type=openapi.TYPE_INTEGER, description='总记录数'),
    'pages': openapi.Schema(type=openapi.TYPE_INTEGER, description='总页数'),
    'page': openapi.Schema(type=openapi.TYPE_INTEGER, description='当前页码'),
}

# 客户列表响应
customer_list_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'items': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties=customer_list_item_fields
                    )
                ),
                **pagination_fields
            }
        )
    }
)

# 客户详情响应
customer_detail_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=customer_detail_fields
        )
    }
)

# 客户创建响应
customer_create_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=customer_detail_fields
        )
    }
)

# 客户更新响应
customer_update_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=customer_detail_fields
        )
    }
)

# 客户删除响应
customer_delete_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
    }
)

# ==================== 通用文件下载响应定义 ====================

# 通用文件下载成功响应
file_download_success_response = openapi.Response(
    description='文件下载成功',
    schema=openapi.Schema(
        type=openapi.TYPE_STRING,
        format=openapi.FORMAT_BINARY
    ),
    headers={
        'Content-Disposition': openapi.Schema(
            type=openapi.TYPE_STRING,
            description='文件下载头信息'
        ),
        'Content-Type': openapi.Schema(
            type=openapi.TYPE_STRING,
            description='文件MIME类型'
        )
    }
)

# Excel文件下载成功响应
excel_download_success_response = openapi.Response(
    description='Excel文件下载成功',
    schema=openapi.Schema(
        type=openapi.TYPE_STRING,
        format=openapi.FORMAT_BINARY
    ),
    headers={
        'Content-Disposition': openapi.Schema(
            type=openapi.TYPE_STRING,
            description='文件下载头信息'
        )
    }
)

# 文件下载400错误响应
file_download_400_response = openapi.Response(
    description='请求参数错误',
    schema=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='参数错误'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
)

# 文件下载404错误响应
file_download_404_response = openapi.Response(
    description='文件不存在',
    schema=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4004),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='文件不存在'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
)

# 文件下载500错误响应
file_download_500_response = openapi.Response(
    description='服务器内部错误',
    schema=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='文件下载失败'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
)

# Excel导出500错误响应
excel_export_500_response = openapi.Response(
    description='服务器内部错误',
    schema=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='导出Excel失败'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
)

# 通用文件下载响应集合
file_download_responses = {
    200: file_download_success_response,
    400: file_download_400_response,
    404: file_download_404_response,
    500: file_download_500_response
}

# Excel导出响应集合
excel_export_responses = {
    200: excel_download_success_response,
    400: file_download_400_response,
    500: excel_export_500_response
}

# 客户导出响应（保持向后兼容）
customer_export_response = excel_download_success_response

# 常用HTTP状态码响应定义
customer_responses = {
    200: customer_detail_response,
    201: customer_create_response,
    400: error_response,
    404: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4004),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='客户不存在'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    ),
    500: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='服务器内部错误'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
}

# 客户列表响应定义
customer_list_responses = {
    200: customer_list_response,
    400: error_response,
    500: customer_responses[500]
}

# 客户创建响应定义
customer_create_responses = {
    201: customer_create_response,
    400: error_response,
    500: customer_responses[500]
}

# 客户详情响应定义
customer_retrieve_responses = {
    200: customer_detail_response,
    404: customer_responses[404],
    500: customer_responses[500]
}

# 客户更新响应定义
customer_update_responses = {
    200: customer_update_response,
    400: error_response,
    404: customer_responses[404],
    500: customer_responses[500]
}

# 客户删除响应定义
customer_delete_responses = {
    200: customer_delete_response,
    404: customer_responses[404],
    500: customer_responses[500]
}

# 客户导出响应定义
customer_export_responses = excel_export_responses

# ==================== 合同响应模式定义 ====================

# 合同基础字段定义（与实际返回匹配）
contract_base_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='合同ID'),
    'code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号', example='SAL001-P001-C25070601'),
    'name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
    'category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别', enum=['sales', 'procurement']),
    'sign_status': openapi.Schema(type=openapi.TYPE_STRING, description='签约状态'),
    'performance_status': openapi.Schema(type=openapi.TYPE_STRING, description='履约状态'),
    'project': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='关联项目ID'),
    'partners': openapi.Schema(
        type=openapi.TYPE_ARRAY,
        items=openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        description='相对方ID列表'
    ),
    'total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额', example='100000.00'),
    'sales_manager': openapi.Schema(type=openapi.TYPE_STRING, description='销售负责人', nullable=True),
    'sign_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='签约日期', nullable=True, example='2025-07-13'),
    'effective_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='生效日期', nullable=True, example='2025-07-13'),
    'termination_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='终止日期', nullable=True, example='2025-12-31'),
    'remark': openapi.Schema(type=openapi.TYPE_STRING, description='合同备注', nullable=True),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间', example='2025-07-13 10:30:00'),
    'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间', example='2025-07-13 10:30:00'),
}

# 合同详情响应字段（包含额外字段，与实际返回匹配）
contract_detail_fields = {
    **contract_base_fields,
    'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别显示名称'),
    'sign_status_display': openapi.Schema(type=openapi.TYPE_STRING, description='签约状态显示名称'),
    'performance_status_display': openapi.Schema(type=openapi.TYPE_STRING, description='履约状态显示名称'),
    'project_name': openapi.Schema(type=openapi.TYPE_STRING, description='项目名称', nullable=True),
    'project_code': openapi.Schema(type=openapi.TYPE_STRING, description='项目编码', nullable=True),
    'partners_info': openapi.Schema(
        type=openapi.TYPE_ARRAY,
        items=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='相对方ID'),
                'code': openapi.Schema(type=openapi.TYPE_STRING, description='相对方编号'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='相对方名称'),
                'partner_type': openapi.Schema(type=openapi.TYPE_STRING, description='相对方类型'),
                'partner_type_display': openapi.Schema(type=openapi.TYPE_STRING, description='相对方类型显示名'),
            }
        ),
        description='相对方详细信息列表'
    ),
    'contract_duration_days': openapi.Schema(type=openapi.TYPE_INTEGER, description='合同期限天数', nullable=True),
    'is_expired': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否已过期'),
    'custom_field_values': openapi.Schema(
        type=openapi.TYPE_OBJECT,
        description='自定义字段值',
        additional_properties=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'field_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='字段ID'),
                'field_name': openapi.Schema(type=openapi.TYPE_STRING, description='字段名称'),
                'field_type': openapi.Schema(type=openapi.TYPE_STRING, description='字段类型'),
                'value': openapi.Schema(type=openapi.TYPE_STRING, description='字段值', nullable=True),
            }
        )
    ),
}

# 合同列表项字段（与实际返回匹配）
contract_list_item_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='合同ID'),
    'code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
    'name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
    'category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别'),
    'sign_status': openapi.Schema(type=openapi.TYPE_STRING, description='签约状态'),
    'performance_status': openapi.Schema(type=openapi.TYPE_STRING, description='履约状态'),
    'project': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='项目ID'),
    'partners': openapi.Schema(
        type=openapi.TYPE_ARRAY,
        items=openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        description='相对方ID列表'
    ),
    'total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额'),
    'sales_manager': openapi.Schema(type=openapi.TYPE_STRING, description='销售负责人', nullable=True),
    'sign_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='签约日期', nullable=True),
    'effective_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='生效日期', nullable=True),
    'termination_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='终止日期', nullable=True),
    'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注', nullable=True),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间'),
    'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间'),
    'creator': openapi.Schema(type=openapi.TYPE_STRING, description='创建人'),
    'updater': openapi.Schema(type=openapi.TYPE_STRING, description='更新人'),
    'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别显示名'),
    'sign_status_display': openapi.Schema(type=openapi.TYPE_STRING, description='签约状态显示名'),
    'performance_status_display': openapi.Schema(type=openapi.TYPE_STRING, description='履约状态显示名'),
    'project_name': openapi.Schema(type=openapi.TYPE_STRING, description='项目名称'),
    'project_code': openapi.Schema(type=openapi.TYPE_STRING, description='项目编号'),
    'project_type': openapi.Schema(type=openapi.TYPE_STRING, description='项目类型'),
    'project_type_display': openapi.Schema(type=openapi.TYPE_STRING, description='项目类型显示名'),
    'partners_info': openapi.Schema(
        type=openapi.TYPE_ARRAY,
        items=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='相对方ID'),
                'code': openapi.Schema(type=openapi.TYPE_STRING, description='相对方编号'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='相对方名称'),
                'partner_type': openapi.Schema(type=openapi.TYPE_STRING, description='相对方类型'),
                'partner_type_display': openapi.Schema(type=openapi.TYPE_STRING, description='相对方类型显示名'),
            }
        ),
        description='相对方详细信息列表'
    ),
    'partners_display': openapi.Schema(type=openapi.TYPE_STRING, description='相对方显示名（逗号分隔）'),
    'contract_duration_days': openapi.Schema(type=openapi.TYPE_INTEGER, description='合同期限天数', nullable=True),
    'is_expired': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否已过期'),
    'custom_field_values': openapi.Schema(
        type=openapi.TYPE_OBJECT,
        description='自定义字段值',
        additional_properties=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'field_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='字段ID'),
                'field_name': openapi.Schema(type=openapi.TYPE_STRING, description='字段名称'),
                'field_type': openapi.Schema(type=openapi.TYPE_STRING, description='字段类型'),
                'value': openapi.Schema(type=openapi.TYPE_STRING, description='字段值', nullable=True),
            }
        )
    ),
}

# 合同列表响应
contract_list_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'items': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties=contract_list_item_fields
                    )
                ),
                **pagination_fields
            }
        )
    }
)

# 合同详情响应
contract_detail_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=contract_detail_fields
        )
    }
)

# 合同创建响应
contract_create_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=contract_detail_fields
        )
    }
)

# 合同更新响应
contract_update_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=contract_detail_fields
        )
    }
)

# 合同删除响应
contract_delete_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
    }
)

# 合同响应定义
contract_list_responses = {
    200: contract_list_response,
    400: error_response,
    500: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='服务器内部错误'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
}

contract_create_responses = {
    201: contract_create_response,
    400: error_response,
    500: contract_list_responses[500]
}

contract_retrieve_responses = {
    200: contract_detail_response,
    404: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4004),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='合同不存在'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    ),
    500: contract_list_responses[500]
}

contract_update_responses = {
    200: contract_update_response,
    400: error_response,
    404: contract_retrieve_responses[404],
    500: contract_list_responses[500]
}

contract_delete_responses = {
    200: contract_delete_response,
    404: contract_retrieve_responses[404],
    500: contract_list_responses[500]
}

contract_complete_responses = {
    200: contract_update_response,
    400: error_response,
    404: contract_retrieve_responses[404],
    500: contract_list_responses[500]
}

contract_terminate_responses = {
    200: contract_update_response,
    400: error_response,
    404: contract_retrieve_responses[404],
    500: contract_list_responses[500]
}

# ==================== 附件响应模式定义 ====================

# 附件基础字段定义
attachment_base_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='附件ID'),
    'original_name': openapi.Schema(type=openapi.TYPE_STRING, description='原始文件名'),
    'file_name': openapi.Schema(type=openapi.TYPE_STRING, description='存储文件名'),
    'file_path': openapi.Schema(type=openapi.TYPE_STRING, description='文件存储路径'),
    'file_size': openapi.Schema(type=openapi.TYPE_INTEGER, description='文件大小（字节）'),
    'file_size_formatted': openapi.Schema(type=openapi.TYPE_STRING, description='格式化的文件大小', example='1.5 MB'),
    'file_type': openapi.Schema(type=openapi.TYPE_STRING, description='MIME类型', nullable=True),
    'file_extension': openapi.Schema(type=openapi.TYPE_STRING, description='文件扩展名', nullable=True),
    'file_md5': openapi.Schema(type=openapi.TYPE_STRING, description='文件MD5值', nullable=True),
    'description': openapi.Schema(type=openapi.TYPE_STRING, description='附件描述', nullable=True),
    'category': openapi.Schema(type=openapi.TYPE_STRING, description='附件类别',
                              enum=['project_trial_balance', 'project_other', 'contract_signed_scan',
                                   'contract_final_word', 'sales_invoice_scan', 'receipt_confirmation',
                                   'acceptance_report', 'purchase_invoice_scan', 'contract_other']),
    'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='附件类别显示名称'),
    'content_type': openapi.Schema(type=openapi.TYPE_INTEGER, description='关联模型类型ID'),
    'object_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='关联对象ID'),
    'related_model_name': openapi.Schema(type=openapi.TYPE_STRING, description='关联模型名称'),
    'related_object_display': openapi.Schema(type=openapi.TYPE_STRING, description='关联对象显示名称'),
    'is_image': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否为图片文件'),
    'is_document': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否为文档文件'),
    'is_archive': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否为压缩文件'),
    'file_icon': openapi.Schema(type=openapi.TYPE_STRING, description='文件图标'),
    'download_url': openapi.Schema(type=openapi.TYPE_STRING, description='下载链接'),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间', example='2025-07-13 10:30:00'),
    'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间', example='2025-07-13 10:30:00'),
    'creator': openapi.Schema(type=openapi.TYPE_STRING, description='创建者', nullable=True),
    'updater': openapi.Schema(type=openapi.TYPE_STRING, description='更新者', nullable=True),
}

# 附件列表响应
attachment_list_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'items': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties=attachment_base_fields
                    )
                ),
                **pagination_fields
            }
        )
    }
)

# 附件详情响应
attachment_detail_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=attachment_base_fields
        )
    }
)

# 附件上传响应
attachment_upload_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=attachment_base_fields
        )
    }
)

# 附件删除响应
attachment_delete_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
    }
)

# 附件下载响应（使用通用文件下载响应）
attachment_download_response = file_download_success_response

# 附件类别选择响应
attachment_category_choices_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'choices': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'value': openapi.Schema(type=openapi.TYPE_STRING, description='选项值'),
                            'label': openapi.Schema(type=openapi.TYPE_STRING, description='选项标签')
                        }
                    )
                )
            }
        )
    }
)



# 附件响应定义
attachment_list_responses = {
    200: attachment_list_response,
    400: error_response,
    500: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='服务器内部错误'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
}

attachment_retrieve_responses = {
    200: attachment_detail_response,
    404: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4004),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='该附件不存在'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    ),
    500: attachment_list_responses[500]
}

attachment_upload_responses = {
    201: attachment_upload_response,
    400: error_response,
    500: attachment_list_responses[500]
}



attachment_delete_responses = {
    200: attachment_delete_response,
    404: attachment_retrieve_responses[404],
    500: attachment_list_responses[500]
}

attachment_download_responses = file_download_responses

attachment_category_choices_responses = {
    200: attachment_category_choices_response,
    400: error_response,
    500: attachment_list_responses[500]
}

# ==================== 发票响应模式定义 ====================

# 发票基础字段定义
invoice_base_fields = {
    'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='发票ID'),
    'contract_name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
    'contract_code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
    'contract_partners': openapi.Schema(
        type=openapi.TYPE_ARRAY,
        items=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='相对方ID'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='相对方名称')
            }
        ),
        description='合同相对方列表'
    ),
    'invoice_amount': openapi.Schema(type=openapi.TYPE_STRING, description='开票金额', example='100000.00'),
    'invoice_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description='开票时间', example='2025-08-02'),
    'invoice_type': openapi.Schema(type=openapi.TYPE_STRING, description='发票类型', enum=['ordinary', 'special']),
    'invoice_type_display': openapi.Schema(type=openapi.TYPE_STRING, description='发票类型显示名'),
    'payee': openapi.Schema(type=openapi.TYPE_STRING, description='收款方'),
    'payee_tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='收款人纳税识别号'),
    'payer': openapi.Schema(type=openapi.TYPE_STRING, description='付款方'),
    'payer_tax_id': openapi.Schema(type=openapi.TYPE_STRING, description='付款人纳税识别号'),
    'invoice_code': openapi.Schema(type=openapi.TYPE_STRING, description='开票代码'),
    'invoice_number': openapi.Schema(type=openapi.TYPE_STRING, description='开票号码'),
    'verification_code': openapi.Schema(type=openapi.TYPE_STRING, description='开票校验码', nullable=True),
    'tax_rate': openapi.Schema(type=openapi.TYPE_STRING, description='税率', example='0.13'),
    'tax_amount': openapi.Schema(type=openapi.TYPE_STRING, description='税额', example='13000.00'),
    'description': openapi.Schema(type=openapi.TYPE_STRING, description='开票说明', nullable=True),
    'custom_fields': openapi.Schema(
        type=openapi.TYPE_OBJECT,
        description='自定义字段值',
        additional_properties=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'field_id': openapi.Schema(type=openapi.TYPE_STRING, description='字段ID'),
                'field_name': openapi.Schema(type=openapi.TYPE_STRING, description='字段名称'),
                'field_type': openapi.Schema(type=openapi.TYPE_STRING, description='字段类型'),
                'value': openapi.Schema(type=openapi.TYPE_STRING, description='字段值')
            }
        )
    ),
    'create_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='创建时间', example='2025-08-02 10:30:00'),
    'update_datetime': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='更新时间', example='2025-08-02 10:30:00'),
}

# 发票详情响应字段（包含额外字段）
invoice_detail_fields = {
    **invoice_base_fields,
    'contract_info': openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='合同ID'),
            'code': openapi.Schema(type=openapi.TYPE_STRING, description='合同编号'),
            'name': openapi.Schema(type=openapi.TYPE_STRING, description='合同名称'),
            'category': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别'),
            'category_display': openapi.Schema(type=openapi.TYPE_STRING, description='合同类别显示名'),
            'total_amount': openapi.Schema(type=openapi.TYPE_STRING, description='合同总额'),
            'partners': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='相对方ID'),
                        'name': openapi.Schema(type=openapi.TYPE_STRING, description='相对方名称')
                    }
                ),
                description='合同相对方列表'
            )
        },
        description='合同信息'
    ),
    'source_file_info': openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description='文件ID'),
            'filename': openapi.Schema(type=openapi.TYPE_STRING, description='文件名'),
            'file_size': openapi.Schema(type=openapi.TYPE_INTEGER, description='文件大小'),
            'file_type': openapi.Schema(type=openapi.TYPE_STRING, description='文件类型'),
            'upload_time': openapi.Schema(type=openapi.TYPE_STRING, format='date-time', description='上传时间')
        },
        description='源文件信息',
        nullable=True
    ),
    'creator': openapi.Schema(type=openapi.TYPE_STRING, description='创建者', nullable=True),
    'updater': openapi.Schema(type=openapi.TYPE_STRING, description='更新者', nullable=True),
}

# 发票列表响应
invoice_list_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'items': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties=invoice_base_fields
                    )
                ),
                **pagination_fields
            }
        )
    }
)

# 发票详情响应
invoice_detail_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=invoice_detail_fields
        )
    }
)

# 发票创建响应
invoice_create_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=invoice_detail_fields
        )
    }
)

# 发票更新响应
invoice_update_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties=invoice_detail_fields
        )
    }
)

# 发票删除响应
invoice_delete_response = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        **base_success_response,
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
    }
)

# 发票响应定义
invoice_list_responses = {
    200: invoice_list_response,
    400: error_response,
    500: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=5000),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='服务器内部错误'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    )
}

invoice_create_responses = {
    201: invoice_create_response,
    400: error_response,
    500: invoice_list_responses[500]
}

invoice_detail_responses = {
    200: invoice_detail_response,
    404: openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=4004),
            'msg': openapi.Schema(type=openapi.TYPE_STRING, example='发票不存在'),
            'data': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True)
        }
    ),
    500: invoice_list_responses[500]
}

invoice_update_responses = {
    200: invoice_update_response,
    400: error_response,
    404: invoice_detail_responses[404],
    500: invoice_list_responses[500]
}

invoice_delete_responses = {
    200: invoice_delete_response,
    404: invoice_detail_responses[404],
    500: invoice_list_responses[500]
}
