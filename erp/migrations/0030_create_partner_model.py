# Generated by Django 4.2.7 on 2025-07-30 04:39

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0029_fix_project_code_unique_constraint'),
    ]

    operations = [
        migrations.CreateModel(
            name='Partner',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.Char<PERSON>ield(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('code', models.CharField(max_length=50, verbose_name='相对方编码')),
                ('name', models.CharField(max_length=200, verbose_name='相对方名称')),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='纳税人识别号')),
                ('type', models.CharField(choices=[('C', '企业客户'), ('G', '政府客户'), ('ORIGINAL', '原厂'), ('CHANNEL', '渠道')], max_length=20, verbose_name='相对方类型')),
                ('status', models.CharField(choices=[('ACTIVE', '活跃'), ('SUSPENDED', '暂停'), ('BLACKLISTED', '黑名单'), ('DEACTIVATED', '已注销')], default='ACTIVE', max_length=20, verbose_name='状态')),
                ('industry', models.CharField(max_length=20, verbose_name='所属行业')),
                ('province_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='省份代码')),
                ('province', models.CharField(blank=True, max_length=50, null=True, verbose_name='省份名称')),
                ('city_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='城市代码')),
                ('city', models.CharField(blank=True, max_length=50, null=True, verbose_name='城市名称')),
                ('contact_person', models.CharField(max_length=100, verbose_name='联系人')),
                ('phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='电子邮箱')),
                ('address', models.TextField(blank=True, null=True, verbose_name='联系地址')),
                ('contact_remark', models.TextField(blank=True, null=True, verbose_name='联系备注')),
                ('website', models.URLField(blank=True, null=True, verbose_name='官网地址')),
                ('owner_name', models.CharField(max_length=100, verbose_name='负责人姓名')),
                ('owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人ID')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('invoice_bank', models.CharField(blank=True, max_length=100, null=True, verbose_name='开户银行')),
                ('invoice_bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='银行账号')),
                ('invoice_address', models.TextField(blank=True, null=True, verbose_name='开票地址')),
                ('invoice_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='开票电话')),
                ('invoice_remark', models.TextField(blank=True, null=True, verbose_name='开票备注')),
            ],
            options={
                'verbose_name': '相对方',
                'verbose_name_plural': '相对方',
                'db_table': 'erp_partner',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='PartnerPaymentInfo',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('bank_name', models.CharField(max_length=100, verbose_name='开户银行')),
                ('account_number', models.CharField(max_length=50, verbose_name='银行账号')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认账户')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_infos', to='erp.partner', verbose_name='相对方')),
            ],
            options={
                'verbose_name': '相对方收款信息',
                'verbose_name_plural': '相对方收款信息',
                'db_table': 'erp_partner_payment_info',
                'ordering': ['-is_default', '-create_datetime'],
            },
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['code'], name='erp_partner_code_7300c2_idx'),
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['name'], name='erp_partner_name_127aee_idx'),
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['type'], name='erp_partner_type_29f44a_idx'),
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['status'], name='erp_partner_status_a1c214_idx'),
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['industry'], name='erp_partner_industr_9c4dd6_idx'),
        ),
        migrations.AddIndex(
            model_name='partner',
            index=models.Index(fields=['create_datetime'], name='erp_partner_create__7def75_idx'),
        ),
        migrations.AddConstraint(
            model_name='partner',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('code',), name='unique_partner_code'),
        ),
        migrations.AddConstraint(
            model_name='partner',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('name',), name='unique_partner_name'),
        ),
        migrations.AddConstraint(
            model_name='partner',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True), ('tax_id__isnull', False)), fields=('tax_id',), name='unique_partner_tax_id'),
        ),
        migrations.AddIndex(
            model_name='partnerpaymentinfo',
            index=models.Index(fields=['partner', 'is_default'], name='erp_partner_partner_bd46d8_idx'),
        ),
        migrations.AddConstraint(
            model_name='partnerpaymentinfo',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True), ('is_default', True)), fields=('partner',), name='unique_default_payment_per_partner'),
        ),
    ]
