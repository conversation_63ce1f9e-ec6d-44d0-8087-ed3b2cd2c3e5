# Generated by Django 4.2.7 on 2025-07-17 09:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0026_add_customer_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='supplier',
            name='industry',
            field=models.CharField(choices=[('HARDWARE', '硬件产品供应商'), ('SOFTWARE', '软件产品供应商'), ('INTEGRATION', '集成/服务供应商')], default='HARDWARE', max_length=20, verbose_name='所属行业'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='type',
            field=models.CharField(choices=[('ORIGINAL', '原厂'), ('CHANNEL', '渠道')], default='ORIGINAL', max_length=20, verbose_name='供应商类型'),
        ),
    ]
