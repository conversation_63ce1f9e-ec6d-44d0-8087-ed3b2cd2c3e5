# Generated by Django 4.2.7 on 2025-07-07 02:17

from django.db import migrations, models


def update_industry_codes_forward(apps, schema_editor):
    """将行业代号从数字编码转换为英文代号"""
    Customer = apps.get_model('erp', 'Customer')

    # 定义转换映射
    code_mapping = {
        'I01': 'GOVT',
        'I02': 'FINANCE',
        'I03': 'IT',
        'I04': 'MANUFACTURING',
        'I05': 'RETAIL',
        'I06': 'ENERGY',
        'I07': 'LOGISTICS',
        'I08': 'HEALTHCARE',
        'I09': 'EDUCATION',
        'I10': 'REALESTATE',
        'I11': 'PROFESSIONAL',
        'I12': 'AGRICULTURE',
        'I13': 'OTHER',
    }

    # 批量更新现有数据
    for old_code, new_code in code_mapping.items():
        Customer.objects.filter(industry=old_code).update(industry=new_code)


def update_industry_codes_reverse(apps, schema_editor):
    """回滚：将英文代号转换回数字编码"""
    Customer = apps.get_model('erp', 'Customer')

    # 定义反向转换映射
    reverse_mapping = {
        'GOVT': 'I01',
        'FINANCE': 'I02',
        'IT': 'I03',
        'MANUFACTURING': 'I04',
        'RETAIL': 'I05',
        'ENERGY': 'I06',
        'LOGISTICS': 'I07',
        'HEALTHCARE': 'I08',
        'EDUCATION': 'I09',
        'REALESTATE': 'I10',
        'PROFESSIONAL': 'I11',
        'AGRICULTURE': 'I12',
        'OTHER': 'I13',
    }

    # 批量回滚数据
    for new_code, old_code in reverse_mapping.items():
        Customer.objects.filter(industry=new_code).update(industry=old_code)


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0008_change_progress_to_decimal'),
    ]

    operations = [
        # 首先扩展字段长度以容纳更长的英文代号
        migrations.AlterField(
            model_name='customer',
            name='industry',
            field=models.CharField(
                choices=[
                    ('GOVT', '政府机构'),
                    ('FINANCE', '金融服务'),
                    ('IT', '信息技术/互联网'),
                    ('MANUFACTURING', '制造与工业'),
                    ('RETAIL', '零售与消费品'),
                    ('ENERGY', '能源与公用事业'),
                    ('LOGISTICS', '交通与物流'),
                    ('HEALTHCARE', '医疗与健康'),
                    ('EDUCATION', '教育与科研'),
                    ('REALESTATE', '房地产与建筑'),
                    ('PROFESSIONAL', '专业服务'),
                    ('AGRICULTURE', '农林牧渔'),
                    ('OTHER', '其他/未分类'),
                ],
                default='OTHER',
                max_length=20,
                verbose_name='所属行业'
            ),
        ),
        # 然后执行数据转换
        migrations.RunPython(
            update_industry_codes_forward,
            update_industry_codes_reverse,
        ),
    ]
