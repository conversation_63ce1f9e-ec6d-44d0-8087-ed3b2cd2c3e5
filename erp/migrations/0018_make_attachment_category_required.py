# Generated by Django 4.2.7 on 2025-07-11 05:54

from django.db import migrations, models


def set_default_category_for_existing_attachments(apps, schema_editor):
    """为现有的附件设置默认类型"""
    Attachment = apps.get_model('erp', 'Attachment')
    ContentType = apps.get_model('contenttypes', 'ContentType')

    # 获取项目和合同的ContentType
    try:
        project_content_type = ContentType.objects.get(app_label='erp', model='project')
        contract_content_type = ContentType.objects.get(app_label='erp', model='contract')
    except ContentType.DoesNotExist:
        # 如果ContentType不存在，跳过
        return

    # 为项目附件设置默认类型
    Attachment.objects.filter(
        content_type=project_content_type,
        category__isnull=True
    ).update(category='project_other')

    # 为合同附件设置默认类型
    Attachment.objects.filter(
        content_type=contract_content_type,
        category__isnull=True
    ).update(category='contract_other')

    # 为其他类型的附件设置默认类型（如果有的话）
    Attachment.objects.filter(
        category__isnull=True
    ).update(category='project_other')


def reverse_set_default_category(apps, schema_editor):
    """回滚操作 - 将默认类型设置为NULL"""
    Attachment = apps.get_model('erp', 'Attachment')
    Attachment.objects.filter(
        category__in=['project_other', 'contract_other']
    ).update(category=None)


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0017_add_attachment_category'),
    ]

    operations = [
        # 首先为现有记录设置默认值
        migrations.RunPython(
            set_default_category_for_existing_attachments,
            reverse_set_default_category
        ),
        # 然后修改字段为必填
        migrations.AlterField(
            model_name='attachment',
            name='category',
            field=models.CharField(choices=[('project_trial_balance', '试算表'), ('project_other', '项目其他'), ('contract_signed_scan', '合同双章扫描件'), ('contract_final_word', '合同终稿word版'), ('sales_invoice_scan', '销售发票扫描件'), ('receipt_confirmation', '签收单'), ('acceptance_report', '验收报告'), ('purchase_invoice_scan', '采购发票扫描件'), ('contract_other', '合同其他')], help_text='附件分类，根据关联对象类型选择相应的类型', max_length=50, verbose_name='附件类别'),
        ),
    ]
