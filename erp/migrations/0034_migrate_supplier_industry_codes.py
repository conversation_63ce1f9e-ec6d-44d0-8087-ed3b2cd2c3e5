# Generated by Django 4.2.7 on 2025-07-30 11:39

from django.db import migrations


def migrate_supplier_industry_codes(apps, schema_editor):
    """迁移供应商行业代码到统一的行业分类"""
    Partner = apps.get_model('erp', 'Partner')

    # 定义旧行业代码到新行业代码的映射
    industry_mapping = {
        'HARDWARE': 'MANUFACTURING',  # 硬件产品供应商 -> 制造与工业
        'SOFTWARE': 'IT',            # 软件产品供应商 -> 信息技术/互联网
        'INTEGRATION': 'PROFESSIONAL', # 集成/服务供应商 -> 专业服务
    }

    # 更新供应商类型的相对方行业代码
    for old_code, new_code in industry_mapping.items():
        updated_count = Partner.objects.filter(
            type__in=['ORIGINAL', 'CHANNEL'],  # 只更新供应商类型
            industry=old_code,
            delete_datetime__isnull=True
        ).update(industry=new_code)

        if updated_count > 0:
            print(f"更新了 {updated_count} 个供应商的行业代码：{old_code} -> {new_code}")


def reverse_migrate_supplier_industry_codes(apps, schema_editor):
    """回滚操作：将新行业代码改回旧代码"""
    Partner = apps.get_model('erp', 'Partner')

    # 定义新行业代码到旧行业代码的映射（回滚用）
    reverse_mapping = {
        'MANUFACTURING': 'HARDWARE',
        'IT': 'SOFTWARE',
        'PROFESSIONAL': 'INTEGRATION',
    }

    # 回滚供应商类型的相对方行业代码
    for new_code, old_code in reverse_mapping.items():
        updated_count = Partner.objects.filter(
            type__in=['ORIGINAL', 'CHANNEL'],  # 只更新供应商类型
            industry=new_code,
            delete_datetime__isnull=True
        ).update(industry=old_code)

        if updated_count > 0:
            print(f"回滚了 {updated_count} 个供应商的行业代码：{new_code} -> {old_code}")


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0033_update_industry_choices'),
    ]

    operations = [
        migrations.RunPython(migrate_supplier_industry_codes, reverse_migrate_supplier_industry_codes),
    ]
