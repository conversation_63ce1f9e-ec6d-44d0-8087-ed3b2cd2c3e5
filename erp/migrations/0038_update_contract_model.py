# Generated by Django 4.2.7 on 2025-07-31 06:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0037_partner_partner_type'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='contract',
            name='erp_contrac_status_567300_idx',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='completion_datetime',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='completion_remark',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='end_date',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='start_date',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='status',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='supplier',
        ),
        migrations.RemoveField(
            model_name='contract',
            name='termination_remark',
        ),
        migrations.AddField(
            model_name='contract',
            name='effective_date',
            field=models.DateField(blank=True, null=True, verbose_name='生效日期'),
        ),
        migrations.AddField(
            model_name='contract',
            name='partners',
            field=models.ManyToManyField(help_text='支持多选，例如三方合同', related_name='contracts', to='erp.partner', verbose_name='相对方'),
        ),
        migrations.AddField(
            model_name='contract',
            name='performance_status',
            field=models.CharField(choices=[('not_performed', '未履约'), ('performing', '履约中'), ('performed', '已履约')], default='not_performed', max_length=20, verbose_name='履约状态'),
        ),
        migrations.AddField(
            model_name='contract',
            name='sales_manager',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='销售负责人'),
        ),
        migrations.AddField(
            model_name='contract',
            name='sign_status',
            field=models.CharField(choices=[('unsigned', '未签约'), ('communicating', '沟通中'), ('signing', '签约中'), ('signed', '已签约'), ('terminating', '解约中')], default='unsigned', max_length=20, verbose_name='签订状态'),
        ),
        migrations.AddField(
            model_name='contract',
            name='termination_date',
            field=models.DateField(blank=True, null=True, verbose_name='终止日期'),
        ),
        migrations.AddField(
            model_name='contract',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='合同总额'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='contract',
            name='category',
            field=models.CharField(choices=[('sales', '销售合同'), ('procurement', '采购合同')], max_length=20, verbose_name='合同类型'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='contracts', to='erp.project', verbose_name='所属项目'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='remark',
            field=models.TextField(blank=True, null=True, verbose_name='备注'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='sign_date',
            field=models.DateField(blank=True, null=True, verbose_name='签订日期'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['sign_status'], name='erp_contrac_sign_st_c0613b_idx'),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['performance_status'], name='erp_contrac_perform_a46ff2_idx'),
        ),
    ]
