# Generated by Django 4.2.7 on 2025-08-01 10:09

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0039_make_sales_manager_optional'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentPeriod',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.Char<PERSON>ield(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('period', models.PositiveIntegerField(help_text='第几期', verbose_name='期数')),
                ('amount', models.DecimalField(decimal_places=2, help_text='本期应收/应付金额', max_digits=15, verbose_name='期数金额')),
                ('due_date', models.DateField(help_text='计划收付款时间', verbose_name='计划时间')),
                ('actual_date', models.DateField(blank=True, help_text='实际收付款时间', null=True, verbose_name='实际时间')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('completed', '已完成')], default='pending', max_length=20, verbose_name='状态')),
                ('description', models.TextField(blank=True, help_text='期数说明或备注', null=True, verbose_name='说明')),
            ],
            options={
                'verbose_name': '付款期数',
                'verbose_name_plural': '付款期数',
                'db_table': 'erp_payment_period',
                'ordering': ['contract', 'period'],
            },
        ),
        migrations.RemoveIndex(
            model_name='project',
            name='erp_project_sales_m_732996_idx',
        ),
        migrations.AddField(
            model_name='paymentperiod',
            name='contract',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='payment_periods', to='erp.contract', verbose_name='关联合同'),
        ),
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['contract'], name='erp_payment_contrac_a1ae68_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['status'], name='erp_payment_status_388089_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['due_date'], name='erp_payment_due_dat_3b1bb8_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['actual_date'], name='erp_payment_actual__1e678e_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['create_datetime'], name='erp_payment_create__ebbb18_idx'),
        ),
        migrations.AddConstraint(
            model_name='paymentperiod',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('contract', 'period'), name='unique_payment_period_contract_period_when_not_deleted'),
        ),
    ]
