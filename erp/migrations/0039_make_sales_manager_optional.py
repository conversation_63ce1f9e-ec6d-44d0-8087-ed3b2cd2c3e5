# Generated by Django 4.2.7 on 2025-08-01 01:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0038_update_contract_model'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='customfield',
            name='target_model',
            field=models.CharField(choices=[('partner', '相对方'), ('project', '项目'), ('contract', '合同')], max_length=50, verbose_name='目标模块'),
        ),
        migrations.AlterField(
            model_name='project',
            name='sales_manager_id',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='销售负责人ID'),
        ),
        migrations.AlterField(
            model_name='project',
            name='sales_manager_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='销售负责人姓名'),
        ),
    ]
