# Generated by Django 4.2.7 on 2025-07-30 09:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0031_create_custom_field_models'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contract',
            name='customer',
            field=models.ForeignKey(blank=True, limit_choices_to={'type__in': ['C', 'G']}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='sales_contracts', to='erp.partner', verbose_name='客户'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='supplier',
            field=models.ForeignKey(blank=True, limit_choices_to={'type__in': ['ORIGINAL', 'CHANNEL']}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='purchase_contracts', to='erp.partner', verbose_name='供应商'),
        ),
        migrations.AlterField(
            model_name='project',
            name='customer',
            field=models.ForeignKey(limit_choices_to={'type__in': ['C', 'G']}, on_delete=django.db.models.deletion.PROTECT, related_name='projects', to='erp.partner', verbose_name='关联客户'),
        ),
    ]
