# Generated by Django 4.2.7 on 2025-07-17 09:24

from django.db import migrations


def migrate_supplier_type_data(apps, schema_editor):
    """将供应商类型从旧值迁移到新值"""
    Supplier = apps.get_model('erp', 'Supplier')

    # 类型映射：旧值 -> 新值
    type_mapping = {
        'C': 'ORIGINAL',  # 默认将企业供应商映射为原厂
        'G': 'CHANNEL',   # 默认将政府供应商映射为渠道
    }

    # 更新所有供应商记录
    for supplier in Supplier.objects.all():
        if supplier.type in type_mapping:
            supplier.type = type_mapping[supplier.type]
            supplier.save(update_fields=['type'])


def migrate_supplier_industry_data(apps, schema_editor):
    """将供应商行业从旧值迁移到新值"""
    Supplier = apps.get_model('erp', 'Supplier')

    # 行业映射：旧值 -> 新值
    industry_mapping = {
        # 默认映射规则
        'IT': 'SOFTWARE',           # IT/互联网 -> 软件产品
        'MANUFACTURING': 'HARDWARE',  # 制造与工业 -> 硬件产品
        'PROFESSIONAL': 'INTEGRATION', # 专业服务 -> 集成/服务

        # 其他行业映射到最合适的新类别
        'GOVT': 'INTEGRATION',
        'FINANCE': 'SOFTWARE',
        'RETAIL': 'HARDWARE',
        'ENERGY': 'HARDWARE',
        'LOGISTICS': 'INTEGRATION',
        'HEALTHCARE': 'SOFTWARE',
        'EDUCATION': 'SOFTWARE',
        'REALESTATE': 'INTEGRATION',
        'AGRICULTURE': 'HARDWARE',
        'OTHER': 'INTEGRATION',
    }

    # 更新所有供应商记录
    for supplier in Supplier.objects.all():
        if supplier.industry in industry_mapping:
            supplier.industry = industry_mapping[supplier.industry]
            supplier.save(update_fields=['industry'])


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0027_update_supplier_field_choices'),
    ]

    operations = [
        migrations.RunPython(migrate_supplier_type_data),
        migrations.RunPython(migrate_supplier_industry_data),
    ]
