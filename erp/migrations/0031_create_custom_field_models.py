# Generated by Django 4.2.7 on 2025-07-30 08:23

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0030_create_partner_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomField',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('field_name', models.CharField(max_length=200, verbose_name='字段显示名称')),
                ('field_type', models.CharField(choices=[('text', '文本'), ('number', '数字'), ('date', '日期'), ('select', '单选'), ('multiselect', '多选'), ('currency', '货币'), ('boolean', '布尔值')], max_length=20, verbose_name='字段类型')),
                ('target_model', models.CharField(choices=[('partner', '相对方'), ('contract', '合同'), ('supplier', '供应商'), ('project', '项目')], max_length=50, verbose_name='目标模块')),
                ('field_options', models.TextField(blank=True, null=True, verbose_name='选项值')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '自定义字段',
                'verbose_name_plural': '自定义字段',
                'db_table': 'erp_custom_field',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='CustomFieldValue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('object_id', models.UUIDField(verbose_name='对象ID')),
                ('text_value', models.TextField(blank=True, null=True, verbose_name='文本值')),
                ('number_value', models.DecimalField(blank=True, decimal_places=6, max_digits=20, null=True, verbose_name='数字值')),
                ('date_value', models.DateField(blank=True, null=True, verbose_name='日期值')),
                ('boolean_value', models.BooleanField(blank=True, null=True, verbose_name='布尔值')),
                ('json_value', models.TextField(blank=True, null=True, verbose_name='JSON值')),
                ('custom_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='erp.customfield', verbose_name='自定义字段')),
            ],
            options={
                'verbose_name': '自定义字段值',
                'verbose_name_plural': '自定义字段值',
                'db_table': 'erp_custom_field_value',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.AddIndex(
            model_name='customfield',
            index=models.Index(fields=['target_model', 'is_active'], name='erp_custom__target__6f82dd_idx'),
        ),
        migrations.AddIndex(
            model_name='customfield',
            index=models.Index(fields=['field_name', 'target_model'], name='erp_custom__field_n_b3e81d_idx'),
        ),
        migrations.AddConstraint(
            model_name='customfield',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('field_name', 'target_model'), name='unique_field_name_per_model'),
        ),
        migrations.AddIndex(
            model_name='customfieldvalue',
            index=models.Index(fields=['object_id'], name='erp_custom__object__f330a4_idx'),
        ),
        migrations.AddIndex(
            model_name='customfieldvalue',
            index=models.Index(fields=['custom_field', 'object_id'], name='erp_custom__custom__4385ae_idx'),
        ),
        migrations.AddConstraint(
            model_name='customfieldvalue',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('custom_field', 'object_id'), name='unique_field_value_per_object'),
        ),
    ]
