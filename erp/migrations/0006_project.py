# Generated by Django 4.2.7 on 2025-07-04 08:06

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0005_integrate_invoice_info_to_customer'),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.Char<PERSON>ield(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='项目编号')),
                ('name', models.CharField(max_length=200, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, null=True, verbose_name='项目描述')),
                ('type', models.CharField(choices=[('system_integration', '系统集成'), ('software_development', '软件开发'), ('product_own', '产品（自有）'), ('product_external', '产品（外采）'), ('service_own', '服务（自有）'), ('service_external', '服务（外采）'), ('other', '其他（行政、租房、人事等）')], max_length=30, verbose_name='项目类型')),
                ('status', models.CharField(choices=[('preparing', '准备中'), ('in_progress', '进行中'), ('paused', '暂停'), ('completed', '已完成'), ('terminated', '已终止')], default='preparing', max_length=20, verbose_name='项目状态')),
                ('reason', models.CharField(choices=[('bidding', '投标'), ('signed', '签约'), ('poc', '概念验证（POC）')], max_length=20, verbose_name='立项理由')),
                ('customer_code', models.CharField(max_length=50, verbose_name='客户编号')),
                ('customer_name', models.CharField(max_length=200, verbose_name='客户名称')),
                ('end_user_name', models.CharField(max_length=200, verbose_name='最终用户名称')),
                ('end_user_contact', models.CharField(blank=True, max_length=100, null=True, verbose_name='最终用户联系人')),
                ('end_user_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='最终用户电话')),
                ('end_user_address', models.TextField(blank=True, null=True, verbose_name='最终用户地址')),
                ('manager_id', models.CharField(max_length=50, verbose_name='项目经理ID')),
                ('manager_name', models.CharField(max_length=100, verbose_name='项目经理姓名')),
                ('start_date', models.DateField(verbose_name='项目开始日期')),
                ('end_date', models.DateField(verbose_name='项目结束日期')),
                ('budget', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='项目预算')),
                ('expected_profit_rate', models.DecimalField(decimal_places=2, help_text='百分比，如25.50表示25.5%', max_digits=5, verbose_name='预计毛利率')),
                ('progress', models.IntegerField(default=0, help_text='百分比，0-100', verbose_name='项目进度')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='projects', to='erp.customer', verbose_name='关联客户')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'db_table': 'erp_project',
                'ordering': ['-create_datetime'],
                'indexes': [models.Index(fields=['code'], name='erp_project_code_507ab0_idx'), models.Index(fields=['name'], name='erp_project_name_1de655_idx'), models.Index(fields=['type'], name='erp_project_type_e315cc_idx'), models.Index(fields=['status'], name='erp_project_status_6637f1_idx'), models.Index(fields=['customer'], name='erp_project_custome_9e841e_idx'), models.Index(fields=['manager_id'], name='erp_project_manager_356623_idx'), models.Index(fields=['start_date'], name='erp_project_start_d_7c5028_idx'), models.Index(fields=['end_date'], name='erp_project_end_dat_155229_idx'), models.Index(fields=['create_datetime'], name='erp_project_create__ee011c_idx')],
            },
        ),
    ]
