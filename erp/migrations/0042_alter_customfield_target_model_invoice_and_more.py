# Generated by Django 4.2.7 on 2025-08-02 07:17

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0041_remove_paymentperiod_erp_payment_due_dat_3b1bb8_idx_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customfield',
            name='target_model',
            field=models.CharField(choices=[('partner', '相对方'), ('project', '项目'), ('contract', '合同'), ('invoice', '发票')], max_length=50, verbose_name='目标模块'),
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('invoice_amount', models.DecimalField(decimal_places=2, help_text='发票开具金额', max_digits=15, verbose_name='开票金额')),
                ('invoice_date', models.DateField(verbose_name='开票时间')),
                ('invoice_type', models.CharField(choices=[('ordinary', '普票'), ('special', '专票')], max_length=20, verbose_name='开票类型')),
                ('payee', models.CharField(max_length=200, verbose_name='收款方')),
                ('payee_tax_id', models.CharField(help_text='收款方的纳税人识别号', max_length=50, verbose_name='收款人纳税识别号')),
                ('payer', models.CharField(max_length=200, verbose_name='付款方')),
                ('payer_tax_id', models.CharField(help_text='付款方的纳税人识别号', max_length=50, verbose_name='付款人纳税识别号')),
                ('invoice_code', models.CharField(help_text='发票代码', max_length=50, verbose_name='开票代码')),
                ('invoice_number', models.CharField(help_text='发票号码', max_length=50, verbose_name='开票号码')),
                ('verification_code', models.CharField(blank=True, help_text='发票校验码（可选）', max_length=50, verbose_name='开票校验码')),
                ('tax_rate', models.DecimalField(decimal_places=4, help_text='税率，如0.13表示13%', max_digits=5, verbose_name='税率')),
                ('tax_amount', models.DecimalField(decimal_places=2, help_text='税额金额', max_digits=15, verbose_name='税额')),
                ('description', models.TextField(blank=True, help_text='发票相关说明或备注', verbose_name='开票说明')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='erp.contract', verbose_name='关联合同')),
                ('source_file', models.ForeignKey(blank=True, help_text='发票的原始文件', null=True, on_delete=django.db.models.deletion.SET_NULL, to='erp.attachment', verbose_name='发票源文件')),
            ],
            options={
                'verbose_name': '发票',
                'verbose_name_plural': '发票',
                'db_table': 'erp_invoice',
                'ordering': ['-invoice_date', '-create_datetime'],
                'indexes': [models.Index(fields=['contract'], name='erp_invoice_contrac_e178fa_idx'), models.Index(fields=['invoice_date'], name='erp_invoice_invoice_bffc5a_idx'), models.Index(fields=['invoice_type'], name='erp_invoice_invoice_6e4a36_idx'), models.Index(fields=['invoice_number'], name='erp_invoice_invoice_111ddd_idx'), models.Index(fields=['invoice_code'], name='erp_invoice_invoice_8011d4_idx'), models.Index(fields=['create_datetime'], name='erp_invoice_create__a79605_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='invoice',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True)), fields=('invoice_code', 'invoice_number'), name='unique_invoice_code_number_when_not_deleted'),
        ),
    ]
