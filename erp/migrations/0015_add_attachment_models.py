# Generated by Django 4.2.7 on 2025-07-10 09:30

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('erp', '0014_add_completion_datetime_to_project'),
    ]

    operations = [
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('original_name', models.CharField(max_length=255, verbose_name='原始文件名')),
                ('file_name', models.CharField(help_text='UUID+扩展名', max_length=255, verbose_name='存储文件名')),
                ('file_path', models.CharField(max_length=500, verbose_name='文件存储路径')),
                ('file_size', models.BigIntegerField(help_text='单位：字节', verbose_name='文件大小')),
                ('file_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='MIME类型')),
                ('file_extension', models.CharField(blank=True, max_length=10, null=True, verbose_name='文件扩展名')),
                ('file_md5', models.CharField(blank=True, help_text='用于文件去重', max_length=32, null=True, verbose_name='文件MD5值')),
                ('object_id', models.UUIDField(verbose_name='关联对象ID')),
                ('description', models.TextField(blank=True, null=True, verbose_name='附件描述')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='关联模型类型')),
            ],
            options={
                'verbose_name': '附件',
                'verbose_name_plural': '附件',
                'db_table': 'erp_attachment',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='AttachmentDownloadLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('user_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='下载用户ID')),
                ('user_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='下载用户名')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('download_datetime', models.DateTimeField(auto_now_add=True, verbose_name='下载时间')),
                ('attachment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='download_logs', to='erp.attachment', verbose_name='附件')),
            ],
            options={
                'verbose_name': '附件下载日志',
                'verbose_name_plural': '附件下载日志',
                'db_table': 'erp_attachment_download_log',
                'ordering': ['-download_datetime'],
                'indexes': [models.Index(fields=['attachment'], name='erp_attachm_attachm_3d2770_idx'), models.Index(fields=['user_id'], name='erp_attachm_user_id_11241d_idx'), models.Index(fields=['download_datetime'], name='erp_attachm_downloa_3ce458_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='attachment',
            index=models.Index(fields=['content_type', 'object_id'], name='erp_attachm_content_9b0a2c_idx'),
        ),
        migrations.AddIndex(
            model_name='attachment',
            index=models.Index(fields=['file_md5'], name='erp_attachm_file_md_0d0eec_idx'),
        ),
        migrations.AddIndex(
            model_name='attachment',
            index=models.Index(fields=['create_datetime'], name='erp_attachm_create__a2365e_idx'),
        ),
    ]
