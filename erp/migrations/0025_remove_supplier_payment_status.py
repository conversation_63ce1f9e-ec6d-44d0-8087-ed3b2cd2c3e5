# Generated by Django 4.2.7 on 2025-07-14 05:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0024_alter_project_type'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='supplierpaymentinfo',
            name='erp_supplie_supplie_fb8210_idx',
        ),
        migrations.RemoveField(
            model_name='supplierpaymentinfo',
            name='status',
        ),
        migrations.AlterField(
            model_name='customer',
            name='industry',
            field=models.CharField(choices=[('GOVT', '政府机构'), ('FINANCE', '金融服务'), ('IT', '信息技术/互联网'), ('MANUFACTURING', '制造与工业'), ('RETAIL', '零售与消费品'), ('ENERGY', '能源与公用事业'), ('LOGISTICS', '交通与物流'), ('HEALTHCARE', '医疗与健康'), ('EDUCATION', '教育与科研'), ('REALESTATE', '房地产与建筑'), ('PROFESSIONAL', '专业服务'), ('AGRICULTURE', '农林牧渔'), ('OTHER', '其他/未分类')], default='OTHER', max_length=20, verbose_name='所属行业'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='type',
            field=models.CharField(choices=[('C', '企业客户'), ('G', '政府客户')], default='C', max_length=10, verbose_name='客户类型'),
        ),
        migrations.AlterField(
            model_name='project',
            name='type',
            field=models.CharField(choices=[('system_integration', '系统集成'), ('software_development', '软件开发'), ('product_own', '产品（自有）'), ('product_external', '产品（外采）'), ('service_own', '服务（自有）'), ('service_external', '服务（外采）'), ('other', '其他（行政、租房、人事等）')], max_length=30, verbose_name='项目类型'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='industry',
            field=models.CharField(choices=[('GOVT', '政府机构'), ('FINANCE', '金融服务'), ('IT', '信息技术/互联网'), ('MANUFACTURING', '制造与工业'), ('RETAIL', '零售与消费品'), ('ENERGY', '能源与公用事业'), ('LOGISTICS', '交通与物流'), ('HEALTHCARE', '医疗与健康'), ('EDUCATION', '教育与科研'), ('REALESTATE', '房地产与建筑'), ('PROFESSIONAL', '专业服务'), ('AGRICULTURE', '农林牧渔'), ('OTHER', '其他/未分类')], default='OTHER', max_length=20, verbose_name='所属行业'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='type',
            field=models.CharField(choices=[('C', '企业供应商'), ('G', '政府供应商')], default='C', max_length=10, verbose_name='供应商类型'),
        ),
    ]
