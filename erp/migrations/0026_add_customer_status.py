# Generated by Django 4.2.7 on 2025-07-14 07:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0025_remove_supplier_payment_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='status',
            field=models.CharField(choices=[('ACTIVE', '活跃'), ('BLACKLISTED', '黑名单'), ('DEACTIVATED', '已注销')], default='ACTIVE', max_length=20, verbose_name='客户状态'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['status'], name='erp_custome_status_96133b_idx'),
        ),
    ]
