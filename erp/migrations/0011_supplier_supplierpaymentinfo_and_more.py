# Generated by Django 4.2.7 on 2025-07-07 07:39

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0010_add_province_city_names_to_customer'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.Char<PERSON>ield(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('code', models.CharField(max_length=50, verbose_name='供应商编码')),
                ('name', models.CharField(max_length=200, verbose_name='供应商名称')),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='纳税人识别号')),
                ('type', models.CharField(choices=[('C', '企业供应商'), ('G', '政府供应商')], default='C', max_length=10, verbose_name='供应商类型')),
                ('status', models.CharField(choices=[('ACTIVE', '活跃'), ('SUSPENDED', '暂停'), ('BLACKLISTED', '黑名单')], default='ACTIVE', max_length=20, verbose_name='合作状态')),
                ('industry', models.CharField(choices=[('GOVT', '政府机构'), ('FINANCE', '金融服务'), ('IT', '信息技术/互联网'), ('MANUFACTURING', '制造与工业'), ('RETAIL', '零售与消费品'), ('ENERGY', '能源与公用事业'), ('LOGISTICS', '交通与物流'), ('HEALTHCARE', '医疗与健康'), ('EDUCATION', '教育与科研'), ('REALESTATE', '房地产与建筑'), ('PROFESSIONAL', '专业服务'), ('AGRICULTURE', '农林牧渔'), ('OTHER', '其他/未分类')], default='OTHER', max_length=20, verbose_name='所属行业')),
                ('province_code', models.CharField(blank=True, help_text='省级行政区划代码', max_length=6, null=True, verbose_name='省份代码')),
                ('province', models.CharField(blank=True, help_text='省份名称，冗余存储', max_length=50, null=True, verbose_name='省份名称')),
                ('city_code', models.CharField(blank=True, help_text='市级行政区划代码', max_length=6, null=True, verbose_name='城市代码')),
                ('city', models.CharField(blank=True, help_text='城市名称，冗余存储', max_length=50, null=True, verbose_name='城市名称')),
                ('contact_person', models.CharField(max_length=100, verbose_name='联系人')),
                ('phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='电子邮箱')),
                ('address', models.TextField(blank=True, null=True, verbose_name='联系地址')),
                ('contact_remark', models.TextField(blank=True, null=True, verbose_name='联系备注')),
                ('website', models.URLField(blank=True, null=True, verbose_name='官网地址')),
                ('owner_name', models.CharField(max_length=100, verbose_name='负责人姓名')),
                ('owner_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人ID')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('invoice_bank', models.CharField(blank=True, max_length=100, null=True, verbose_name='开户银行')),
                ('invoice_bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='银行账号')),
                ('invoice_address', models.TextField(blank=True, null=True, verbose_name='开票地址')),
                ('invoice_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='开票电话')),
                ('invoice_remark', models.TextField(blank=True, null=True, verbose_name='开票备注')),
            ],
            options={
                'verbose_name': '供应商',
                'verbose_name_plural': '供应商',
                'db_table': 'erp_supplier',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='SupplierPaymentInfo',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('bank_name', models.CharField(max_length=100, verbose_name='开户银行')),
                ('account_number', models.CharField(max_length=50, verbose_name='银行账号')),
                ('account_name', models.CharField(max_length=100, verbose_name='账户名称')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认账户')),
                ('status', models.CharField(choices=[('ACTIVE', '启用'), ('INACTIVE', '禁用')], default='ACTIVE', max_length=20, verbose_name='账户状态')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_infos', to='erp.supplier', verbose_name='供应商')),
            ],
            options={
                'verbose_name': '供应商收款信息',
                'verbose_name_plural': '供应商收款信息',
                'db_table': 'erp_supplier_payment_info',
                'ordering': ['-is_default', '-create_datetime'],
            },
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['code'], name='erp_supplie_code_a9f973_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['name'], name='erp_supplie_name_62235d_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['type'], name='erp_supplie_type_0d273a_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['status'], name='erp_supplie_status_1647d4_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['create_datetime'], name='erp_supplie_create__50881d_idx'),
        ),
        migrations.AddIndex(
            model_name='supplierpaymentinfo',
            index=models.Index(fields=['supplier', 'is_default'], name='erp_supplie_supplie_343af2_idx'),
        ),
        migrations.AddIndex(
            model_name='supplierpaymentinfo',
            index=models.Index(fields=['supplier', 'status'], name='erp_supplie_supplie_fb8210_idx'),
        ),
        migrations.AddConstraint(
            model_name='supplierpaymentinfo',
            constraint=models.UniqueConstraint(condition=models.Q(('delete_datetime__isnull', True), ('is_default', True)), fields=('supplier',), name='unique_default_payment_per_supplier'),
        ),
    ]
