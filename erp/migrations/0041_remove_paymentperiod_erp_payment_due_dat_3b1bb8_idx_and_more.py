# Generated by Django 4.2.7 on 2025-08-02 06:36

import datetime
from django.db import migrations, models


def migrate_date_fields(apps, schema_editor):
    """将due_date和actual_date数据迁移到新的date字段"""
    PaymentPeriod = apps.get_model('erp', 'PaymentPeriod')

    for period in PaymentPeriod.objects.all():
        # 优先使用actual_date，如果没有则使用due_date
        if period.actual_date:
            period.date = period.actual_date
        else:
            period.date = period.due_date
        period.save()


def reverse_migrate_date_fields(apps, schema_editor):
    """反向迁移：将date字段数据迁移回due_date"""
    PaymentPeriod = apps.get_model('erp', 'PaymentPeriod')

    for period in PaymentPeriod.objects.all():
        period.due_date = period.date
        # 根据状态设置actual_date
        if period.status == 'completed':
            period.actual_date = period.date
        else:
            period.actual_date = None
        period.save()


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0040_paymentperiod_and_more'),
    ]

    operations = [
        # 1. 先添加新字段（允许null）
        migrations.AddField(
            model_name='paymentperiod',
            name='date',
            field=models.DateField(null=True, help_text='收付款日期', verbose_name='日期'),
        ),

        # 2. 迁移数据
        migrations.RunPython(migrate_date_fields, reverse_migrate_date_fields),

        # 3. 将新字段设为非null
        migrations.AlterField(
            model_name='paymentperiod',
            name='date',
            field=models.DateField(help_text='收付款日期', verbose_name='日期'),
        ),

        # 4. 移除旧索引
        migrations.RemoveIndex(
            model_name='paymentperiod',
            name='erp_payment_due_dat_3b1bb8_idx',
        ),
        migrations.RemoveIndex(
            model_name='paymentperiod',
            name='erp_payment_actual__1e678e_idx',
        ),

        # 5. 移除旧字段
        migrations.RemoveField(
            model_name='paymentperiod',
            name='actual_date',
        ),
        migrations.RemoveField(
            model_name='paymentperiod',
            name='due_date',
        ),

        # 6. 添加新索引
        migrations.AddIndex(
            model_name='paymentperiod',
            index=models.Index(fields=['date'], name='erp_payment_date_2850e2_idx'),
        ),
    ]
