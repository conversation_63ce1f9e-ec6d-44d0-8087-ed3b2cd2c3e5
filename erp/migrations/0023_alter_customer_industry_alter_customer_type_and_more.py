# Generated by Django 4.2.7 on 2025-07-13 03:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0022_fix_contract_code_unique_constraint'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customer',
            name='industry',
            field=models.CharField(default='OTHER', help_text='支持多选，逗号分隔，如：FINANCE,GOVT', max_length=100, verbose_name='所属行业'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='type',
            field=models.CharField(default='C', help_text='支持多选，逗号分隔，如：C,G', max_length=50, verbose_name='客户类型'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='industry',
            field=models.CharField(default='OTHER', help_text='支持多选，逗号分隔，如：FINANCE,GOVT', max_length=100, verbose_name='所属行业'),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='type',
            field=models.CharField(default='C', help_text='支持多选，逗号分隔，如：C,G', max_length=50, verbose_name='供应商类型'),
        ),
    ]
