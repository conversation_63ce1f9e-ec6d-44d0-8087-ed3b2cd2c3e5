# Generated by Django 4.2.7 on 2025-07-04 02:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0004_remove_code_unique_constraint'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='invoice_address',
            field=models.TextField(blank=True, null=True, verbose_name='开票地址'),
        ),
        migrations.AddField(
            model_name='customer',
            name='invoice_bank',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='开户银行'),
        ),
        migrations.AddField(
            model_name='customer',
            name='invoice_bank_account',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='银行账号'),
        ),
        migrations.AddField(
            model_name='customer',
            name='invoice_phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='开票电话'),
        ),
        migrations.DeleteModel(
            name='CustomerInvoiceInfo',
        ),
    ]
