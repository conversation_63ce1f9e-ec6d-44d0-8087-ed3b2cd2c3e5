# Generated by Django 4.2.7 on 2025-07-09 15:20

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0012_remove_supplierpaymentinfo_account_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.Char<PERSON>ield(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='合同编号')),
                ('name', models.CharField(max_length=200, verbose_name='合同名称')),
                ('category', models.CharField(choices=[('sales', '销售合同'), ('procurement', '采购合同')], max_length=20, verbose_name='合同类别')),
                ('status', models.CharField(choices=[('pending_signature', '待签署'), ('executing', '执行中'), ('completed', '已完成')], default='pending_signature', max_length=20, verbose_name='合同状态')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='合同金额')),
                ('sign_date', models.DateField(blank=True, null=True, verbose_name='签约日期')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='开始日期')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='结束日期')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='合同备注')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='sales_contracts', to='erp.customer', verbose_name='客户')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='contracts', to='erp.project', verbose_name='关联项目')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='purchase_contracts', to='erp.supplier', verbose_name='供应商')),
            ],
            options={
                'verbose_name': '合同',
                'verbose_name_plural': '合同',
                'db_table': 'erp_contract',
                'ordering': ['-create_datetime'],
                'indexes': [models.Index(fields=['code'], name='erp_contrac_code_a0ed53_idx'), models.Index(fields=['name'], name='erp_contrac_name_804d31_idx'), models.Index(fields=['category'], name='erp_contrac_categor_9f60d1_idx'), models.Index(fields=['status'], name='erp_contrac_status_567300_idx'), models.Index(fields=['create_datetime'], name='erp_contrac_create__750a44_idx')],
            },
        ),
    ]
