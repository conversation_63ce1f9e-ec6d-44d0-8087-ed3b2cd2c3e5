# Generated by Django 4.2.7 on 2025-07-07 02:40

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0009_update_industry_codes_to_english'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='city_code',
            field=models.CharField(blank=True, help_text='市级行政区划代码', max_length=6, null=True, verbose_name='城市代码'),
        ),
        migrations.AddField(
            model_name='customer',
            name='province_code',
            field=models.CharField(blank=True, help_text='省级行政区划代码', max_length=6, null=True, verbose_name='省份代码'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='city',
            field=models.CharField(blank=True, help_text='城市名称，冗余存储', max_length=50, null=True, verbose_name='城市名称'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='province',
            field=models.CharField(blank=True, help_text='省份名称，冗余存储', max_length=50, null=True, verbose_name='省份名称'),
        ),
        migrations.CreateModel(
            name='AdministrativeDivision',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('code', models.CharField(help_text='6位数字代码', max_length=6, unique=True, verbose_name='行政区划代码')),
                ('name', models.CharField(max_length=100, verbose_name='区划名称')),
                ('level', models.CharField(choices=[('province', '省/直辖市/自治区'), ('city', '市/区/县'), ('district', '区/县')], max_length=10, verbose_name='区划级别')),
                ('parent_code', models.CharField(blank=True, max_length=6, null=True, verbose_name='上级区划代码')),
            ],
            options={
                'verbose_name': '行政区划',
                'verbose_name_plural': '行政区划',
                'db_table': 'erp_administrative_division',
                'ordering': ['code'],
                'indexes': [models.Index(fields=['code'], name='erp_adminis_code_f0bce7_idx'), models.Index(fields=['parent_code'], name='erp_adminis_parent__e35f10_idx'), models.Index(fields=['level'], name='erp_adminis_level_0e6c47_idx'), models.Index(fields=['name'], name='erp_adminis_name_5f4465_idx')],
            },
        ),
    ]
