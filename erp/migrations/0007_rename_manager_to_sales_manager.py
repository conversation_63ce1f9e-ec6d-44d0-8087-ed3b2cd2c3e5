# Generated by Django 4.2.7 on 2025-07-04 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('erp', '0006_project'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='project',
            name='erp_project_manager_356623_idx',
        ),
        migrations.RemoveField(
            model_name='project',
            name='manager_id',
        ),
        migrations.RemoveField(
            model_name='project',
            name='manager_name',
        ),
        migrations.AddField(
            model_name='project',
            name='sales_manager_id',
            field=models.CharField(default='SALES001', max_length=50, verbose_name='销售负责人ID'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='project',
            name='sales_manager_name',
            field=models.CharField(default=1, max_length=100, verbose_name='销售负责人姓名'),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['sales_manager_id'], name='erp_project_sales_m_732996_idx'),
        ),
    ]
