# Generated by Django 4.2.7 on 2025-07-03 15:11

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='客户编码')),
                ('name', models.CharField(max_length=200, verbose_name='客户名称')),
                ('tax_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='纳税人识别号')),
                ('type', models.CharField(choices=[('C', '企业客户'), ('G', '政府客户')], default='C', max_length=10, verbose_name='客户类型')),
                ('industry', models.CharField(choices=[('I01', '政府机构'), ('I02', '金融服务'), ('I03', '信息技术/互联网'), ('I04', '制造与工业'), ('I05', '零售与消费品'), ('I06', '能源与公用事业'), ('I07', '交通与物流'), ('I08', '医疗与健康'), ('I09', '教育与科研'), ('I10', '房地产与建筑'), ('I11', '专业服务'), ('I12', '农林牧渔'), ('I13', '其他/未分类')], default='I13', max_length=10, verbose_name='所属行业')),
                ('province', models.CharField(blank=True, max_length=50, null=True, verbose_name='省份')),
                ('city', models.CharField(blank=True, max_length=50, null=True, verbose_name='城市')),
                ('contact', models.CharField(blank=True, max_length=100, null=True, verbose_name='联系人')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='电子邮箱')),
                ('address', models.TextField(blank=True, null=True, verbose_name='联系地址')),
                ('contact_remark', models.TextField(blank=True, null=True, verbose_name='联系备注')),
                ('source', models.CharField(choices=[('website', '官网'), ('referral', '推荐'), ('exhibition', '展会'), ('advertisement', '广告'), ('social', '社交媒体'), ('other', '其他')], default='website', max_length=20, verbose_name='客户来源')),
                ('owner', models.CharField(blank=True, max_length=50, null=True, verbose_name='负责人ID')),
                ('owner_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='负责人姓名')),
                ('last_contact_time', models.DateTimeField(blank=True, null=True, verbose_name='最后联系时间')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '客户',
                'verbose_name_plural': '客户',
                'db_table': 'erp_customer',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.CreateModel(
            name='CustomerInvoiceInfo',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='更新时间', verbose_name='更新时间')),
                ('creator', models.CharField(blank=True, help_text='创建者', max_length=100, null=True, verbose_name='创建者')),
                ('updater', models.CharField(blank=True, help_text='更新者', max_length=100, null=True, verbose_name='更新者')),
                ('delete_datetime', models.DateTimeField(blank=True, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('company_name', models.CharField(max_length=200, verbose_name='开票公司名称')),
                ('tax_id', models.CharField(max_length=50, verbose_name='开票税号')),
                ('bank', models.CharField(blank=True, max_length=100, null=True, verbose_name='开户银行')),
                ('bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='银行账号')),
                ('address', models.TextField(blank=True, null=True, verbose_name='开票地址')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='开票电话')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoice_infos', to='erp.customer', verbose_name='客户')),
            ],
            options={
                'verbose_name': '客户开票信息',
                'verbose_name_plural': '客户开票信息',
                'db_table': 'erp_customer_invoice_info',
                'ordering': ['-create_datetime'],
            },
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['code'], name='erp_custome_code_3e90d9_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['name'], name='erp_custome_name_e241f5_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['type'], name='erp_custome_type_5f0cff_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['create_datetime'], name='erp_custome_create__25d864_idx'),
        ),
    ]
