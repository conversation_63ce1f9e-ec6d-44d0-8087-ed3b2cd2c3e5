import uuid
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from erp.models import Customer, Supplier, Partner, PartnerPaymentInfo, SupplierPaymentInfo


class Command(BaseCommand):
    help = '将现有的客户和供应商数据迁移到Partner模型'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示迁移计划，不实际执行迁移',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制执行迁移，即使已有相同编码的相对方',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']

        self.stdout.write(self.style.SUCCESS('开始数据迁移...'))

        # 统计数据
        customers = Customer.objects.filter(delete_datetime__isnull=True)
        suppliers = Supplier.objects.filter(delete_datetime__isnull=True)
        
        self.stdout.write(f"待迁移客户数量: {customers.count()}")
        self.stdout.write(f"待迁移供应商数量: {suppliers.count()}")

        if dry_run:
            self.stdout.write(self.style.WARNING('这是试运行模式，不会实际执行迁移'))
            self._show_migration_plan(customers, suppliers)
            return

        # 执行迁移（不使用事务包装，让每个操作独立）
        migrated_customers = self._migrate_customers(customers, force)
        migrated_suppliers = self._migrate_suppliers(suppliers, force)

        self.stdout.write(
            self.style.SUCCESS(
                f'迁移完成！成功迁移 {migrated_customers} 个客户，{migrated_suppliers} 个供应商'
            )
        )

    def _show_migration_plan(self, customers, suppliers):
        """显示迁移计划"""
        self.stdout.write(self.style.HTTP_INFO('\n=== 客户迁移计划 ==='))
        for customer in customers:
            existing = Partner.objects.filter(code=customer.code, delete_datetime__isnull=True).first()
            status = "跳过（已存在）" if existing else "迁移"
            self.stdout.write(f"{customer.code} - {customer.name} - {status}")

        self.stdout.write(self.style.HTTP_INFO('\n=== 供应商迁移计划 ==='))
        for supplier in suppliers:
            existing = Partner.objects.filter(code=supplier.code, delete_datetime__isnull=True).first()
            status = "跳过（已存在）" if existing else "迁移"
            self.stdout.write(f"{supplier.code} - {supplier.name} - {status}")

    def _migrate_customers(self, customers, force):
        """迁移客户数据"""
        migrated_count = 0
        
        for customer in customers:
            # 检查是否已存在相同编码的相对方
            existing = Partner.objects.filter(code=customer.code, delete_datetime__isnull=True).first()
            if existing and not force:
                self.stdout.write(
                    self.style.WARNING(f"跳过客户 {customer.code}，相对方已存在")
                )
                continue

            try:
                # 创建相对方记录
                partner_data = {
                    'id': customer.id,  # 保持相同的ID
                    'code': customer.code,
                    'name': customer.name,
                    'tax_id': customer.tax_id,
                    'type': customer.type,  # C 或 G
                    'status': customer.status,
                    'industry': customer.industry,
                    'province_code': customer.province_code,
                    'province': customer.province,
                    'city_code': customer.city_code,
                    'city': customer.city,
                    'contact_person': customer.contact_person,
                    'phone': customer.phone,
                    'email': customer.email,
                    'address': customer.address,
                    'contact_remark': customer.contact_remark,
                    'website': None,  # Customer模型没有website字段
                    'owner_name': customer.owner_name or '未指定',  # 如果为空，提供默认值
                    'owner_id': None,  # Customer模型没有owner_id字段
                    'remark': customer.remark,
                    'invoice_bank': customer.invoice_bank,
                    'invoice_bank_account': customer.invoice_bank_account,
                    'invoice_address': customer.invoice_address,
                    'invoice_phone': customer.invoice_phone,
                    'invoice_remark': None,  # Customer模型没有invoice_remark字段
                    'create_datetime': customer.create_datetime,
                    'update_datetime': customer.update_datetime,
                    'creator': customer.creator,
                    'updater': customer.updater,
                }

                if existing and force:
                    # 更新现有记录
                    for key, value in partner_data.items():
                        if key != 'id':  # 不更新ID
                            setattr(existing, key, value)
                    existing.save()
                    self.stdout.write(f"更新客户 {customer.code} -> 相对方")
                else:
                    # 创建新记录
                    Partner.objects.create(**partner_data)
                    self.stdout.write(f"迁移客户 {customer.code} -> 相对方")

                migrated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"迁移客户 {customer.code} 失败: {str(e)}")
                )

        return migrated_count

    def _migrate_suppliers(self, suppliers, force):
        """迁移供应商数据"""
        migrated_count = 0
        
        for supplier in suppliers:
            # 检查是否已存在相同编码的相对方
            existing = Partner.objects.filter(code=supplier.code, delete_datetime__isnull=True).first()
            if existing and not force:
                self.stdout.write(
                    self.style.WARNING(f"跳过供应商 {supplier.code}，相对方已存在")
                )
                continue

            try:
                # 创建相对方记录
                partner_data = {
                    'id': supplier.id,  # 保持相同的ID
                    'code': supplier.code,
                    'name': supplier.name,
                    'tax_id': supplier.tax_id,
                    'type': supplier.type,  # ORIGINAL 或 CHANNEL
                    'status': supplier.status,
                    'industry': supplier.industry,
                    'province_code': supplier.province_code,
                    'province': supplier.province,
                    'city_code': supplier.city_code,
                    'city': supplier.city,
                    'contact_person': supplier.contact_person,
                    'phone': supplier.phone,
                    'email': supplier.email,
                    'address': supplier.address,
                    'contact_remark': supplier.contact_remark,
                    'website': supplier.website,
                    'owner_name': supplier.owner_name,
                    'owner_id': supplier.owner_id,
                    'remark': supplier.remark,
                    'invoice_bank': supplier.invoice_bank,
                    'invoice_bank_account': supplier.invoice_bank_account,
                    'invoice_address': supplier.invoice_address,
                    'invoice_phone': supplier.invoice_phone,
                    'invoice_remark': supplier.invoice_remark,
                    'create_datetime': supplier.create_datetime,
                    'update_datetime': supplier.update_datetime,
                    'creator': supplier.creator,
                    'updater': supplier.updater,
                }

                if existing and force:
                    # 更新现有记录
                    for key, value in partner_data.items():
                        if key != 'id':  # 不更新ID
                            setattr(existing, key, value)
                    existing.save()
                    partner = existing
                    self.stdout.write(f"更新供应商 {supplier.code} -> 相对方")
                else:
                    # 创建新记录
                    partner = Partner.objects.create(**partner_data)
                    self.stdout.write(f"迁移供应商 {supplier.code} -> 相对方")

                # 迁移供应商收款信息
                self._migrate_supplier_payment_info(supplier, partner)

                migrated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"迁移供应商 {supplier.code} 失败: {str(e)}")
                )

        return migrated_count

    def _migrate_supplier_payment_info(self, supplier, partner):
        """迁移供应商收款信息"""
        payment_infos = SupplierPaymentInfo.objects.filter(
            supplier=supplier,
            delete_datetime__isnull=True
        )

        for payment_info in payment_infos:
            # 检查是否已存在相同的收款信息
            existing = PartnerPaymentInfo.objects.filter(
                partner=partner,
                bank_name=payment_info.bank_name,
                account_number=payment_info.account_number,
                delete_datetime__isnull=True
            ).first()

            if existing:
                self.stdout.write(
                    self.style.WARNING(f"跳过收款信息 {payment_info.bank_name} - {payment_info.account_number}，已存在")
                )
                continue

            try:
                PartnerPaymentInfo.objects.create(
                    id=payment_info.id,  # 保持相同的ID
                    partner=partner,
                    bank_name=payment_info.bank_name,
                    account_number=payment_info.account_number,
                    is_default=payment_info.is_default,
                    remark=payment_info.remark,
                    create_datetime=payment_info.create_datetime,
                    update_datetime=payment_info.update_datetime,
                    creator=payment_info.creator,
                    updater=payment_info.updater,
                )
                self.stdout.write(f"  迁移收款信息: {payment_info.bank_name} - {payment_info.account_number}")

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  迁移收款信息失败: {str(e)}")
                )
