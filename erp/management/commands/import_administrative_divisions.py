from django.core.management.base import BaseCommand
from django.db import transaction
import requests
import time

from erp.models import AdministrativeDivision


class Command(BaseCommand):
    help = '导入全国行政区划数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清理现有数据后导入',
        )
        parser.add_argument(
            '--source',
            type=str,
            default='fallback',
            choices=['github', 'fallback'],
            help='数据源选择',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🌐 开始导入全国行政区划数据...')
        )

        if options['clear']:
            self.stdout.write('清理现有数据...')
            AdministrativeDivision.objects.all().delete()

        if options['source'] == 'github':
            data = self.get_data_from_github()
        else:
            data = self.get_fallback_data()

        if data:
            success = self.import_data(data)
            if success:
                self.stdout.write(
                    self.style.SUCCESS('✅ 行政区划数据导入成功！')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ 行政区划数据导入失败！')
                )
        else:
            self.stdout.write(
                self.style.ERROR('❌ 无法获取数据！')
            )

    def get_data_from_github(self):
        """从GitHub获取数据"""
        self.stdout.write('正在从GitHub获取数据...')
        # 这里可以实现GitHub数据获取逻辑
        return None

    def get_fallback_data(self):
        """获取备用数据"""
        self.stdout.write('使用内置数据源...')

        # 尝试导入完整的城市数据
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'scripts'))
            from complete_cities_data import get_complete_cities_data
            complete_cities = get_complete_cities_data()
            self.stdout.write(f'✓ 加载完整城市数据: {len(complete_cities)} 个')
        except ImportError as e:
            self.stdout.write(f'⚠️  无法加载完整城市数据: {e}')
            complete_cities = []

        return {
            'provinces': [
                {'code': '110000', 'name': '北京市'},
                {'code': '120000', 'name': '天津市'},
                {'code': '130000', 'name': '河北省'},
                {'code': '140000', 'name': '山西省'},
                {'code': '150000', 'name': '内蒙古自治区'},
                {'code': '210000', 'name': '辽宁省'},
                {'code': '220000', 'name': '吉林省'},
                {'code': '230000', 'name': '黑龙江省'},
                {'code': '310000', 'name': '上海市'},
                {'code': '320000', 'name': '江苏省'},
                {'code': '330000', 'name': '浙江省'},
                {'code': '340000', 'name': '安徽省'},
                {'code': '350000', 'name': '福建省'},
                {'code': '360000', 'name': '江西省'},
                {'code': '370000', 'name': '山东省'},
                {'code': '410000', 'name': '河南省'},
                {'code': '420000', 'name': '湖北省'},
                {'code': '430000', 'name': '湖南省'},
                {'code': '440000', 'name': '广东省'},
                {'code': '450000', 'name': '广西壮族自治区'},
                {'code': '460000', 'name': '海南省'},
                {'code': '500000', 'name': '重庆市'},
                {'code': '510000', 'name': '四川省'},
                {'code': '520000', 'name': '贵州省'},
                {'code': '530000', 'name': '云南省'},
                {'code': '540000', 'name': '西藏自治区'},
                {'code': '610000', 'name': '陕西省'},
                {'code': '620000', 'name': '甘肃省'},
                {'code': '630000', 'name': '青海省'},
                {'code': '640000', 'name': '宁夏回族自治区'},
                {'code': '650000', 'name': '新疆维吾尔自治区'},
                {'code': '710000', 'name': '台湾省'},
                {'code': '810000', 'name': '香港特别行政区'},
                {'code': '820000', 'name': '澳门特别行政区'},
            ],
            'cities': complete_cities if complete_cities else [
                # 如果无法加载完整数据，使用基础数据
                {'code': '110101', 'name': '东城区', 'provinceCode': '110000'},
                {'code': '110102', 'name': '西城区', 'provinceCode': '110000'},
                {'code': '310101', 'name': '黄浦区', 'provinceCode': '310000'},
                {'code': '440100', 'name': '广州市', 'provinceCode': '440000'},
                {'code': '440300', 'name': '深圳市', 'provinceCode': '440000'},
            ]
        }

    @transaction.atomic
    def import_data(self, data):
        """导入数据"""
        try:
            # 导入省级数据
            province_count = 0
            for province in data['provinces']:
                AdministrativeDivision.objects.get_or_create(
                    code=province['code'],
                    defaults={
                        'name': province['name'],
                        'level': 'province',
                        'parent_code': None
                    }
                )
                province_count += 1

            self.stdout.write(f'✓ 导入省级数据: {province_count} 条')

            # 导入市级数据
            city_count = 0
            for city in data.get('cities', []):
                parent_code = city.get('provinceCode') or city.get('parent_code')
                if not parent_code and len(city['code']) == 6:
                    parent_code = city['code'][:2] + '0000'

                AdministrativeDivision.objects.get_or_create(
                    code=city['code'],
                    defaults={
                        'name': city['name'],
                        'level': 'city',
                        'parent_code': parent_code
                    }
                )
                city_count += 1

            self.stdout.write(f'✓ 导入市级数据: {city_count} 条')

            # 统计结果
            total = AdministrativeDivision.objects.count()
            self.stdout.write(f'📊 总计: {total} 条记录')

            return True

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'导入失败: {e}')
            )
            return False
