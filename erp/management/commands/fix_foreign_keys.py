from django.core.management.base import BaseCommand
from django.db import connection, transaction
from erp.models import Contract, Project, Partner


class Command(BaseCommand):
    help = '修复外键引用问题'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始修复外键引用问题...'))

        with connection.cursor() as cursor:
            # 查找有问题的记录
            cursor.execute("""
                SELECT id, code, customer_id 
                FROM erp_contract 
                WHERE customer_id IS NOT NULL 
                AND customer_id NOT IN (SELECT id FROM erp_partner WHERE delete_datetime IS NULL)
            """)
            
            invalid_contracts = cursor.fetchall()
            self.stdout.write(f"发现 {len(invalid_contracts)} 个有无效客户引用的合同")
            
            for contract_id, code, customer_id in invalid_contracts:
                self.stdout.write(f"合同: {code}, 无效客户ID: {customer_id}")
                
                # 删除这些无效的合同
                cursor.execute("DELETE FROM erp_contract WHERE id = %s", [contract_id])
                self.stdout.write(f"已删除合同: {code}")

            # 查找有问题的项目记录
            cursor.execute("""
                SELECT id, code, customer_id 
                FROM erp_project 
                WHERE customer_id IS NOT NULL 
                AND customer_id NOT IN (SELECT id FROM erp_partner WHERE delete_datetime IS NULL)
            """)
            
            invalid_projects = cursor.fetchall()
            self.stdout.write(f"发现 {len(invalid_projects)} 个有无效客户引用的项目")
            
            for project_id, code, customer_id in invalid_projects:
                self.stdout.write(f"项目: {code}, 无效客户ID: {customer_id}")

                # 先删除引用此项目的合同
                cursor.execute("DELETE FROM erp_contract WHERE project_id = %s", [project_id])

                # 然后删除这些无效的项目
                cursor.execute("DELETE FROM erp_project WHERE id = %s", [project_id])
                self.stdout.write(f"已删除项目及其相关合同: {code}")

        self.stdout.write(self.style.SUCCESS('外键引用问题修复完成！'))
