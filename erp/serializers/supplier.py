from rest_framework import serializers
from erp.models import Supplier, SupplierPaymentInfo


class SupplierPaymentInfoSerializer(serializers.ModelSerializer):
    """供应商收款信息序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = SupplierPaymentInfo
        fields = [
            'id', 'bank_name', 'account_number',
            'is_default', 'remark',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime']


class SupplierPaymentInfoUpdateSerializer(serializers.ModelSerializer):
    """供应商收款信息更新序列化器（PUT方法专用）"""

    class Meta:
        model = SupplierPaymentInfo
        fields = ['bank_name', 'account_number', 'is_default', 'remark']
        extra_kwargs = {
            'bank_name': {
                'required': True,
                'allow_blank': False,
                'help_text': '开户银行名称'
            },
            'account_number': {
                'required': True,
                'allow_blank': False,
                'help_text': '银行账号'
            },
            'is_default': {
                'required': True,
                'help_text': '是否设为默认账户'
            },
            'remark': {
                'required': False,
                'allow_blank': True,
                'help_text': '备注信息'
            }
        }

    def validate_bank_name(self, value):
        """验证银行名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("银行名称不能为空")
        return value.strip()

    def validate_account_number(self, value):
        """验证银行账号"""
        if not value or not value.strip():
            raise serializers.ValidationError("银行账号不能为空")

        # 简单的银行账号格式验证
        account_number = value.strip()
        if len(account_number) < 10:
            raise serializers.ValidationError("银行账号长度不能少于10位")

        if not account_number.isdigit():
            raise serializers.ValidationError("银行账号只能包含数字")

        return account_number


class SupplierSerializer(serializers.ModelSerializer):
    """供应商序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 嵌套收款信息（只包含未删除的记录）
    payment_infos = serializers.SerializerMethodField()

    class Meta:
        model = Supplier
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'phone', 'email', 'address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'payment_infos',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city']

    def validate(self, attrs):
        """验证并自动填充省市名称"""
        # 如果提供了省份代码，自动填充省份名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            # 如果省份代码为空，清空省份名称
            attrs['province'] = None

        # 如果提供了城市代码，自动填充城市名称
        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            # 如果城市代码为空，清空城市名称
            attrs['city'] = None

        return attrs

    def get_payment_infos(self, obj):
        """获取未删除的收款信息"""
        payment_infos = obj.payment_infos.filter(delete_datetime__isnull=True)
        return SupplierPaymentInfoSerializer(payment_infos, many=True).data

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['display_name'] = instance.display_name

        # 添加类型和行业的显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = instance.get_industry_display()

        # 添加统计信息（预留）
        data['active_projects_count'] = instance.get_active_projects_count()
        data['total_contract_amount'] = instance.get_total_contract_amount()

        return data


class SupplierListSerializer(serializers.ModelSerializer):
    """供应商列表序列化器（简化版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Supplier
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province', 'city', 'contact_person', 'phone', 'email', 'owner_name', 'create_datetime'
        ]
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加类型、状态和行业的显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = instance.get_industry_display()

        return data


class SupplierCreateUpdateSerializer(serializers.ModelSerializer):
    """供应商创建/完全更新序列化器（PUT）"""

    # 可选的编码日期字段，用于历史数据导入
    code_date = serializers.DateField(
        required=False,
        write_only=True,
        help_text='编码生成日期，格式：YYYY-MM-DD。如果不提供，则使用当前日期。主要用于历史数据导入。'
    )

    class Meta:
        model = Supplier
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'phone', 'email', 'address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'code_date'
        ]
        extra_kwargs = {
            'name': {'required': True, 'allow_blank': False, 'help_text': '供应商名称'},
            'type': {'required': True, 'help_text': '供应商类型'},
            'status': {'required': True, 'help_text': '合作状态'},
            'industry': {'required': True, 'help_text': '所属行业'},
            'contact_person': {'required': True, 'allow_blank': False, 'help_text': '联系人'},
            'phone': {'required': True, 'allow_blank': False, 'help_text': '联系电话'},
            'owner_name': {'required': True, 'allow_blank': False, 'help_text': '负责人姓名'},
            'tax_id': {'help_text': '纳税人识别号'},
            'email': {'help_text': '电子邮箱'},
            'address': {'help_text': '联系地址'},
            'website': {'help_text': '官网地址'},
        }

    def validate_name(self, value):
        """验证供应商名称唯一性"""
        if value and value.strip():
            query = Supplier.objects.filter(
                name=value.strip(),
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除自己
            if self.instance:
                query = query.exclude(id=self.instance.id)

            if query.exists():
                raise serializers.ValidationError("供应商名称已存在")
        return value

    def validate_tax_id(self, value):
        """验证税号唯一性"""
        if value and value.strip():
            query = Supplier.objects.filter(
                tax_id=value.strip(),
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除自己
            if self.instance:
                query = query.exclude(id=self.instance.id)

            if query.exists():
                raise serializers.ValidationError("税号已存在")
        return value

    def validate_invoice_bank_account(self, value):
        """验证开票银行账号"""
        from erp.utils.validators import validate_bank_account
        return validate_bank_account(value, allow_null=True, field_name="开票银行账号")

    def validate_phone(self, value):
        """验证联系电话（支持手机号码和座机号码）"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=False, field_name="联系电话")

    def validate(self, attrs):
        """整体验证"""
        # 验证编码不可修改（仅更新时）
        if self.instance and 'code' in attrs and attrs['code'] != self.instance.code:
            raise serializers.ValidationError({"code": "供应商编码不可修改"})

        # 自动填充省市名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs

    def create(self, validated_data):
        """创建供应商，支持自定义编码日期"""
        # 提取编码日期参数
        code_date = validated_data.pop('code_date', None)

        # 创建供应商实例
        supplier = Supplier(**validated_data)

        # 保存时传递编码日期参数
        if code_date:
            supplier.save(code_date=code_date)
        else:
            supplier.save()

        return supplier


class SupplierPartialUpdateSerializer(serializers.ModelSerializer):
    """供应商部分更新序列化器（PATCH）"""

    class Meta:
        model = Supplier
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'phone', 'email', 'address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark'
        ]
        # PATCH时所有字段都是可选的
        extra_kwargs = {
            'name': {'required': False, 'help_text': '供应商名称'},
            'type': {'required': False, 'help_text': '供应商类型'},
            'status': {'required': False, 'help_text': '合作状态'},
            'industry': {'required': False, 'help_text': '所属行业'},
            'contact_person': {'required': False, 'help_text': '联系人'},
            'phone': {'required': False, 'help_text': '联系电话'},
            'owner_name': {'required': False, 'help_text': '负责人姓名'},
            'tax_id': {'required': False, 'help_text': '纳税人识别号'},
            'email': {'required': False, 'help_text': '电子邮箱'},
            'address': {'required': False, 'help_text': '联系地址'},
            'website': {'required': False, 'help_text': '官网地址'},
        }


class SupplierSimpleSerializer(serializers.ModelSerializer):
    """供应商简单序列化器（用于下拉选择等场景）"""

    class Meta:
        model = Supplier
        fields = ['id', 'code', 'name', 'status']

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        data['status_display'] = instance.get_status_display()
        return data

    def validate_code(self, value):
        """供应商编码不可修改"""
        if self.instance and self.instance.code != value:
            raise serializers.ValidationError("供应商编码不可修改")
        return value

    def validate_name(self, value):
        """验证供应商名称唯一性（更新时）"""
        if value and self.instance:
            existing = Supplier.objects.filter(
                name=value.strip(),
                delete_datetime__isnull=True
            ).exclude(id=self.instance.id)
            
            if existing.exists():
                raise serializers.ValidationError("供应商名称已存在")
        return value

    def validate_tax_id(self, value):
        """验证税号唯一性（更新时）"""
        if value and value.strip() and self.instance:
            existing = Supplier.objects.filter(
                tax_id=value.strip(),
                delete_datetime__isnull=True
            ).exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError("税号已存在")
        return value

    def validate_invoice_bank_account(self, value):
        """验证开票银行账号"""
        from erp.utils.validators import validate_bank_account
        return validate_bank_account(value, allow_null=True, field_name="开票银行账号")

    def validate_phone(self, value):
        """验证联系电话（支持手机号码和座机号码）"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=True, field_name="联系电话")

    def validate(self, attrs):
        """整体验证"""
        # 自动填充省市名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs


class SupplierQuerySerializer(serializers.Serializer):
    """供应商查询参数序列化器"""
    search = serializers.CharField(required=False, help_text="模糊搜索（供应商名称、编号、纳税人识别号）")
    type = serializers.CharField(required=False, help_text="供应商类型")
    status = serializers.CharField(required=False, help_text="合作状态")
    industry = serializers.CharField(required=False, help_text="行业类型")
    province = serializers.CharField(required=False, help_text="省份")
    city = serializers.CharField(required=False, help_text="城市")
    date_start = serializers.DateField(required=False, help_text="创建开始日期")
    date_end = serializers.DateField(required=False, help_text="创建结束日期")
    page = serializers.IntegerField(required=False, default=1, help_text="页码")
    page_size = serializers.IntegerField(required=False, default=10, help_text="每页数量")


class SupplierStatusUpdateSerializer(serializers.ModelSerializer):
    """供应商状态更新序列化器"""

    class Meta:
        model = Supplier
        fields = ['status']
        extra_kwargs = {
            'status': {'required': True, 'help_text': '供应商状态，可选值：ACTIVE(活跃)、SUSPENDED(暂停)、BLACKLISTED(黑名单)'}
        }

    def validate_status(self, value):
        """验证状态值"""
        valid_statuses = [choice[0] for choice in Supplier.SUPPLIER_STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(f"无效的状态值。可选值：{', '.join(valid_statuses)}")
        return value

