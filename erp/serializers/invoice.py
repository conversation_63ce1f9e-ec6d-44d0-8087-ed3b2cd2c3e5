from rest_framework import serializers
from erp.models import Invoice, Contract, CustomFieldValue
from decimal import Decimal


class InvoiceOCRUploadSerializer(serializers.Serializer):
    """发票OCR上传序列化器"""

    file = serializers.FileField(
        help_text="发票图片文件（支持JPG、PNG、PDF格式，最大10MB）"
    )
    contract_id = serializers.UUIDField(
        required=False,
        help_text="关联合同ID（可选，用于预填充信息）"
    )


class InvoiceListSerializer(serializers.ModelSerializer):
    """发票列表序列化器"""
    
    # 合同信息
    contract_name = serializers.CharField(source='contract.name', read_only=True)
    contract_code = serializers.CharField(source='contract.code', read_only=True)
    
    # 相对方信息
    contract_partners = serializers.SerializerMethodField()
    
    # 发票类型显示名
    invoice_type_display = serializers.CharField(read_only=True)
    
    # 时间格式化
    invoice_date = serializers.DateField(format='%Y-%m-%d')
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 自定义字段
    custom_fields = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            'id', 'contract_name', 'contract_code', 'contract_partners',
            'invoice_amount', 'invoice_date', 'invoice_type', 'invoice_type_display',
            'payee', 'payee_tax_id', 'payer', 'payer_tax_id',
            'invoice_code', 'invoice_number', 'verification_code',
            'tax_rate', 'tax_amount', 'description',
            'custom_fields', 'create_datetime', 'update_datetime'
        ]

    def get_contract_partners(self, obj):
        """获取合同相对方信息"""
        partners = obj.contract_partners
        return [
            {
                'id': str(partner.id),
                'name': partner.name
            }
            for partner in partners
        ]

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'invoice')


class InvoiceDetailSerializer(serializers.ModelSerializer):
    """发票详情序列化器"""
    
    # 合同信息
    contract_info = serializers.SerializerMethodField()
    
    # 发票类型显示名
    invoice_type_display = serializers.CharField(read_only=True)
    
    # 时间格式化
    invoice_date = serializers.DateField(format='%Y-%m-%d')
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 自定义字段
    custom_fields = serializers.SerializerMethodField()
    
    # 源文件信息
    source_file_info = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = [
            'id', 'contract_info', 'invoice_amount', 'invoice_date',
            'invoice_type', 'invoice_type_display', 'payee', 'payee_tax_id',
            'payer', 'payer_tax_id', 'invoice_code', 'invoice_number',
            'verification_code', 'tax_rate', 'tax_amount', 'description',
            'source_file_info', 'custom_fields',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def get_contract_info(self, obj):
        """获取合同基本信息"""
        return {
            'id': str(obj.contract.id),
            'code': obj.contract.code,
            'name': obj.contract.name,
            'category': obj.contract.category,
            'category_display': obj.contract.category_display,
            'total_amount': str(obj.contract.total_amount),
            'partners': [
                {
                    'id': str(partner.id),
                    'name': partner.name
                }
                for partner in obj.contract_partners
            ]
        }

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'invoice')

    def get_source_file_info(self, obj):
        """获取源文件信息"""
        if obj.source_file:
            return {
                'id': str(obj.source_file.id),
                'filename': obj.source_file.filename,
                'file_size': obj.source_file.file_size,
                'file_type': obj.source_file.file_type,
                'upload_time': obj.source_file.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
            }
        return None


class InvoiceCreateSerializer(serializers.ModelSerializer):
    """发票创建序列化器"""

    # 前端习惯传递contract_id而不是contract
    contract_id = serializers.UUIDField(write_only=True, help_text="合同ID")

    # 时间格式化
    invoice_date = serializers.DateField(format='%Y-%m-%d')

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="自定义字段数据，格式：[{'field_name': '字段名', 'value': '字段值'}]"
    )

    class Meta:
        model = Invoice
        fields = [
            'contract_id', 'invoice_amount', 'invoice_date', 'invoice_type',
            'payee', 'payee_tax_id', 'payer', 'payer_tax_id',
            'invoice_code', 'invoice_number', 'verification_code',
            'tax_rate', 'tax_amount', 'description', 'source_file',
            'custom_fields'
        ]

    def validate_contract_id(self, value):
        """验证合同ID"""
        try:
            contract = Contract.objects.get(id=value, delete_datetime__isnull=True)
            return contract
        except Contract.DoesNotExist:
            raise serializers.ValidationError("合同不存在或已删除")

    def validate_invoice_amount(self, value):
        """验证开票金额"""
        if value <= 0:
            raise serializers.ValidationError("开票金额必须大于0")
        return value

    def validate_tax_rate(self, value):
        """验证税率"""
        if not (0 <= value <= 1):
            raise serializers.ValidationError("税率必须在0-1之间")
        return value

    def validate_tax_amount(self, value):
        """验证税额"""
        if value < 0:
            raise serializers.ValidationError("税额不能为负数")
        return value

    def validate(self, attrs):
        """交叉验证"""
        # 验证发票号码唯一性
        invoice_code = attrs.get('invoice_code')
        invoice_number = attrs.get('invoice_number')
        
        if invoice_code and invoice_number:
            existing = Invoice.objects.filter(
                invoice_code=invoice_code,
                invoice_number=invoice_number,
                delete_datetime__isnull=True
            )
            if existing.exists():
                raise serializers.ValidationError({
                    'invoice_number': f"发票号码 {invoice_number} 在代码 {invoice_code} 下已存在"
                })

        return attrs

    def create(self, validated_data):
        """创建发票"""
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 处理contract_id到contract的转换
        contract = validated_data.pop('contract_id')
        validated_data['contract'] = contract

        # 创建发票
        invoice = Invoice(**validated_data)
        invoice.validate_against_contract()  # 验证业务规则
        invoice.save()

        # 处理自定义字段
        if custom_fields_data:
            CustomFieldValue.set_values_for_object(
                invoice.id, 'invoice', custom_fields_data, invoice.creator
            )

        return invoice


class InvoiceUpdateSerializer(serializers.ModelSerializer):
    """发票更新序列化器"""

    # 前端习惯传递contract_id而不是contract
    contract_id = serializers.UUIDField(write_only=True, required=False, help_text="合同ID")

    # 时间格式化
    invoice_date = serializers.DateField(format='%Y-%m-%d', required=False)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        allow_empty=True,
        help_text="自定义字段数据，格式：[{'field_name': '字段名', 'value': '字段值'}]"
    )

    class Meta:
        model = Invoice
        fields = [
            'contract_id', 'invoice_amount', 'invoice_date', 'invoice_type',
            'payee', 'payee_tax_id', 'payer', 'payer_tax_id',
            'invoice_code', 'invoice_number', 'verification_code',
            'tax_rate', 'tax_amount', 'description', 'source_file',
            'custom_fields'
        ]
        extra_kwargs = {
            'invoice_amount': {'required': False},
            'invoice_type': {'required': False},
            'payee': {'required': False},
            'payee_tax_id': {'required': False},
            'payer': {'required': False},
            'payer_tax_id': {'required': False},
            'invoice_code': {'required': False},
            'invoice_number': {'required': False},
            'tax_rate': {'required': False},
            'tax_amount': {'required': False},
        }

    def validate_invoice_amount(self, value):
        """验证开票金额"""
        if value is not None and value <= 0:
            raise serializers.ValidationError("开票金额必须大于0")
        return value

    def validate_tax_rate(self, value):
        """验证税率"""
        if value is not None and not (0 <= value <= 1):
            raise serializers.ValidationError("税率必须在0-1之间")
        return value

    def validate_tax_amount(self, value):
        """验证税额"""
        if value is not None and value < 0:
            raise serializers.ValidationError("税额不能为负数")
        return value

    def validate(self, attrs):
        """交叉验证"""
        # 验证发票号码唯一性（排除当前记录）
        invoice_code = attrs.get('invoice_code')
        invoice_number = attrs.get('invoice_number')
        
        if invoice_code and invoice_number:
            existing = Invoice.objects.filter(
                invoice_code=invoice_code,
                invoice_number=invoice_number,
                delete_datetime__isnull=True
            ).exclude(id=self.instance.id)
            
            if existing.exists():
                raise serializers.ValidationError({
                    'invoice_number': f"发票号码 {invoice_number} 在代码 {invoice_code} 下已存在"
                })

        return attrs

    def validate_contract_id(self, value):
        """验证合同ID"""
        if value is not None:
            try:
                contract = Contract.objects.get(id=value, delete_datetime__isnull=True)
                return contract
            except Contract.DoesNotExist:
                raise serializers.ValidationError("合同不存在或已删除")
        return value

    def update(self, instance, validated_data):
        """更新发票"""
        custom_fields_data = validated_data.pop('custom_fields', None)

        # 处理contract_id到contract的转换
        if 'contract_id' in validated_data:
            contract = validated_data.pop('contract_id')
            if contract is not None:
                validated_data['contract'] = contract

        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # 验证业务规则
        instance.validate_against_contract()
        instance.save()

        # 处理自定义字段
        if custom_fields_data is not None:
            CustomFieldValue.set_values_for_object(
                instance.id, 'invoice', custom_fields_data, instance.updater
            )

        return instance
