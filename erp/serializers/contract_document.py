from rest_framework import serializers
from django.db.models import Q
from erp.models import Contract, Attachment
from collections import defaultdict


class ContractDocumentListSerializer(serializers.ModelSerializer):
    """合同文档管理列表序列化器"""
    
    # 合同基本信息
    category_display = serializers.CharField(read_only=True)
    sign_status_display = serializers.CharField(read_only=True)
    performance_status_display = serializers.CharField(read_only=True)
    
    # 项目信息
    project_info = serializers.SerializerMethodField()
    
    # 相对方信息
    partners = serializers.SerializerMethodField()
    
    # 文档统计信息
    document_count = serializers.SerializerMethodField()
    document_categories = serializers.SerializerMethodField()
    latest_document = serializers.SerializerMethodField()
    
    # 时间格式化
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 创建者信息
    creator_name = serializers.SerializerMethodField()

    class Meta:
        model = Contract
        fields = [
            'id', 'name', 'code', 'category', 'category_display',
            'sign_status', 'sign_status_display', 'performance_status', 'performance_status_display',
            'total_amount', 'project_info', 'partners',
            'document_count', 'document_categories', 'latest_document',
            'create_datetime', 'update_datetime', 'creator', 'creator_name'
        ]

    def get_document_count(self, obj):
        """获取文档数量"""
        from django.contrib.contenttypes.models import ContentType

        # 获取Contract的ContentType
        contract_content_type = ContentType.objects.get_for_model(Contract)

        return Attachment.objects.filter(
            content_type=contract_content_type,
            object_id=obj.id,
            delete_datetime__isnull=True
        ).count()

    def get_project_info(self, obj):
        """获取项目信息"""
        if obj.project:
            return {
                'id': str(obj.project.id),
                'name': obj.project.name,
                'code': obj.project.code
            }
        return None

    def get_partners(self, obj):
        """获取合同相对方信息"""
        partners = obj.partners.filter(delete_datetime__isnull=True)
        return [
            {
                'id': str(partner.id),
                'name': partner.name
            }
            for partner in partners
        ]

    def get_document_categories(self, obj):
        """获取按类别统计的文档数量"""
        from django.contrib.contenttypes.models import ContentType

        # 获取Contract的ContentType
        contract_content_type = ContentType.objects.get_for_model(Contract)

        # 获取该合同的所有附件
        attachments = Attachment.objects.filter(
            content_type=contract_content_type,
            object_id=obj.id,
            delete_datetime__isnull=True
        )

        # 按类别统计
        category_counts = defaultdict(int)
        for attachment in attachments:
            if attachment.category:
                category_counts[attachment.category] += 1

        return dict(category_counts)

    def get_latest_document(self, obj):
        """获取最新上传的文档信息"""
        from django.contrib.contenttypes.models import ContentType

        # 获取Contract的ContentType
        contract_content_type = ContentType.objects.get_for_model(Contract)

        latest_attachment = Attachment.objects.filter(
            content_type=contract_content_type,
            object_id=obj.id,
            delete_datetime__isnull=True
        ).order_by('-create_datetime').first()

        if latest_attachment:
            return {
                'id': str(latest_attachment.id),
                'original_name': latest_attachment.original_name,
                'category_display': latest_attachment.category_display,
                'create_datetime': latest_attachment.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
            }
        return None

    def get_creator_name(self, obj):
        """获取创建者名称"""
        # 这里可以根据实际的用户系统来获取用户名称
        # 暂时返回creator字段的值
        return obj.creator or '未知'
