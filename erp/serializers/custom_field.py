import json
from rest_framework import serializers
from erp.models import CustomField, CustomFieldValue


class CustomFieldSerializer(serializers.ModelSerializer):
    """自定义字段序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 显示名称
    field_type_display = serializers.CharField(source='get_field_type_display', read_only=True)
    target_model_display = serializers.CharField(source='get_target_model_display', read_only=True)

    # 选项列表（直接返回字符串数组）
    options_list = serializers.SerializerMethodField()
    field_options = serializers.SerializerMethodField()

    class Meta:
        model = CustomField
        fields = [
            'id', 'field_name', 'field_type', 'target_model', 'field_options', 'is_active',
            'field_type_display', 'target_model_display', 'options_list',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

    def get_options_list(self, obj):
        """获取选项列表（直接返回字符串数组）"""
        return obj.get_options_list()

    def get_field_options(self, obj):
        """获取字段选项（返回数组格式而不是字符串）"""
        return obj.get_options_list()

    def validate_field_name(self, value):
        """验证字段名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("字段名称不能为空")
        return value.strip()

    def validate_field_options(self, value):
        """验证选项值"""
        if not value:
            return value

        try:
            options = json.loads(value)
            if not isinstance(options, list):
                raise serializers.ValidationError("选项值必须是数组格式")

            for option in options:
                if not isinstance(option, str) or not option.strip():
                    raise serializers.ValidationError("选项值必须是非空字符串数组")

            return value
        except json.JSONDecodeError:
            raise serializers.ValidationError("选项值必须是有效的JSON格式")

    def validate(self, attrs):
        """交叉验证"""
        field_type = attrs.get('field_type')
        field_options = attrs.get('field_options')

        # 单选和多选类型必须有选项值
        if field_type in ['select', 'multiselect']:
            if not field_options:
                raise serializers.ValidationError({"field_options": "单选和多选类型必须设置选项值"})
        else:
            # 其他类型不应该有选项值
            if field_options:
                attrs['field_options'] = None

        return attrs


class CustomFieldCreateSerializer(serializers.ModelSerializer):
    """自定义字段创建序列化器"""

    # 支持多种格式的选项输入
    options = serializers.JSONField(
        required=False,
        write_only=True,
        help_text="选项值，支持格式：1. 字符串数组 ['选项1', '选项2'] 2. 逗号分隔字符串 '选项1,选项2'"
    )

    class Meta:
        model = CustomField
        fields = ['field_name', 'field_type', 'target_model', 'options']

    def validate_field_name(self, value):
        """验证字段名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("字段名称不能为空")
        return value.strip()

    def validate_options(self, value):
        """验证选项值，支持多种格式"""
        if not value:
            return value

        # 如果是字符串，尝试按逗号分割
        if isinstance(value, str):
            labels = [label.strip() for label in value.split(',') if label.strip()]
            if not labels:
                raise serializers.ValidationError("选项值不能为空")
            return labels

        # 如果是列表，验证每个元素都是字符串
        elif isinstance(value, list):
            if not value:
                raise serializers.ValidationError("选项值不能为空")

            for item in value:
                if not isinstance(item, str) or not item.strip():
                    raise serializers.ValidationError("选项值必须是非空字符串")

            return [item.strip() for item in value]

        else:
            raise serializers.ValidationError("选项值必须是字符串数组或逗号分隔的字符串")

    def validate(self, attrs):
        """交叉验证"""
        field_type = attrs.get('field_type')
        options = attrs.get('options')
        target_model = attrs.get('target_model')
        field_name = attrs.get('field_name')

        # 单选和多选类型必须有选项值
        if field_type in ['select', 'multiselect']:
            if not options:
                raise serializers.ValidationError({"options": "单选和多选类型必须设置选项值"})
        else:
            # 其他类型不应该有选项值
            if options:
                attrs['options'] = None

        # 验证字段名称在同一模块下的唯一性
        if CustomField.objects.filter(
            field_name=field_name,
            target_model=target_model,
            delete_datetime__isnull=True
        ).exists():
            raise serializers.ValidationError({"field_name": f"field_name '{field_name}' 在 {target_model} 模块中已存在"})

        return attrs

    def create(self, validated_data):
        """创建自定义字段，直接使用label作为选项值"""
        options = validated_data.pop('options', None)

        # 如果有选项值，直接存储label列表
        if options and validated_data.get('field_type') in ['select', 'multiselect']:
            # 直接将label列表转换为JSON字符串存储
            validated_data['field_options'] = json.dumps(options, ensure_ascii=False)

        return super().create(validated_data)


class CustomFieldUpdateSerializer(serializers.ModelSerializer):
    """自定义字段更新序列化器（仅允许修改名称）"""

    class Meta:
        model = CustomField
        fields = ['field_name']

    def validate_field_name(self, value):
        """验证字段名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("字段名称不能为空")
        return value.strip()

    def validate(self, attrs):
        """交叉验证"""
        field_name = attrs.get('field_name')
        instance = self.instance

        # 验证字段名称在同一模块下的唯一性（排除自己）
        if CustomField.objects.filter(
            field_name=field_name,
            target_model=instance.target_model,
            delete_datetime__isnull=True
        ).exclude(id=instance.id).exists():
            raise serializers.ValidationError({"field_name": f"field_name '{field_name}' 在 {instance.target_model} 模块中已存在"})

        return attrs


class CustomFieldValueSerializer(serializers.ModelSerializer):
    """自定义字段值序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 字段信息
    field_name = serializers.CharField(source='custom_field.field_name', read_only=True)
    field_type = serializers.CharField(source='custom_field.field_type', read_only=True)

    # 值
    value = serializers.SerializerMethodField()

    class Meta:
        model = CustomFieldValue
        fields = [
            'id', 'custom_field', 'object_id', 'value',
            'field_name', 'field_type',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

    def get_value(self, obj):
        """获取字段值"""
        return obj.get_value()


class CustomFieldSimpleSerializer(serializers.ModelSerializer):
    """自定义字段简单序列化器（用于下拉选择等场景）"""

    field_type_display = serializers.CharField(source='get_field_type_display', read_only=True)
    options_list = serializers.SerializerMethodField()

    class Meta:
        model = CustomField
        fields = ['id', 'field_name', 'field_type', 'field_type_display', 'options_list', 'is_active']

    def get_options_list(self, obj):
        """获取选项列表（直接返回字符串数组）"""
        return obj.get_options_list()


class CustomFieldBatchValueSerializer(serializers.Serializer):
    """自定义字段批量值设置序列化器"""

    custom_fields = serializers.DictField(
        child=serializers.JSONField(),
        required=False,
        help_text="自定义字段值，格式：{field_id: value}"
    )


class CustomFieldOptionsSerializer(serializers.Serializer):
    """自定义字段选项管理序列化器"""

    OPERATION_CHOICES = [
        ('add', '添加选项'),
        ('remove', '删除选项'),
        ('update', '更新选项'),
    ]

    operation = serializers.ChoiceField(
        choices=OPERATION_CHOICES,
        help_text="操作类型：add(添加)、remove(删除)、update(更新)"
    )

    options = serializers.ListField(
        child=serializers.CharField(max_length=200),
        required=False,
        help_text="选项列表，字符串数组格式"
    )

    old_option = serializers.CharField(
        max_length=200,
        required=False,
        help_text="要更新或删除的原选项值（update和remove操作时必填）"
    )

    new_option = serializers.CharField(
        max_length=200,
        required=False,
        help_text="新选项值（update操作时必填）"
    )

    def validate(self, attrs):
        """交叉验证"""
        operation = attrs.get('operation')
        options = attrs.get('options')
        old_option = attrs.get('old_option')
        new_option = attrs.get('new_option')

        if operation == 'add':
            if not options:
                raise serializers.ValidationError("添加操作需要提供options参数")
        elif operation == 'remove':
            if not old_option:
                raise serializers.ValidationError("删除操作需要提供old_option参数")
        elif operation == 'update':
            if not old_option or not new_option:
                raise serializers.ValidationError("更新操作需要同时提供old_option和new_option参数")

        return attrs

    def validate_custom_fields(self, value):
        """验证自定义字段值"""
        if not value:
            return value

        # 获取目标模块（需要在视图中设置）
        target_model = self.context.get('target_model')
        if not target_model:
            raise serializers.ValidationError("缺少目标模块信息")

        # 验证每个字段值
        for field_id, field_value in value.items():
            try:
                # 验证字段ID格式
                import uuid
                uuid.UUID(field_id)

                # 获取字段定义
                custom_field = CustomField.objects.get(
                    id=field_id,
                    target_model=target_model,
                    is_active=True,
                    delete_datetime__isnull=True
                )

                # 验证字段值
                if field_value is not None and not custom_field.validate_value(field_value):
                    raise serializers.ValidationError(f"字段 '{custom_field.field_name}' 的值格式不正确")

            except ValueError:
                raise serializers.ValidationError(f"无效的字段ID: {field_id}")
            except CustomField.DoesNotExist:
                raise serializers.ValidationError(f"字段不存在或已禁用: {field_id}")

        return value
