from rest_framework import serializers
from erp.models import Partner, PartnerPaymentInfo, CustomFieldValue
from erp.serializers.custom_field import CustomFieldBatchValueSerializer


class PartnerPaymentInfoSerializer(serializers.ModelSerializer):
    """相对方收款信息序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = PartnerPaymentInfo
        fields = [
            'id', 'bank_name', 'account_number', 'is_default', 'remark',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'create_datetime', 'update_datetime']

    def validate_bank_name(self, value):
        """验证开户银行"""
        if not value or not value.strip():
            raise serializers.ValidationError("开户银行不能为空")
        return value.strip()

    def validate_account_number(self, value):
        """验证银行账号"""
        if not value or not value.strip():
            raise serializers.ValidationError("银行账号不能为空")

        # 简单的银行账号格式验证
        account_number = value.strip()
        if len(account_number) < 10:
            raise serializers.ValidationError("银行账号长度不能少于10位")

        if not account_number.isdigit():
            raise serializers.ValidationError("银行账号只能包含数字")

        return account_number


class PartnerSerializer(serializers.ModelSerializer):
    """相对方序列化器（基础版本）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 嵌套收款信息（只包含未删除的记录，仅供应商类型）
    payment_infos = serializers.SerializerMethodField()

    # 自定义字段
    custom_fields = serializers.SerializerMethodField()

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'partner_type', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'payment_infos', 'custom_fields',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city']

    def get_payment_infos(self, obj):
        """获取收款信息（仅供应商类型）"""
        if obj.is_supplier:
            payment_infos = obj.payment_infos.filter(delete_datetime__isnull=True)
            return PartnerPaymentInfoSerializer(payment_infos, many=True).data
        return []

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'partner')

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['display_name'] = instance.display_name

        # 添加类型、状态和行业的显示名称
        data['partner_type_display'] = instance.get_partner_type_display()
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        # 根据相对方类型添加统计信息
        if instance.is_customer:
            data['project_count'] = instance.get_active_projects_count()
            data['sales_contract_count'] = instance.get_sales_contract_count()
            data['sales_contract_total_amount'] = instance.get_sales_contract_total_amount()
        elif instance.is_supplier:
            data['purchase_contract_count'] = instance.get_purchase_contract_count()
            data['purchase_contract_total_amount'] = instance.get_purchase_contract_total_amount()

        # 将自定义字段展开到顶级
        custom_fields = data.get('custom_fields', {})
        # 移除嵌套的custom_fields对象（无论是否有值）
        data.pop('custom_fields', None)
        # 将每个自定义字段作为顶级字段添加
        if custom_fields:
            for field_name, field_info in custom_fields.items():
                data[field_name] = field_info.get('value')

        return data


class PartnerListSerializer(serializers.ModelSerializer):
    """相对方列表序列化器（简化版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 自定义字段
    payment_infos = serializers.SerializerMethodField()
    custom_fields = serializers.SerializerMethodField()

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'partner_type', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'payment_infos', 'custom_fields',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def get_payment_infos(self, obj):
        """获取收款信息（仅供应商类型）"""
        if obj.is_supplier:
            payment_infos = obj.payment_infos.filter(delete_datetime__isnull=True)
            return PartnerPaymentInfoSerializer(payment_infos, many=True).data
        return []

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'partner')

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加类型、状态和行业的显示名称
        data['partner_type_display'] = instance.get_partner_type_display()
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        # 将自定义字段展开到顶级
        custom_fields = data.get('custom_fields', {})
        # 移除嵌套的custom_fields对象（无论是否有值）
        data.pop('custom_fields', None)
        # 将每个自定义字段作为顶级字段添加
        if custom_fields:
            for field_name, field_info in custom_fields.items():
                data[field_name] = field_info.get('value')

        return data


class PartnerCreateSerializer(serializers.ModelSerializer):
    """相对方创建序列化器（POST）"""

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Partner
        fields = [
            'name', 'tax_id', 'partner_type', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'custom_fields'
        ]
        extra_kwargs = {
            # 必填字段
            'name': {'required': True},
            'partner_type': {'required': True},
            'type': {'required': True},
            # 非必填字段
            'industry': {'required': False},
            'contact_person': {'required': False},
            'contact_phone': {'required': False},
            'owner_name': {'required': False},
        }

    def validate_name(self, value):
        """验证相对方名称（必填）"""
        if not value or not value.strip():
            raise serializers.ValidationError("相对方名称不能为空")
        return value.strip()

    def validate_partner_type(self, value):
        """验证合作伙伴类型（必填）"""
        if not value:
            raise serializers.ValidationError("合作伙伴类型不能为空")
        return value

    def validate_type(self, value):
        """验证相对方类型（必填）"""
        if not value:
            raise serializers.ValidationError("相对方类型不能为空")
        return value

    def validate_contact_person(self, value):
        """验证联系人（非必填）"""
        if value:
            return value.strip()
        return value

    def validate_contact_phone(self, value):
        """验证联系电话（非必填）"""
        if value:
            return value.strip()
        return value

    def validate_owner_name(self, value):
        """验证负责人姓名（非必填）"""
        if value:
            return value.strip()
        return value

    def validate_industry(self, value):
        """验证行业选择（非必填）"""
        return value

    def validate(self, attrs):
        """验证partner_type和type的组合"""
        partner_type = attrs.get('partner_type')
        type_value = attrs.get('type')

        if partner_type and type_value:
            # 客户类型验证
            if partner_type == 'customer' and type_value not in ['C', 'G']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为客户时，相对方类型只能选择企业客户(C)或政府客户(G)'
                })

            # 供应商类型验证
            if partner_type == 'supplier' and type_value not in ['ORIGINAL', 'CHANNEL']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为供应商时，相对方类型只能选择原厂(ORIGINAL)或渠道(CHANNEL)'
                })

        return super().validate(attrs)

    def create(self, validated_data):
        """创建相对方，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 创建相对方实例
        partner = Partner(**validated_data)

        # 保存相对方（使用当前日期生成编码）
        partner.save()

        # 保存自定义字段值
        if custom_fields_data:
            creator = validated_data.get('creator', None)
            CustomFieldValue.set_values_for_object(
                partner.id, 'partner', custom_fields_data, creator
            )

        return partner


class PartnerUpdateSerializer(serializers.ModelSerializer):
    """相对方更新序列化器（PUT）"""

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Partner
        fields = [
            'name', 'tax_id', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'custom_fields'
        ]

    def validate_name(self, value):
        """验证相对方名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("相对方名称不能为空")
        return value.strip()

    def validate_contact_person(self, value):
        """验证联系人"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系人不能为空")
        return value.strip()

    def validate_contact_phone(self, value):
        """验证联系电话"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系电话不能为空")
        return value.strip()

    def validate_owner_name(self, value):
        """验证负责人姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError("负责人姓名不能为空")
        return value.strip()

    def validate_industry(self, value):
        """验证行业选择"""
        if not value:
            raise serializers.ValidationError("所属行业不能为空")
        return value

    def update(self, instance, validated_data):
        """更新相对方，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新自定义字段值
        if custom_fields_data:
            updater = getattr(instance, 'updater', None)
            CustomFieldValue.set_values_for_object(
                instance.id, 'partner', custom_fields_data, updater
            )

        return instance

    def validate_name(self, value):
        """验证相对方名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("相对方名称不能为空")
        return value.strip()

    def validate_contact_person(self, value):
        """验证联系人"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系人不能为空")
        return value.strip()

    def validate_contact_phone(self, value):
        """验证联系电话"""
        if not value or not value.strip():
            raise serializers.ValidationError("联系电话不能为空")
        return value.strip()

    def validate_owner_name(self, value):
        """验证负责人姓名"""
        if not value or not value.strip():
            raise serializers.ValidationError("负责人姓名不能为空")
        return value.strip()

    def validate_industry(self, value):
        """验证行业选择"""
        if not value:
            raise serializers.ValidationError("所属行业不能为空")
        return value

    def validate(self, attrs):
        """验证partner_type和type的组合"""
        partner_type = attrs.get('partner_type')
        type_value = attrs.get('type')

        if partner_type and type_value:
            # 客户类型验证
            if partner_type == 'customer' and type_value not in ['C', 'G']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为客户时，相对方类型只能选择企业客户(C)或政府客户(G)'
                })

            # 供应商类型验证
            if partner_type == 'supplier' and type_value not in ['ORIGINAL', 'CHANNEL']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为供应商时，相对方类型只能选择原厂(ORIGINAL)或渠道(CHANNEL)'
                })

        return super().validate(attrs)

    def validate_custom_fields(self, value):
        """验证自定义字段值"""
        if not value:
            return value

        # 支持新的列表格式：[{"field_name": "字段名称", "value": "字段值"}]
        if isinstance(value, list):
            from erp.models import CustomField
            for field_data in value:
                if not isinstance(field_data, dict):
                    raise serializers.ValidationError("自定义字段数据格式错误，应为字典格式")

                field_name = field_data.get('field_name')
                field_value = field_data.get('value')

                if not field_name:
                    raise serializers.ValidationError("自定义字段必须包含 field_name")

                try:
                    # 获取字段定义
                    custom_field = CustomField.objects.get(
                        field_name=field_name,
                        target_model='partner',
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    # 验证字段值
                    if field_value is not None and not custom_field.validate_value(field_value):
                        raise serializers.ValidationError(f"字段 '{field_name}' 的值格式不正确")

                except CustomField.DoesNotExist:
                    raise serializers.ValidationError(f"字段不存在或已禁用: {field_name}")

        # 兼容旧的字典格式：{field_id: value}
        elif isinstance(value, dict):
            for field_id, field_value in value.items():
                try:
                    # 验证字段ID格式
                    import uuid
                    uuid.UUID(field_id)

                    # 获取字段定义
                    from erp.models import CustomField
                    custom_field = CustomField.objects.get(
                        id=field_id,
                        target_model='partner',
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    # 验证字段值
                    if field_value is not None and not custom_field.validate_value(field_value):
                        raise serializers.ValidationError(f"字段 '{custom_field.field_name}' 的值格式不正确")

                except ValueError:
                    raise serializers.ValidationError(f"无效的字段ID: {field_id}")
                except CustomField.DoesNotExist:
                    raise serializers.ValidationError(f"字段不存在或已禁用: {field_id}")
        else:
            raise serializers.ValidationError("自定义字段数据格式错误，应为列表或字典格式")

        return value

    def validate(self, attrs):
        """交叉验证"""
        # 验证省份和城市代码
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs

    def create(self, validated_data):
        """创建相对方，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 创建相对方实例
        partner = Partner(**validated_data)

        # 保存相对方（使用当前日期生成编码）
        partner.save()

        # 保存自定义字段值
        if custom_fields_data:
            creator = validated_data.get('creator', None)
            CustomFieldValue.set_values_for_object(
                partner.id, 'partner', custom_fields_data, creator
            )

        return partner


class PartnerPartialUpdateSerializer(serializers.ModelSerializer):
    """相对方部分更新序列化器（PATCH）"""

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Partner
        fields = [
            'name', 'tax_id', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'custom_fields'
        ]

    def validate_custom_fields(self, value):
        """验证自定义字段值"""
        if not value:
            return value

        # 支持新的列表格式：[{"field_name": "字段名称", "value": "字段值"}]
        if isinstance(value, list):
            from erp.models import CustomField
            for field_data in value:
                if not isinstance(field_data, dict):
                    raise serializers.ValidationError("自定义字段数据格式错误，应为字典格式")

                field_name = field_data.get('field_name')
                field_value = field_data.get('value')

                if not field_name:
                    raise serializers.ValidationError("自定义字段必须包含 field_name")

                try:
                    # 获取字段定义
                    custom_field = CustomField.objects.get(
                        field_name=field_name,
                        target_model='partner',
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    # 验证字段值
                    if field_value is not None and not custom_field.validate_value(field_value):
                        raise serializers.ValidationError(f"字段 '{field_name}' 的值格式不正确")

                except CustomField.DoesNotExist:
                    raise serializers.ValidationError(f"字段不存在或已禁用: {field_name}")

        # 兼容旧的字典格式：{field_id: value}
        elif isinstance(value, dict):
            for field_id, field_value in value.items():
                try:
                    # 验证字段ID格式
                    import uuid
                    uuid.UUID(field_id)

                    # 获取字段定义
                    from erp.models import CustomField
                    custom_field = CustomField.objects.get(
                        id=field_id,
                        target_model='partner',
                        is_active=True,
                        delete_datetime__isnull=True
                    )

                    # 验证字段值
                    if field_value is not None and not custom_field.validate_value(field_value):
                        raise serializers.ValidationError(f"字段 '{custom_field.field_name}' 的值格式不正确")

                except ValueError:
                    raise serializers.ValidationError(f"无效的字段ID: {field_id}")
                except CustomField.DoesNotExist:
                    raise serializers.ValidationError(f"字段不存在或已禁用: {field_id}")
        else:
            raise serializers.ValidationError("自定义字段数据格式错误，应为列表或字典格式")

        return value

    def validate(self, attrs):
        """交叉验证"""
        # 验证partner_type和type的组合
        partner_type = attrs.get('partner_type')
        type_value = attrs.get('type')

        # 如果只更新了其中一个字段，需要获取实例的另一个字段值
        if self.instance:
            if partner_type is None:
                partner_type = self.instance.partner_type
            if type_value is None:
                type_value = self.instance.type

        if partner_type and type_value:
            # 客户类型验证
            if partner_type == 'customer' and type_value not in ['C', 'G']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为客户时，相对方类型只能选择企业客户(C)或政府客户(G)'
                })

            # 供应商类型验证
            if partner_type == 'supplier' and type_value not in ['ORIGINAL', 'CHANNEL']:
                raise serializers.ValidationError({
                    'type': '当合作伙伴类型为供应商时，相对方类型只能选择原厂(ORIGINAL)或渠道(CHANNEL)'
                })

        # 验证省份和城市代码
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs

    def update(self, instance, validated_data):
        """更新相对方，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新自定义字段值
        if custom_fields_data:
            updater = getattr(instance, 'updater', None)
            CustomFieldValue.set_values_for_object(
                instance.id, 'partner', custom_fields_data, updater
            )

        return instance


class PartnerStatusUpdateSerializer(serializers.ModelSerializer):
    """相对方状态更新序列化器"""

    class Meta:
        model = Partner
        fields = ['status']

    def validate_status(self, value):
        """验证状态值"""
        valid_statuses = [choice[0] for choice in Partner.PARTNER_STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(f"无效的状态值，有效值为: {', '.join(valid_statuses)}")
        return value


class PartnerSimpleSerializer(serializers.ModelSerializer):
    """相对方简单序列化器（用于下拉选择等场景）"""

    class Meta:
        model = Partner
        fields = ['id', 'code', 'name', 'partner_type', 'type', 'status']

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        data['partner_type_display'] = instance.get_partner_type_display()
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        return data


class PartnerPaymentInfoUpdateSerializer(serializers.ModelSerializer):
    """相对方收款信息更新序列化器"""

    class Meta:
        model = PartnerPaymentInfo
        fields = ['bank_name', 'account_number', 'is_default', 'remark']

    def validate_bank_name(self, value):
        """验证开户银行"""
        if not value or not value.strip():
            raise serializers.ValidationError("开户银行不能为空")
        return value.strip()

    def validate_account_number(self, value):
        """验证银行账号"""
        if not value or not value.strip():
            raise serializers.ValidationError("银行账号不能为空")

        # 简单的银行账号格式验证
        account_number = value.strip()
        if len(account_number) < 10:
            raise serializers.ValidationError("银行账号长度不能少于10位")

        if not account_number.isdigit():
            raise serializers.ValidationError("银行账号只能包含数字")

        return account_number
