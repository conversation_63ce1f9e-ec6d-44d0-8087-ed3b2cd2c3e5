from rest_framework import serializers
from django.contrib.contenttypes.models import ContentType
from erp.models import Attachment


class AttachmentSerializer(serializers.ModelSerializer):
    """附件序列化器"""
    
    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    
    # 只读字段
    file_size_formatted = serializers.ReadOnlyField()
    is_image = serializers.ReadOnlyField()
    is_document = serializers.ReadOnlyField()
    is_archive = serializers.ReadOnlyField()
    file_icon = serializers.SerializerMethodField()
    related_model_name = serializers.ReadOnlyField()
    related_object_display = serializers.ReadOnlyField()
    download_url = serializers.SerializerMethodField()

    class Meta:
        model = Attachment
        fields = [
            'id', 'original_name', 'file_name', 'file_path', 'file_size', 'file_size_formatted',
            'file_type', 'file_extension', 'file_md5', 'description', 'category', 'category_display',
            'content_type', 'object_id', 'related_model_name', 'related_object_display',
            'is_image', 'is_document', 'is_archive', 'file_icon', 'download_url',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = [
            'id', 'file_name', 'file_path', 'file_size', 'file_type', 'file_extension', 'file_md5',
            'category_display', 'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def get_file_icon(self, obj):
        """获取文件图标"""
        return obj.get_file_icon()

    def get_download_url(self, obj):
        """获取下载URL（使用外部访问地址）"""
        from erp.utils.storage import attachment_upload_manager
        storage = attachment_upload_manager.storage

        # 优先使用外部URL，如果存储后端支持的话
        if hasattr(storage, 'external_url'):
            return storage.external_url(obj.file_path)
        else:
            # 回退到普通URL
            return storage.url(obj.file_path)


class AttachmentUploadSerializer(serializers.Serializer):
    """附件上传序列化器"""

    file = serializers.FileField(help_text="上传的文件")
    content_type = serializers.CharField(max_length=50, help_text="关联模型类型，可选值：contract, project, customer")
    object_id = serializers.UUIDField(help_text="关联对象ID")
    category = serializers.ChoiceField(choices=Attachment.CATEGORY_CHOICES, required=True, help_text="附件类别")
    description = serializers.CharField(max_length=500, required=False, allow_blank=True, help_text="附件描述")
    
    def validate_content_type(self, value):
        """验证内容类型"""
        allowed_types = ['contract', 'project', 'customer']  # 可以根据需要扩展
        if value not in allowed_types:
            raise serializers.ValidationError(f"不支持的内容类型，支持的类型：{', '.join(allowed_types)}")
        return value
    
    def validate_file(self, value):
        """验证文件"""
        # 检查文件大小（200MB限制）
        max_size = 200 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError(f"文件大小不能超过{max_size // (1024*1024)}MB")
        
        # 检查文件类型
        allowed_extensions = [
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.txt', '.rtf', '.zip', '.rar', '.7z',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp',
            '.mp4', '.avi', '.mov', '.wmv'
        ]
        
        file_extension = None
        if hasattr(value, 'name') and value.name:
            file_extension = '.' + value.name.split('.')[-1].lower()
        
        if file_extension and file_extension not in allowed_extensions:
            raise serializers.ValidationError(f"不支持的文件类型: {file_extension}")
        
        return value
    
    def validate(self, attrs):
        """整体验证"""
        content_type_name = attrs['content_type']
        object_id = attrs['object_id']
        
        # 验证关联对象是否存在
        try:
            if content_type_name == 'contract':
                from erp.models import Contract
                Contract.objects.get(id=object_id, delete_datetime__isnull=True)
            elif content_type_name == 'project':
                from erp.models import Project
                Project.objects.get(id=object_id, delete_datetime__isnull=True)
            elif content_type_name == 'customer':
                from erp.models import Customer
                Customer.objects.get(id=object_id, delete_datetime__isnull=True)
        except Exception:
            raise serializers.ValidationError(f"关联的{content_type_name}对象不存在")
        
        return attrs



class AttachmentQuerySerializer(serializers.Serializer):
    """附件查询参数序列化器"""
    
    content_type = serializers.CharField(required=False, help_text="关联模型类型，可选值：contract, project, customer")
    object_id = serializers.UUIDField(required=False, help_text="关联对象ID")
    file_type = serializers.CharField(required=False, help_text="文件类型筛选")
    search = serializers.CharField(required=False, help_text="文件名搜索")
    page = serializers.IntegerField(required=False, default=1, help_text="页码")
    page_size = serializers.IntegerField(required=False, default=10, help_text="每页数量")


class AttachmentUpdateSerializer(serializers.ModelSerializer):
    """附件更新序列化器"""
    
    class Meta:
        model = Attachment
        fields = ['description']
        
    def validate_description(self, value):
        """验证描述"""
        if value and len(value) > 500:
            raise serializers.ValidationError("描述长度不能超过500字符")
        return value
