from rest_framework import serializers
from erp.models import Project, Partner
from erp.models.custom_field import CustomFieldValue


class ProjectSerializer(serializers.ModelSerializer):
    """项目序列化器"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    completion_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')

    # 只读字段
    duration_days = serializers.ReadOnlyField()
    is_overdue = serializers.ReadOnlyField()
    status_display_with_overdue = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()

    # 自定义字段
    custom_fields = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id', 'code', 'name', 'description', 'type', 'status', 'reason',
            'customer', 'customer_code', 'customer_name',
            'end_user_name', 'end_user_contact', 'end_user_phone', 'end_user_address',
            'sales_manager_id', 'sales_manager_name',
            'start_date', 'end_date', 'completion_datetime', 'completion_remark', 'budget', 'expected_profit_rate', 'progress',
            'create_datetime', 'update_datetime', 'creator', 'updater',
            'duration_days', 'is_overdue', 'status_display_with_overdue', 'display_name', 'custom_fields'
        ]
        read_only_fields = [
            'id', 'code', 'customer_code', 'customer_name', 
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'project')

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加类型、状态、理由的显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['reason_display'] = instance.get_reason_display()

        # 将自定义字段展开到顶级
        custom_fields = data.get('custom_fields', {})
        # 移除嵌套的custom_fields对象（无论是否有值）
        data.pop('custom_fields', None)
        # 将每个自定义字段作为顶级字段添加
        if custom_fields:
            for field_name, field_info in custom_fields.items():
                data[field_name] = field_info.get('value')

        return data


class ProjectListSerializer(serializers.ModelSerializer):
    """项目列表序列化器（简化版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    completion_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')

    # 只读字段
    is_overdue = serializers.ReadOnlyField()
    status_display_with_overdue = serializers.ReadOnlyField()

    # 自定义字段
    custom_fields = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id', 'code', 'name', 'type', 'status',
            'customer_code', 'customer_name', 'sales_manager_name',
            'start_date', 'end_date', 'completion_datetime', 'completion_remark', 'progress', 'expected_profit_rate',
            'create_datetime', 'is_overdue', 'status_display_with_overdue', 'custom_fields'
        ]

    def get_custom_fields(self, obj):
        """获取自定义字段值"""
        return CustomFieldValue.get_values_for_object(obj.id, 'project')

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加类型和状态的显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()

        # 将自定义字段展开到顶级
        custom_fields = data.get('custom_fields', {})
        # 移除嵌套的custom_fields对象（无论是否有值）
        data.pop('custom_fields', None)
        # 将每个自定义字段作为顶级字段添加
        if custom_fields:
            for field_name, field_info in custom_fields.items():
                data[field_name] = field_info.get('value')

        return data


class ProjectCreateUpdateSerializer(serializers.ModelSerializer):
    """项目创建/更新序列化器"""

    # 客户ID字段，用于接收前端传递的客户ID
    customer_id = serializers.UUIDField(write_only=True, help_text='客户ID')

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Project
        fields = [
            'id', 'code', 'name', 'description', 'type', 'status', 'reason',
            'customer_id', 'customer_code', 'customer_name',
            'end_user_name', 'end_user_contact',
            'end_user_phone', 'end_user_address',
            'sales_manager_id', 'sales_manager_name',
            'start_date', 'end_date', 'completion_remark', 'budget', 'expected_profit_rate', 'progress',
            'create_datetime', 'update_datetime', 'custom_fields'
        ]
        read_only_fields = [
            'id', 'code', 'customer_code', 'customer_name',
            'create_datetime', 'update_datetime'
        ]
        extra_kwargs = {
            'name': {'required': True, 'help_text': '项目名称'},
            'type': {'required': True, 'help_text': '项目类型'},
            'status': {'required': True, 'help_text': '项目状态'},
            'end_user_name': {'required': True, 'help_text': '最终用户名称'},
            'sales_manager_id': {'required': False, 'help_text': '销售负责人ID'},
            'sales_manager_name': {'required': False, 'help_text': '销售负责人姓名'},
            'start_date': {'required': True, 'help_text': '项目开始日期'},
            'end_date': {'required': True, 'help_text': '项目结束日期'},
            'expected_profit_rate': {'help_text': '预计毛利率(%)'},
            'budget': {'help_text': '项目预算'},
            'progress': {'help_text': '项目进度(%)'},
        }

    def validate_name(self, value):
        """验证项目名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("项目名称不能为空")
        return value.strip()

    def validate_end_user_name(self, value):
        """验证最终用户名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("最终用户名称不能为空")
        return value.strip()

    def validate_sales_manager_name(self, value):
        """验证销售负责人姓名"""
        if value is not None and (not value or not value.strip()):
            raise serializers.ValidationError("销售负责人姓名不能为空")
        return value.strip() if value else value

    def validate_customer_id(self, value):
        """验证客户ID"""
        try:
            customer = Partner.objects.get(
                id=value,
                delete_datetime__isnull=True,
                type__in=['C', 'G']  # 只允许客户类型的相对方
            )
            return customer
        except Partner.DoesNotExist:
            raise serializers.ValidationError("客户不存在或不是有效的客户类型")

    def validate_expected_profit_rate(self, value):
        """验证预计毛利率"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("预计毛利率必须在0-100之间")
        return value

    def validate_progress(self, value):
        """验证项目进度"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("项目进度必须在0-100之间")
        return value

    def validate_end_user_phone(self, value):
        """验证最终用户电话"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=True, field_name="最终用户电话")

    def validate(self, attrs):
        """交叉验证"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("项目开始日期不能晚于结束日期")

        # 验证项目名称唯一性
        name = attrs.get('name')
        if name:
            name = name.strip()
            existing = Project.objects.filter(
                name=name,
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除当前记录
            if self.instance:
                existing = existing.exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError({
                    'name': f"项目名称 '{name}' 已存在"
                })

        return attrs

    def create(self, validated_data):
        """创建项目，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 从validated_data中取出customer对象
        customer = validated_data.pop('customer_id')

        # 设置customer外键
        validated_data['customer'] = customer

        # 创建项目
        project = super().create(validated_data)

        # 设置自定义字段值
        if custom_fields_data:
            CustomFieldValue.set_values_for_object(
                project.id, 'project', custom_fields_data,
                self.context.get('request').user.username if self.context.get('request') else None
            )

        return project

    def update(self, instance, validated_data):
        """更新项目，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 如果有customer_id，更新customer外键
        if 'customer_id' in validated_data:
            customer = validated_data.pop('customer_id')
            validated_data['customer'] = customer

        # 更新项目
        project = super().update(instance, validated_data)

        # 设置自定义字段值
        if custom_fields_data:
            CustomFieldValue.set_values_for_object(
                project.id, 'project', custom_fields_data,
                self.context.get('request').user.username if self.context.get('request') else None
            )

        return project


class ProjectDetailSerializer(ProjectSerializer):
    """项目详情序列化器"""

    # 添加统计信息
    task_count = serializers.SerializerMethodField()
    milestone_count = serializers.SerializerMethodField()
    member_count = serializers.SerializerMethodField()

    class Meta(ProjectSerializer.Meta):
        fields = ProjectSerializer.Meta.fields + [
            'task_count', 'milestone_count', 'member_count'
        ]

    def get_task_count(self, obj):
        """获取任务数量"""
        return obj.get_task_count()

    def get_milestone_count(self, obj):
        """获取里程碑数量"""
        return obj.get_milestone_count()

    def get_member_count(self, obj):
        """获取项目成员数量"""
        return obj.get_member_count()


class ProjectSimpleSerializer(serializers.ModelSerializer):
    """项目简单序列化器（用于下拉选择等场景）"""

    class Meta:
        model = Project
        fields = ['id', 'code', 'name', 'status']

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        data['display_name'] = instance.display_name
        data['status_display'] = instance.get_status_display()
        return data


class ProjectPartialUpdateSerializer(serializers.ModelSerializer):
    """项目部分更新序列化器（PATCH）"""

    # 客户ID字段，用于接收前端传递的客户ID
    customer_id = serializers.UUIDField(write_only=True, required=False, help_text='客户ID')

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    start_date = serializers.DateField(format='%Y-%m-%d', required=False)
    end_date = serializers.DateField(format='%Y-%m-%d', required=False)

    # 自定义字段
    custom_fields = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="自定义字段值，格式：[{\"field_name\": \"字段名称\", \"value\": \"字段值\"}]"
    )

    class Meta:
        model = Project
        fields = [
            'id', 'code', 'name', 'description', 'type', 'status', 'reason',
            'customer_id', 'customer_code', 'customer_name',
            'end_user_name', 'end_user_contact',
            'end_user_phone', 'end_user_address',
            'sales_manager_id', 'sales_manager_name',
            'start_date', 'end_date', 'completion_remark', 'budget', 'expected_profit_rate', 'progress',
            'create_datetime', 'update_datetime', 'custom_fields'
        ]
        read_only_fields = [
            'id', 'code', 'customer_code', 'customer_name',
            'create_datetime', 'update_datetime'
        ]
        # PATCH时所有字段都是可选的
        extra_kwargs = {
            'name': {'required': False, 'help_text': '项目名称'},
            'type': {'required': False, 'help_text': '项目类型'},
            'status': {'required': False, 'help_text': '项目状态'},
            'end_user_name': {'required': False, 'help_text': '最终用户名称'},
            'sales_manager_id': {'required': False, 'help_text': '销售负责人ID'},
            'sales_manager_name': {'required': False, 'help_text': '销售负责人姓名'},
            'expected_profit_rate': {'required': False, 'help_text': '预计毛利率(%)'},
            'budget': {'required': False, 'help_text': '项目预算'},
            'progress': {'required': False, 'help_text': '项目进度(%)'},
        }

    def validate_name(self, value):
        """验证项目名称"""
        if value is not None and (not value or not value.strip()):
            raise serializers.ValidationError("项目名称不能为空")
        return value.strip() if value else value

    def validate_end_user_name(self, value):
        """验证最终用户名称"""
        if value is not None and (not value or not value.strip()):
            raise serializers.ValidationError("最终用户名称不能为空")
        return value.strip() if value else value

    def validate_sales_manager_name(self, value):
        """验证销售负责人姓名"""
        if value is not None and (not value or not value.strip()):
            raise serializers.ValidationError("销售负责人姓名不能为空")
        return value.strip() if value else value

    def validate_customer_id(self, value):
        """验证客户ID"""
        if value is not None:
            try:
                customer = Customer.objects.get(id=value, delete_datetime__isnull=True)
                return customer
            except Customer.DoesNotExist:
                raise serializers.ValidationError("客户不存在")
        return value

    def validate_expected_profit_rate(self, value):
        """验证预计毛利率"""
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("预计毛利率必须在0-100之间")
        return value

    def validate_progress(self, value):
        """验证项目进度"""
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("项目进度必须在0-100之间")
        return value

    def validate_end_user_phone(self, value):
        """验证最终用户电话"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=True, field_name="最终用户电话")

    def validate(self, attrs):
        """交叉验证"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')

        # 如果同时提供了开始和结束日期，验证逻辑关系
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("项目开始日期不能晚于结束日期")

        # 验证项目名称唯一性（如果提供了名称）
        if 'name' in attrs and attrs['name']:
            name = attrs['name'].strip()
            existing = Project.objects.filter(
                name=name,
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除当前记录
            if self.instance:
                existing = existing.exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError({
                    'name': f"项目名称 '{name}' 已存在"
                })

        return attrs

    def update(self, instance, validated_data):
        """更新项目，支持自定义字段"""
        # 提取自定义字段数据
        custom_fields_data = validated_data.pop('custom_fields', [])

        # 如果提供了customer_id，更新customer外键
        if 'customer_id' in validated_data:
            customer = validated_data.pop('customer_id')
            if customer:
                validated_data['customer'] = customer

        # 更新项目
        project = super().update(instance, validated_data)

        # 设置自定义字段值
        if custom_fields_data:
            CustomFieldValue.set_values_for_object(
                project.id, 'project', custom_fields_data,
                self.context.get('request').user.username if self.context.get('request') else None
            )

        return project
