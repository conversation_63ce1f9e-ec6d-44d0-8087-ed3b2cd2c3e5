from rest_framework import serializers
from erp.models import Customer


class CustomerSerializer(serializers.ModelSerializer):
    """客户序列化器（基础版本，不包含统计信息）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Customer
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'phone', 'email', 'address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city']

    def validate(self, attrs):
        """验证并自动填充省市名称"""
        # 如果提供了省份代码，自动填充省份名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            # 如果省份代码为空，清空省份名称
            attrs['province'] = None

        # 如果提供了城市代码，自动填充城市名称
        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name

                # 验证城市是否属于指定的省份
                if 'province_code' in attrs and attrs['province_code']:
                    if city.parent_code != attrs['province_code']:
                        raise serializers.ValidationError({"city_code": "城市不属于指定的省份"})
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            # 如果城市代码为空，清空城市名称
            attrs['city'] = None

        return super().validate(attrs)

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['display_name'] = instance.display_name

        return data


class CustomerDetailSerializer(serializers.ModelSerializer):
    """客户详情序列化器（包含统计信息）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 统计字段
    project_count = serializers.SerializerMethodField()
    sales_contract_count = serializers.SerializerMethodField()
    sales_contract_total_amount = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'phone', 'email', 'address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone',
            'create_datetime', 'update_datetime', 'creator', 'updater',
            'project_count', 'sales_contract_count', 'sales_contract_total_amount'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city',
                           'project_count', 'sales_contract_count', 'sales_contract_total_amount']

    def get_project_count(self, obj):
        """获取该客户关联的项目总数量（只统计未删除的项目）"""
        return obj.projects.filter(delete_datetime__isnull=True).count()

    def get_sales_contract_count(self, obj):
        """获取该客户的销售合同数量（只统计未删除且category='sales'的合同）"""
        from erp.models import Contract
        return Contract.objects.filter(
            customer=obj,
            category='sales',
            delete_datetime__isnull=True
        ).count()

    def get_sales_contract_total_amount(self, obj):
        """获取该客户销售合同的总金额（所有销售合同的amount字段求和）"""
        from erp.models import Contract
        from django.db.models import Sum

        result = Contract.objects.filter(
            customer=obj,
            category='sales',
            delete_datetime__isnull=True
        ).aggregate(total_amount=Sum('amount'))

        return result['total_amount'] or 0

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 添加显示名称
        data['display_name'] = instance.display_name

        # 添加类型、状态和行业的显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = instance.get_industry_display()

        return data


# class CustomerListSerializer(serializers.ModelSerializer):
#     """客户列表序列化器（简化版，不包含统计信息）"""

#     # 自定义时间格式
#     create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

#     class Meta:
#         model = Customer
#         fields = [
#             'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
#             'contact_person', 'phone', 'email', 'owner_name', 'create_datetime'
#         ]

#     def to_representation(self, instance):
#         """自定义序列化输出"""
#         data = super().to_representation(instance)

#         # 添加类型、状态和行业的显示名称
#         data['type_display'] = instance.get_type_display()
#         data['status_display'] = instance.get_status_display()
#         data['industry_display'] = instance.get_industry_display()

#         return data


class CustomerCreateUpdateSerializer(serializers.ModelSerializer):
    """客户创建/完全更新序列化器（PUT）"""

    # 可选的编码日期字段，用于历史数据导入
    code_date = serializers.DateField(
        required=False,
        write_only=True,
        help_text='编码生成日期，格式：YYYY-MM-DD。如果不提供，则使用当前日期。主要用于历史数据导入。'
    )

    class Meta:
        model = Customer
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'phone', 'email', 'address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone',
            'code_date'
        ]
        extra_kwargs = {
            'name': {'required': True, 'allow_blank': False, 'help_text': '客户名称'},
            'type': {'required': True, 'help_text': '客户类型'},
            'status': {'required': False, 'help_text': '客户状态'},
            'industry': {'required': True, 'help_text': '所属行业'},
            'contact_person': {'required': True, 'allow_blank': False, 'help_text': '联系人'},
            'phone': {'required': True, 'allow_blank': False, 'help_text': '联系电话'},
            'tax_id': {'help_text': '纳税人识别号'},
            'email': {'help_text': '电子邮箱'},
            'address': {'help_text': '联系地址'},
        }
    
    def validate_name(self, value):
        """验证客户名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("客户名称不能为空")
        return value.strip()
    
    def validate_phone(self, value):
        """验证电话号码"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=False, field_name="电话号码")

    def validate_email(self, value):
        """验证邮箱"""
        if value and '@' not in value:
            raise serializers.ValidationError("请输入有效的邮箱地址")
        return value

    def validate_tax_id(self, value):
        """验证税号"""
        if value and value.strip():
            value = value.strip()
            # 检查税号唯一性（只针对未删除的记录）
            existing = Customer.objects.filter(
                tax_id=value,
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除当前记录
            if self.instance:
                existing = existing.exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError(f"税号 {value} 已存在")
        return value

    def validate_invoice_bank_account(self, value):
        """验证银行账号"""
        from erp.utils.validators import validate_bank_account
        return validate_bank_account(value, allow_null=True, field_name="银行账号")

    def validate(self, attrs):
        """整体验证"""
        # 自动填充省市名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name

                # 验证城市是否属于指定的省份
                if 'province_code' in attrs and attrs['province_code']:
                    if city.parent_code != attrs['province_code']:
                        raise serializers.ValidationError({"city_code": "城市不属于指定的省份"})
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        # 验证客户名称唯一性
        name = attrs.get('name')
        if name:
            name = name.strip()
            existing = Customer.objects.filter(
                name=name,
                delete_datetime__isnull=True
            )
            # 如果是更新操作，排除当前记录
            if self.instance:
                existing = existing.exclude(id=self.instance.id)

            if existing.exists():
                raise serializers.ValidationError({
                    'name': f"客户名称 '{name}' 已存在"
                })

        return attrs

    def create(self, validated_data):
        """创建客户，支持自定义编码日期"""
        # 提取编码日期参数
        code_date = validated_data.pop('code_date', None)

        # 创建客户实例
        customer = Customer(**validated_data)

        # 保存时传递编码日期参数
        if code_date:
            customer.save(code_date=code_date)
        else:
            customer.save()

        return customer




class CustomerStatusUpdateSerializer(serializers.ModelSerializer):
    """客户状态更新序列化器"""

    class Meta:
        model = Customer
        fields = ['status']
        extra_kwargs = {
            'status': {'required': True, 'help_text': '客户状态，可选值：ACTIVE(活跃)、BLACKLISTED(黑名单)、DEACTIVATED(已注销)'}
        }

    def validate_status(self, value):
        """验证状态值"""
        valid_statuses = [choice[0] for choice in Customer.CUSTOMER_STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(f"无效的状态值。可选值：{', '.join(valid_statuses)}")
        return value


class CustomerQuerySerializer(serializers.Serializer):
    """客户查询参数序列化器"""
    search = serializers.CharField(required=False, help_text="模糊搜索（客户名称、编号、纳税人识别号）")
    type = serializers.CharField(required=False, help_text="客户类型")
    industry = serializers.CharField(required=False, help_text="行业类型")
    province = serializers.CharField(required=False, help_text="省份")
    city = serializers.CharField(required=False, help_text="城市")
    date_start = serializers.DateField(required=False, help_text="创建开始日期")
    date_end = serializers.DateField(required=False, help_text="创建结束日期")
    page = serializers.IntegerField(required=False, default=1, help_text="页码")
    page_size = serializers.IntegerField(required=False, default=10, help_text="每页数量")
    ordering = serializers.CharField(required=False, help_text="排序字段")


class CustomerPartialUpdateSerializer(serializers.ModelSerializer):
    """客户部分更新序列化器（PATCH）"""

    class Meta:
        model = Customer
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'phone', 'email', 'address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone'
        ]


class CustomerSimpleSerializer(serializers.ModelSerializer):
    """客户简单序列化器（用于下拉选择等场景）"""

    class Meta:
        model = Customer
        fields = ['id', 'code', 'name', 'status']

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        data['status_display'] = instance.get_status_display()
        return data
        # PATCH时所有字段都是可选的
        extra_kwargs = {
            'name': {'required': False, 'help_text': '客户名称'},
            'type': {'required': False, 'help_text': '客户类型'},
            'status': {'required': False, 'help_text': '客户状态'},
            'industry': {'required': False, 'help_text': '所属行业'},
            'contact_person': {'required': False, 'help_text': '联系人'},
            'phone': {'required': False, 'help_text': '联系电话'},
            'tax_id': {'required': False, 'help_text': '纳税人识别号'},
            'email': {'required': False, 'help_text': '电子邮箱'},
            'address': {'required': False, 'help_text': '联系地址'},
        }

    def validate_name(self, value):
        """验证客户名称"""
        if value is not None and (not value or not value.strip()):
            raise serializers.ValidationError("客户名称不能为空")
        return value.strip() if value else value

    def validate_phone(self, value):
        """验证电话号码"""
        from erp.utils.validators import validate_phone_number
        return validate_phone_number(value, allow_null=True, field_name="电话号码")

    def validate_tax_id(self, value):
        """验证纳税人识别号"""
        if value is not None:
            value = value.strip()
            # 检查是否与其他客户重复
            existing_customer = Customer.objects.filter(
                tax_id=value,
                delete_datetime__isnull=True
            ).exclude(id=self.instance.id if self.instance else None).first()

            if existing_customer:
                raise serializers.ValidationError("该纳税人识别号已存在")
        return value

    def validate_invoice_bank_account(self, value):
        """验证银行账号"""
        from erp.utils.validators import validate_bank_account
        return validate_bank_account(value, allow_null=True, field_name="银行账号")

    def validate(self, attrs):
        """整体验证"""
        # 自动填充省市名称
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name

                # 验证城市是否属于指定的省份
                if 'province_code' in attrs and attrs['province_code']:
                    if city.parent_code != attrs['province_code']:
                        raise serializers.ValidationError({"city_code": "城市不属于指定的省份"})
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        # 如果提供了客户名称，检查是否与其他客户重复
        if 'name' in attrs and attrs['name']:
            name = attrs['name'].strip()
            existing_customer = Customer.objects.filter(
                name=name,
                delete_datetime__isnull=True
            ).exclude(id=self.instance.id if self.instance else None).first()

            if existing_customer:
                raise serializers.ValidationError({"name": ["该客户名称已存在"]})

        return attrs
