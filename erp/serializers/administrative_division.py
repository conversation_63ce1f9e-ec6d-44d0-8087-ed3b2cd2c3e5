from rest_framework import serializers
from erp.models import AdministrativeDivision


class AdministrativeDivisionSerializer(serializers.ModelSerializer):
    """行政区划序列化器"""
    
    class Meta:
        model = AdministrativeDivision
        fields = ['code', 'name', 'level', 'parent_code']


class ProvinceSerializer(serializers.ModelSerializer):
    """省份序列化器"""
    
    class Meta:
        model = AdministrativeDivision
        fields = ['code', 'name']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        # 确保只返回省级数据
        if instance.level != 'province':
            return None
        return data


class CitySerializer(serializers.ModelSerializer):
    """城市序列化器"""
    
    class Meta:
        model = AdministrativeDivision
        fields = ['code', 'name', 'parent_code']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        # 确保只返回市级数据
        if instance.level != 'city':
            return None
        return data


class DistrictSerializer(serializers.ModelSerializer):
    """区县序列化器"""
    
    class Meta:
        model = AdministrativeDivision
        fields = ['code', 'name', 'parent_code']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        # 确保只返回区县级数据
        if instance.level != 'district':
            return None
        return data
