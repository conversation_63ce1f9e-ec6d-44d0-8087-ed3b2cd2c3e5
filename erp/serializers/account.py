from rest_framework import serializers
from erp.models import PaymentPeriod, Contract
from decimal import Decimal


class PaymentPeriodListSerializer(serializers.ModelSerializer):
    """期数列表序列化器（F003-02）"""
    
    # 状态显示字段（根据合同类型）
    status_display = serializers.CharField(source='status_display_by_contract_type', read_only=True)
    
    # 时间格式化
    date = serializers.DateField(format='%Y-%m-%d')
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = PaymentPeriod
        fields = [
            'id', 'period', 'amount', 'date',
            'status', 'status_display', 'description',
            'create_datetime', 'update_datetime'
        ]


class PaymentPeriodDetailSerializer(serializers.ModelSerializer):
    """期数详情序列化器"""
    
    # 合同信息
    contract_info = serializers.SerializerMethodField()
    
    # 状态显示字段
    status_display = serializers.CharField(source='status_display_by_contract_type', read_only=True)
    
    # 时间格式化
    date = serializers.DateField(format='%Y-%m-%d')
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = PaymentPeriod
        fields = [
            'id', 'contract_info', 'period', 'amount', 'date',
            'status', 'status_display', 'description',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]

    def get_contract_info(self, obj):
        """获取合同基本信息"""
        return {
            'id': str(obj.contract.id),
            'code': obj.contract.code,
            'name': obj.contract.name,
            'category': obj.contract.category,
            'category_display': obj.contract.category_display,
        }


class PaymentPeriodCreateSerializer(serializers.ModelSerializer):
    """期数创建序列化器"""
    
    # 时间格式化
    date = serializers.DateField(format='%Y-%m-%d')

    class Meta:
        model = PaymentPeriod
        fields = [
            'period', 'amount', 'date', 'status', 'description'
        ]

    def validate_period(self, value):
        """验证期数"""
        if value <= 0:
            raise serializers.ValidationError("期数必须大于0")
        return value

    def validate_amount(self, value):
        """验证金额"""
        if value <= 0:
            raise serializers.ValidationError("期数金额必须大于0")
        return value

    def validate(self, attrs):
        """验证期数唯一性"""
        contract = self.context.get('contract')
        period = attrs.get('period')
        
        if contract and period:
            # 检查同一合同的期数是否已存在
            if PaymentPeriod.objects.filter(
                contract=contract,
                period=period,
                delete_datetime__isnull=True
            ).exists():
                raise serializers.ValidationError({
                    'period': f"第{period}期已存在"
                })
        
        return attrs

    def create(self, validated_data):
        """创建期数"""
        contract = self.context.get('contract')
        validated_data['contract'] = contract
        
        period = PaymentPeriod(**validated_data)
        period.save()
        
        return period


class PaymentPeriodUpdateSerializer(serializers.ModelSerializer):
    """期数更新序列化器"""
    
    # 时间格式化
    date = serializers.DateField(format='%Y-%m-%d', required=False)

    class Meta:
        model = PaymentPeriod
        fields = [
            'amount', 'date', 'status', 'description'
        ]
        extra_kwargs = {
            'amount': {'required': False},
            'status': {'required': False},
        }

    def validate_amount(self, value):
        """验证金额"""
        if value is not None and value <= 0:
            raise serializers.ValidationError("期数金额必须大于0")
        return value

    def update(self, instance, validated_data):
        """更新期数"""
        # 更新基础字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance


class ContractAccountSummarySerializer(serializers.ModelSerializer):
    """合同账款汇总序列化器（F005-02）"""
    
    # 合同基本信息
    contract_name = serializers.CharField(source='name', read_only=True)
    contract_code = serializers.CharField(source='code', read_only=True)
    contract_category = serializers.CharField(source='category', read_only=True)
    contract_category_display = serializers.CharField(source='category_display', read_only=True)
    contract_total_amount = serializers.DecimalField(source='total_amount', max_digits=15, decimal_places=2, read_only=True)

    # 项目信息
    project_id = serializers.CharField(source='project.id', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    
    # 相对方信息
    contract_partners = serializers.SerializerMethodField()

    # 期数统计
    period_count = serializers.SerializerMethodField()

    # 最后一期信息
    last_period_date = serializers.SerializerMethodField()
    last_period_status = serializers.SerializerMethodField()
    
    # 时间格式化
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Contract
        fields = [
            'id', 'contract_name', 'contract_code', 'contract_category',
            'contract_category_display', 'contract_total_amount', 'project_id', 'project_name',
            'contract_partners', 'period_count', 'last_period_date', 'last_period_status',
            'create_datetime', 'update_datetime'
        ]

    def get_contract_partners(self, obj):
        """获取合同相对方信息"""
        partners = obj.partners.all()
        return [{'id': str(p.id), 'name': p.name} for p in partners]

    def get_period_count(self, obj):
        """获取合同期数数量"""
        return obj.payment_periods.filter(delete_datetime__isnull=True).count()

    def get_last_period_date(self, obj):
        """获取最后一期的日期"""
        last_period = obj.payment_periods.filter(
            delete_datetime__isnull=True
        ).order_by('-period').first()

        if last_period:
            return last_period.date.strftime('%Y-%m-%d')
        return None

    def get_last_period_status(self, obj):
        """获取最后一期的状态"""
        last_period = obj.payment_periods.filter(
            delete_datetime__isnull=True
        ).order_by('-period').first()

        if last_period:
            return last_period.status
        return None




class ContractAccountStatusSerializer(serializers.Serializer):
    """合同账款状态跟踪序列化器（F005-04）"""
    
    # 合同基本信息
    contract_info = serializers.SerializerMethodField()
    
    # 账款状态信息
    account_status = serializers.SerializerMethodField()
    
    # 期数列表
    periods = serializers.SerializerMethodField()

    def get_contract_info(self, obj):
        """获取合同基本信息"""
        return {
            'id': str(obj.id),
            'code': obj.code,
            'name': obj.name,
            'category': obj.category,
            'category_display': obj.category_display,
            'total_amount': str(obj.total_amount),
        }

    def get_account_status(self, obj):
        """获取账款状态信息"""
        summary = PaymentPeriod.calculate_contract_summary(obj.id)
        
        if obj.category == 'sales':
            return {
                'type': 'receivable',
                'status_text': '收款状态',
                'completed_text': '已收款',
                'pending_text': '待收款',
                'completed_amount': summary['completed_amount'],
                'pending_amount': summary['pending_amount'],
                'completed_count': summary['completed_count'],
                'pending_count': summary['pending_count'],
                'progress': summary['progress'],
            }
        else:
            return {
                'type': 'payable',
                'status_text': '付款状态',
                'completed_text': '已付款',
                'pending_text': '待付款',
                'completed_amount': summary['completed_amount'],
                'pending_amount': summary['pending_amount'],
                'completed_count': summary['completed_count'],
                'pending_count': summary['pending_count'],
                'progress': summary['progress'],
            }

    def get_periods(self, obj):
        """获取期数列表"""
        periods = PaymentPeriod.get_periods_by_contract(obj.id)
        return PaymentPeriodListSerializer(periods, many=True).data
