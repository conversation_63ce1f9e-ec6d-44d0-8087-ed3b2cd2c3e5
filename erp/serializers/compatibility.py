"""
向后兼容性序列化器
为了保持现有API的兼容性，提供Customer和Supplier的兼容性序列化器
这些序列化器内部使用Partner模型，但对外保持原有的API接口
"""

from rest_framework import serializers
from erp.models import Partner, PartnerPaymentInfo


class CustomerCompatibilitySerializer(serializers.ModelSerializer):
    """客户兼容性序列化器 - 基于Partner模型但保持Customer API接口"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city']

    def to_representation(self, instance):
        """自定义序列化输出，保持Customer API格式"""
        data = super().to_representation(instance)

        # 字段名兼容性映射（保持旧的API字段名）
        if 'contact_phone' in data:
            data['phone'] = data.pop('contact_phone')
        if 'contact_email' in data:
            data['email'] = data.pop('contact_email')
        if 'contact_address' in data:
            data['address'] = data.pop('contact_address')

        # 添加显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        # 添加统计信息（客户特有）
        data['project_count'] = instance.get_active_projects_count()
        data['sales_contract_count'] = instance.get_sales_contract_count()
        data['sales_contract_total_amount'] = instance.get_sales_contract_total_amount()

        return data

    def validate(self, attrs):
        """验证数据，确保只能创建客户类型"""
        # 强制设置为客户类型
        if 'type' not in attrs or attrs['type'] not in ['C', 'G']:
            attrs['type'] = 'C'  # 默认为企业客户
        
        return super().validate(attrs)


class SupplierCompatibilitySerializer(serializers.ModelSerializer):
    """供应商兼容性序列化器 - 基于Partner模型但保持Supplier API接口"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    update_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 嵌套收款信息
    payment_infos = serializers.SerializerMethodField()

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'province', 'city_code', 'city',
            'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'payment_infos',
            'create_datetime', 'update_datetime', 'creator', 'updater'
        ]
        read_only_fields = ['id', 'code', 'create_datetime', 'update_datetime', 'province', 'city']

    def get_payment_infos(self, obj):
        """获取收款信息"""
        payment_infos = obj.payment_infos.filter(delete_datetime__isnull=True)
        from erp.serializers.partner import PartnerPaymentInfoSerializer
        return PartnerPaymentInfoSerializer(payment_infos, many=True).data

    def to_representation(self, instance):
        """自定义序列化输出，保持Supplier API格式"""
        data = super().to_representation(instance)

        # 字段名兼容性映射（保持旧的API字段名）
        if 'contact_phone' in data:
            data['phone'] = data.pop('contact_phone')
        if 'contact_email' in data:
            data['email'] = data.pop('contact_email')
        if 'contact_address' in data:
            data['address'] = data.pop('contact_address')

        # 添加显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        # 添加统计信息（供应商特有）
        data['purchase_contract_count'] = instance.get_purchase_contract_count()
        data['purchase_contract_total_amount'] = instance.get_purchase_contract_total_amount()

        return data

    def validate(self, attrs):
        """验证数据，确保只能创建供应商类型"""
        # 强制设置为供应商类型
        if 'type' not in attrs or attrs['type'] not in ['ORIGINAL', 'CHANNEL']:
            attrs['type'] = 'ORIGINAL'  # 默认为原厂
        
        return super().validate(attrs)


class CustomerListCompatibilitySerializer(serializers.ModelSerializer):
    """客户列表兼容性序列化器（简化版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province', 'city', 'contact_person', 'contact_phone', 'contact_email', 'owner_name', 'create_datetime'
        ]

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 字段名兼容性映射（保持旧的API字段名）
        if 'contact_phone' in data:
            data['phone'] = data.pop('contact_phone')
        if 'contact_email' in data:
            data['email'] = data.pop('contact_email')

        # 添加显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        return data


class SupplierListCompatibilitySerializer(serializers.ModelSerializer):
    """供应商列表兼容性序列化器（简化版）"""

    # 自定义时间格式
    create_datetime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Partner
        fields = [
            'id', 'code', 'name', 'tax_id', 'type', 'status', 'industry',
            'province', 'city', 'contact_person', 'contact_phone', 'contact_email', 'owner_name', 'create_datetime'
        ]

    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)

        # 字段名兼容性映射（保持旧的API字段名）
        if 'contact_phone' in data:
            data['phone'] = data.pop('contact_phone')
        if 'contact_email' in data:
            data['email'] = data.pop('contact_email')

        # 添加显示名称
        data['type_display'] = instance.get_type_display()
        data['status_display'] = instance.get_status_display()
        data['industry_display'] = dict(instance.INDUSTRY_CHOICES).get(instance.industry, instance.industry)

        return data


class CustomerCreateUpdateCompatibilitySerializer(serializers.ModelSerializer):
    """客户创建/更新兼容性序列化器"""

    # 可选的编码日期字段，用于历史数据导入
    code_date = serializers.DateField(
        required=False,
        write_only=True,
        help_text='编码生成日期，格式：YYYY-MM-DD。如果不提供，则使用当前日期。主要用于历史数据导入。'
    )

    class Meta:
        model = Partner
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark',
            'owner_name', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone',
            'code_date'
        ]

    def validate(self, attrs):
        """验证数据，确保只能创建客户类型"""
        # 强制设置为客户类型
        if 'type' not in attrs or attrs['type'] not in ['C', 'G']:
            attrs['type'] = 'C'  # 默认为企业客户
        
        # 验证省份和城市代码
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs

    def create(self, validated_data):
        """创建客户，支持自定义编码日期"""
        # 提取编码日期参数
        code_date = validated_data.pop('code_date', None)

        # 创建相对方实例
        partner = Partner(**validated_data)

        # 保存时传递编码日期参数
        if code_date:
            partner.save(code_date=code_date)
        else:
            partner.save()

        return partner


class SupplierCreateUpdateCompatibilitySerializer(serializers.ModelSerializer):
    """供应商创建/更新兼容性序列化器"""

    # 可选的编码日期字段，用于历史数据导入
    code_date = serializers.DateField(
        required=False,
        write_only=True,
        help_text='编码生成日期，格式：YYYY-MM-DD。如果不提供，则使用当前日期。主要用于历史数据导入。'
    )

    class Meta:
        model = Partner
        fields = [
            'name', 'tax_id', 'type', 'status', 'industry',
            'province_code', 'city_code', 'contact_person', 'contact_phone', 'contact_email', 'contact_address', 'contact_remark', 'website',
            'owner_name', 'owner_id', 'remark',
            'invoice_bank', 'invoice_bank_account', 'invoice_address', 'invoice_phone', 'invoice_remark',
            'code_date'
        ]

    def validate(self, attrs):
        """验证数据，确保只能创建供应商类型"""
        # 强制设置为供应商类型
        if 'type' not in attrs or attrs['type'] not in ['ORIGINAL', 'CHANNEL']:
            attrs['type'] = 'ORIGINAL'  # 默认为原厂
        
        # 验证省份和城市代码
        if 'province_code' in attrs and attrs['province_code']:
            try:
                from erp.models import AdministrativeDivision
                province = AdministrativeDivision.objects.get(
                    code=attrs['province_code'],
                    level='province'
                )
                attrs['province'] = province.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"province_code": "省份代码不存在"})
        elif 'province_code' in attrs and not attrs['province_code']:
            attrs['province'] = None

        if 'city_code' in attrs and attrs['city_code']:
            try:
                from erp.models import AdministrativeDivision
                city = AdministrativeDivision.objects.get(
                    code=attrs['city_code'],
                    level='city'
                )
                attrs['city'] = city.name
            except AdministrativeDivision.DoesNotExist:
                raise serializers.ValidationError({"city_code": "城市代码不存在"})
        elif 'city_code' in attrs and not attrs['city_code']:
            attrs['city'] = None

        return attrs

    def create(self, validated_data):
        """创建供应商，支持自定义编码日期"""
        # 提取编码日期参数
        code_date = validated_data.pop('code_date', None)

        # 创建相对方实例
        partner = Partner(**validated_data)

        # 保存时传递编码日期参数
        if code_date:
            partner.save(code_date=code_date)
        else:
            partner.save()

        return partner
