-- 业财一体化系统数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建业务用户（可选）
-- CREATE USER erp_user WITH PASSWORD 'erp_password';
-- GRANT ALL PRIVILEGES ON DATABASE erp_finance TO erp_user;

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建表前缀函数（可选）
CREATE OR REPLACE FUNCTION add_table_prefix()
RETURNS event_trigger AS $$
BEGIN
    -- 可以在这里添加表前缀逻辑
END;
$$ LANGUAGE plpgsql;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '业财一体化系统数据库初始化完成';
    RAISE NOTICE '数据库名称: erp_finance';
    RAISE NOTICE '字符编码: UTF-8';
    RAISE NOTICE '时区设置: Asia/Shanghai';
END $$;
