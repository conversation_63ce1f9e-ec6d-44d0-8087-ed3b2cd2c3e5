# 多选查询功能实现文档

## 概述

本文档描述了为客户、供应商和项目列表接口实现的多选查询功能。该功能允许在API查询参数中使用逗号分隔的值来进行多选过滤，而不修改数据库模型字段的定义。

## 实现范围

### 支持多选查询的接口

1. **客户列表接口** (`GET /api/v1/customers/`)
   - `type` 参数：客户类型多选，如 `?type=C,G`
   - `industry` 参数：行业类型多选，如 `?industry=FINANCE,GOVT,IT`

2. **供应商列表接口** (`GET /api/v1/suppliers/`)
   - `type` 参数：供应商类型多选，如 `?type=C,G`
   - `industry` 参数：行业类型多选，如 `?industry=MANUFACTURING,ENERGY`

3. **项目列表接口** (`GET /api/v1/projects/`)
   - `type` 参数：项目类型多选，如 `?type=system_integration,software_development`

4. **合同列表接口** (`GET /api/v1/contracts/`)：
   - `category` 参数：合同类别多选，如 `?category=sales,procurement`

### 数据存储

- **保持不变**：数据库模型字段定义保持原有的单选格式
- **创建/更新操作**：仍然使用单选值
- **查询操作**：支持多选过滤

## 技术实现

### 1. 自定义过滤器类

创建了 `erp/filters.py` 文件，包含以下核心组件：

#### MultiChoiceFilter 类
```python
class MultiChoiceFilter(django_filters.CharFilter):
    """
    多选过滤器，支持逗号分隔的值
    
    使用方式：
    - 单个值：?type=C
    - 多个值：?type=C,G
    """
    
    def filter(self, qs, value):
        if not value:
            return qs
        
        # 分割逗号分隔的值
        values = [v.strip() for v in value.split(',') if v.strip()]
        if not values:
            return qs
        
        # 使用 __in 查询匹配多个值
        lookup = f'{self.field_name}__in'
        return qs.filter(**{lookup: values})
```

#### 专用过滤器类
- `CustomerFilter`：客户过滤器
- `SupplierFilter`：供应商过滤器
- `ProjectFilter`：项目过滤器
- `ContractFilter`：合同过滤器

### 2. ViewSet 配置

更新了各个ViewSet以使用自定义过滤器：

```python
class CustomerViewSet(JWTRequiredMixin, CreateUpdateMixin, viewsets.ModelViewSet):
    """客户管理视图集"""
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = CustomerFilter
```

### 3. Swagger 文档更新

#### 自定义Swagger检查器

创建了自定义的Swagger检查器 `erp/swagger/inspectors.py`，用于正确显示过滤器的help_text：

```python
class DjangoFilterDescriptionInspector(CoreAPICompatInspector):
    """自定义过滤器检查器，用于正确显示django-filter的help_text"""

    def get_filter_parameters(self, filter_backend):
        if isinstance(filter_backend, DjangoFilterBackend):
            # 从过滤器的extra属性中获取help_text
            description = getattr(filter_field, 'extra', {}).get('help_text', None)
            # 生成正确的参数描述
```

#### 参数描述示例

更新后的API文档中，参数描述明确说明多选功能：

**客户列表接口**：
- `type`: "客户类型，支持多选，使用逗号分隔多个值，如：C,G"
- `industry`: "所属行业，支持多选，使用逗号分隔多个值，如：FINANCE,GOVT,IT"

**供应商列表接口**：
- `type`: "供应商类型，支持多选，使用逗号分隔多个值，如：C,G"
- `industry`: "所属行业，支持多选，使用逗号分隔多个值，如：MANUFACTURING,ENERGY"

**项目列表接口**：
- `type`: "项目类型，支持多选，使用逗号分隔多个值，如：system_integration,software_development"

**合同列表接口**：
- `category`: "合同类别，支持多选，使用逗号分隔多个值，如：sales,procurement"

## 使用示例

### 客户查询示例

```bash
# 单选查询
GET /api/v1/customers/?type=C

# 多选查询
GET /api/v1/customers/?type=C,G

# 行业多选
GET /api/v1/customers/?industry=FINANCE,GOVT,IT

# 组合查询
GET /api/v1/customers/?type=C&industry=FINANCE,IT
```

### 供应商查询示例

```bash
# 类型多选
GET /api/v1/suppliers/?type=C,G

# 行业多选
GET /api/v1/suppliers/?industry=MANUFACTURING,ENERGY

# 状态和类型组合
GET /api/v1/suppliers/?status=ACTIVE&type=C,G
```

### 项目查询示例

```bash
# 项目类型多选
GET /api/v1/projects/?type=system_integration,software_development

# 状态和类型组合
GET /api/v1/projects/?status=in_progress&type=system_integration,product_own
```

### 合同查询示例

```bash
# 合同类别多选
GET /api/v1/contracts/?category=sales,procurement

# 类别和状态组合
GET /api/v1/contracts/?category=sales&status=executing

# 多条件组合查询
GET /api/v1/contracts/?category=sales,procurement&status=executing&amount_min=10000
```

## 功能特性

### 1. 向后兼容
- 单个值查询仍然正常工作
- 现有的API调用不受影响

### 2. 容错处理
- 自动去除空格：`" C , G "` → `["C", "G"]`
- 忽略空值：`"C,,G,"` → `["C", "G"]`
- 无效值处理：`"C,INVALID,G"` → 返回匹配 C 和 G 的记录

### 3. 性能优化
- 使用Django ORM的 `__in` 查询，生成高效的SQL
- 支持数据库索引优化

## 测试验证

### 测试覆盖
- 单选查询测试
- 多选查询测试
- 组合过滤测试
- 边界情况测试（空值、无效值、带空格等）

### 测试结果
所有测试通过，功能正常工作：

```
=== 测试客户多选API ===
✓ 单选查询成功，返回 10 条记录
✓ 多选查询成功，返回 10 条记录
✓ 行业多选查询成功，返回 10 条记录
✓ 组合过滤查询成功，返回 10 条记录

=== 测试供应商多选API ===
✓ 类型多选查询成功，返回 10 条记录
✓ 行业多选查询成功，返回 4 条记录

=== 测试项目多选API ===
✓ 项目类型多选查询成功，返回 2 条记录
✓ 单选查询成功，返回 2 条记录

=== 测试合同多选API ===
✓ 单选查询成功，返回 2 条记录
✓ 多选查询成功，返回 3 条记录
✓ 返回了多种类别的合同: {'sales', 'procurement'}
✓ 无效值查询成功，返回 0 条记录
✓ 混合值查询成功，返回 3 条记录
✓ 带空格值查询成功，返回 3 条记录
```

## 文件清单

### 新增文件
- `erp/filters.py` - 自定义过滤器定义
- `erp/swagger/inspectors.py` - 自定义Swagger检查器，用于正确显示过滤器参数描述
- `docs/multi_choice_query_implementation.md` - 本文档

### 修改文件
- `erp/views/customer.py` - 客户ViewSet配置
- `erp/views/supplier.py` - 供应商ViewSet配置
- `erp/views/project.py` - 项目ViewSet配置
- `erp/views/contract.py` - 合同ViewSet配置，添加ContractFilter支持
- `application/settings.py` - 添加自定义Swagger检查器配置

## 注意事项

1. **数据模型不变**：数据库字段定义和创建/更新操作保持原有的单选格式
2. **只影响查询**：多选功能仅适用于列表查询，不影响数据的创建和更新
3. **性能考虑**：大量多选值可能影响查询性能，建议合理使用
4. **验证逻辑**：过滤器会自动处理无效值，不会抛出错误

## 扩展建议

如需为其他字段添加多选支持，可以：

1. 在相应的FilterSet中添加MultiChoiceFilter字段
2. 更新Swagger文档说明
3. 添加相应的测试用例

例如：
```python
class CustomerFilter(django_filters.FilterSet):
    # 现有字段...
    
    # 新增多选字段
    status = MultiChoiceFilter(
        field_name='status',
        help_text='客户状态，支持多选，逗号分隔'
    )
```
