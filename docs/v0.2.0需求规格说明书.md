# 业财一体化系统原型 - 需求规格说明书

## 文档信息

| 项目名称 | 业财一体化系统原型 (Vue Contract Management) |
|---------|------------------------------------------|
| 文档版本 | v1.0.0 |
| 创建日期 | 2025-07-23 |
| 更新日期 | 2025-08-02 |
| 文档类型 | 需求规格说明书 |

## 1. 项目概述

### 1.1 项目背景
基于Vue 3 + Ant Design Vue技术栈开发的合同管理系统，实现业务与财务的一体化管理。


### 1.2 系统特点
- 🎨 精致界面设计，企业级用户体验
- 📱 响应式布局，支持多种屏幕尺寸
- ⚡ 现代化技术栈，高性能组件
- 🔧 完整业务流程，合同全生命周期管理
- 💾 数据持久化，本地存储支持

## 2. 功能模块清单

### 2.1 系统首页模块 (P1 - 高优先级)

#### 功能编号: F001
**功能名称**: 系统概览仪表板
**功能描述**: 提供系统整体数据统计和快速导航入口
**主要功能点**:
- F001-01: 合同数据统计卡片展示
  - 客户总数统计卡片，显示数量和增长率
  - 供应商总数统计卡片，显示数量和增长率  
  - 进行中项目统计卡片，显示数量和增长率
  - 合同总数统计卡片，显示数量和合同总额
  - 每个卡片包含图标、数值、增长趋势指示器
- F001-02: 实时数据更新
  - 页面加载时自动获取最新统计数据
  - 统计数据与实际业务数据保持同步
  - 支持数据刷新和动态更新显示
- F001-03: 快速操作入口
  - 合同管理快速导航按钮
  - 相对方管理快速导航按钮
  - 项目管理快速导航按钮
  - 一键跳转到对应功能模块
- F001-04: 现代化数据可视化
  - 响应式卡片布局设计
  - 彩色图标和数据展示
  - 增长趋势箭头指示器
  - 统一的视觉设计风格

**验收标准**:
- 统计数据准确反映系统状态
- 卡片布局响应式适配
- 快速导航功能正常

### 2.2 合同管理模块 (P1 - 高优先级)

#### 功能编号: F002
**功能名称**: 合同台账管理
**功能描述**: 合同信息的增删改查和高级搜索功能
**子功能清单**:
- F002-01: 合同列表展示 (表格形式)
  - 分页和排序
  - 操作列：查看详情、删除
- F002-02: 高级搜索和过滤
  - 模糊搜索
  - 每列表头支持筛选
- F002-03: 表头设置
  - 分类：系统字段、自定义字段
  - 新增字段：类型（文本、数字、日期、单选、多选、货币）、名称
  - 删除字段
  - 修改字段：只允许修改名称
  - 关闭、启用自定义字段
- F002-04: 合同详情查看 (抽屉式)
- F002-05: 新增合同表单
  - 系统字段
  ```
  合同名称
  合同类型
  相对方（支持多选，例如三方合同）
  所属项目
  合同编号
  合同总额
  销售负责人
  签订日期
  签订状态（未签约、沟通中、签约中、已签约、解约中）
  生效日期
  终止日期
  履约状态（未履约、履约中、已履约）
  备注
  其他在表头设置中自定义的字段
  ```
  - 支持上传多个附件
  - 支持预览上传的合同文件
- F002-06: 导入台账
  - 支持下载Excel模板
  - 支持多条数据导入
  - 只导入基础数据，后续支持修改字段内容

**验收标准**:
- 支持分页和排序
- 搜索响应时间 < 1秒
- 表单验证完整
- 数据持久化正常

#### 功能编号: F003
**功能名称**: 合同详情管理
**功能描述**: 多标签页展示合同完整信息

**标签页结构**:
- F003-01: 基本信息标签页
  - 显示所有合同字段信息
  - 禁止修改字段：合同编码、合同类型、客户/供应商、所属项目（使用禁止修改的样式）
  - 可编辑字段：合同名称、合同总额、销售负责人、签订日期、签订状态、生效日期、终止日期、履约状态、备注等
  - 实时保存：每个输入框的修改都是及时生效，无需保存、重置按钮
  - 显示计算字段：账款类型、已收/已付、待收/待付、收/付款进度、已开票金额、待开票金额、开票进度
- F003-02: 账款管理标签页
  - 付款\收款期数列表表格展示
    - 一期占一行
    - 字段：
      - 期数
      - 金额
      - 时间
      - 说明
      - 状态：
        - 对于采购合同：已付款、代付款 
        - 对于销售合同：已完成、待完成
  - 新增期数按钮
  - 操作列：删除
  - 支持按状态筛选和搜索
- F003-03: 发票记录标签页
  - 发票记录列表表格展示
  - 新增发票按钮（支持上传发票、直接录入两种方式）
  - 发票录入字段：开票金额、开票时间、开票说明、开票类型、开票类别、收款方、收款人纳税识别号、付款方、付款人纳税识别号、开票代码、开票号码、开票校验码
  - 表头设置功能
  - 操作列：编辑、删除
- F003-04: 关联合同标签页
  - 关键指标卡片：关联合同数、关联应收总额、关联应付总额
  - 关联合同列表表格展示
    - 默认将该项目下的合同展出出来
  - 新建关联合同功能
  - 表头设置功能
  - 显示关联关系类型和相关合同信息

**验收标准**:
- 标签页切换流畅
- 数据展示完整
- 操作响应及时

#### 功能编号: F004
**功能名称**: 独立合同文档管理
**功能描述**: 独立页面管理合同相关文档

**主要功能**:
- F004-01: 合同列表展示（第一层）
  - 显示字段：合同名称、合同编号、文档数量
  - 文档数量显示：带徽章的文件夹图标 + 数量统计
  - 支持按合同名称、编号进行搜索
  - 操作列：查看文档按钮
  - 分页和排序功能
- F004-02: 文档列表展示（第二层）
  - 显示该合同下所有的附件
  - 显示字段：文件名称、文件大小、上传时间、文件类型、上传人
  - 文件类型图标区分：PDF、Word、Excel、图片等
  - 支持按文件名、类型、时间筛选
  - 操作列：预览、下载、删除
  - 返回上级按钮
- F004-03: 文档上传功能
  - 支持拖拽上传和点击选择上传
  - 支持多文件批量上传
  - 支持多种文件格式：PDF、Word、Excel、图片等
  - 文件大小限制和格式验证
  - 上传进度显示和状态反馈
- F004-04: 文档删除功能
  - 单个文档删除确认
  - 批量删除选中文档
- F004-05: 表头设置功能
  - 第一层和第二层分别支持表头自定义
  - 自定义显示列的选择
  - 列宽调整和排序设置
  - 设置结果本地存储

**验收标准**:
- 支持多格式文件
- 文档信息完整显示
- 操作权限控制

#### 功能编号: F005
**功能名称**: 账款管理
**功能描述**: 合同收付款计划和记录管理

**主要功能**:
- F005-01: 账款统计概览
  - 统计卡片展示：总账款金额、已收/已付金额、待收/待付金额、逾期金额
  - 收付款进度可视化展示
- F005-02: 账款记录列表
  - 列表：一个合同占一行，每行显示字段：
    - 合同名称
    - 合同编号
    - 合同相对方
    - 合同类型（销售合同显示"销售"，采购合同显示"采购"）
    - 合同总额
    - 已付款/已收款
    - 待付款/待收款
    - 付款进度/收款进度
    - 下期付款金额/下期收款时间
    - 下期付款时间/下期收款时间
  - 合同名称支持点击跳转到合同详情的账款管理标签页
  - 表头设置功能
- F005-03: 动态操作按钮
  - 根据合同类型显示不同按钮：销售合同显示"新增收款"，采购合同显示"新增付款"
  - 点击后跳转到对应合同详情页面的账款管理标签页
  - 操作列包含：查看详情按钮
- F005-04: 账款状态跟踪（针对单个合同）
  - 收款状态：待收款、已收款、期数
  - 付款状态：待付款、已付款、期数
  - 收付款进度百分比显示
- F005-05: 搜索和过滤功能
  - 支持按合同名称、编号模糊搜索
  - 按合同类型筛选

**验收标准**:
- 付款计划准确计算
- 状态更新及时
- 提醒功能有效

#### 功能编号: F006
**功能名称**: 发票管理
**功能描述**: 发票开具和记录管理

**主要功能**:
- F006-01: 发票统计概览
  - 统计卡片展示：总开票金额、已开票金额、待开票金额、本月开票
  - 开票进度可视化展示
  - 按发票类型、时间维度统计
  - 税额统计和分析
- F006-02: 发票记录列表
  - 一个发票一行
  - 显示字段：
    - 合同名称
    - 相对方
    - 合同编号
    - 开票金额
    - 开票时间
    - 开票类型（普票、专票）
    - 收款方
    - 收款人纳税识别号
    - 付款方
    - 付款人纳税识别号
    - 开票代码
    - 开票号码
    - 开票校验码
    - 税率
    - 税额
  - 支持按合同名称、发票类型、开票时间筛选
  - 合同名称支持点击跳转到合同详情的发票记录标签页
  - 表头设置功能
- F006-03: 发票详细信息
  - 显示字段：
    - 合同名称
    - 相对方
    - 合同编号
    - 开票金额
    - 开票时间
    - 开票类型
    - 收款方
    - 收款人纳税识别号
    - 付款方
    - 付款人纳税识别号
    - 开票代码
    - 开票号码
    - 开票校验码
    - 税率
    - 税额
- F006-04: 搜索和过滤功能
  - 支持按合同名称、发票号码模糊搜索
  - 按合同ID、发票类型、状态、开票时间筛选
  - 按开票金额范围筛选
- F006-05: 新建发票
  - 支持直接录入（填写各个字段）
  - 支持自动识别图片
- F006-06: 编辑发票
- F006-07: 存储发票源文件，该文件应该可以在附件接口中查询到

**验收标准**:
- 税额计算准确
- 发票类型完整
- 状态流转正确

### 2.3 相对方管理模块 (P1 - 高优先级)

#### 功能编号: F007
**功能名称**: 客户供应商统一管理
**功能描述**: 整合客户和供应商信息的统一管理

**主要功能**:
- F007-01: 相对方列表展示
  - 显示字段：相对方编码、相对方名称、相对方类型、所属行业、联系人、联系电话、省份、城市、状态、创建时间
  - 相对方类型标识：客户类型（C企业客户、G政府客户）、供应商类型（ORIGINAL原厂、CHANNEL渠道）
  - 支持分页和排序功能
  - 操作列：查看详情、编辑、删除、状态管理
  - 表头设置功能，支持自定义显示列
- F007-02: 高级搜索和过滤
  - 模糊搜索：支持相对方名称、编码、联系人、电话、纳税人识别号等字段
  - 筛选条件：相对方类型、所属行业、状态、省份、城市
  - 每列表头支持独立筛选
- F007-03: 新增相对方表单
  - 基本信息字段：
    ```
    相对方编码（系统自动生成，创建时允许修改，创建相对方后不可修改）
    相对方名称（必填）
    纳税人识别号
    相对方类型（必选，创建后不可修改）：
      - 客户类型：C（企业客户）、G（政府客户）
      - 供应商类型：ORIGINAL（原厂）、CHANNEL（渠道）
    状态（默认ACTIVE）：ACTIVE（活跃）、SUSPENDED（暂停）、BLACKLISTED（黑名单）、DEACTIVATED（已注销）
    所属行业：
      - 客户行业：GOVT（政府机构）、FINANCE（金融服务）、IT（信息技术/互联网）、MANUFACTURING（制造与工业）、RETAIL（零售与消费品）、ENERGY（能源与公用事业）、LOGISTICS（交通与物流）、HEALTHCARE（医疗与健康）、EDUCATION（教育与科研）、REALESTATE（房地产与建筑）、PROFESSIONAL（专业服务）、AGRICULTURE（农林牧渔）、OTHER（其他/未分类）
      - 供应商行业：HARDWARE（硬件产品供应商）、SOFTWARE（软件产品供应商）、INTEGRATION（集成/服务供应商）
    负责人姓名
    负责人ID（可以为空）
    备注
    ```
  - 地区信息字段：
    ```
    省份代码
    省份名称
    城市代码
    城市名称
    ```
  - 联系信息字段：
    ```
    联系人
    联系电话（支持手机号、座机号、400电话等格式）
    电子邮箱
    联系地址
    联系备注
    官网地址
    ```
  - 开票信息字段：
    ```
    开户银行
    银行账号
    开票地址
    开票电话
    开票备注
    ```
  - 自定义字段：支持在表头设置中定义的自定义字段
- F007-04: 相对方详情查看（抽屉式）
  - 多标签页展示：基本信息、联系信息、开票信息、收款信息（仅供应商）、关联项目、关联合同
  - 基本信息标签页：显示所有基本字段，禁止修改字段使用特殊样式标识
  - 联系信息标签页：显示完整联系方式和地区信息
  - 开票信息标签页：显示开票相关的所有字段
  - 收款信息标签页（仅供应商）：显示供应商收款账户信息列表
  - 关联项目标签页：显示该相对方关联的所有项目
  - 关联合同标签页：显示该相对方关联的所有合同
  - 自定义字段标签页：显示所有自定义字段信息
- F007-05: 相对方信息编辑
  - 禁止修改字段：相对方编码、相对方类型（使用禁用样式显示）
  - 可编辑字段：除编码和类型外的所有字段，包括自定义字段
  - 实时保存：每个输入框的修改都是及时生效，无需保存、重置按钮
  - 字段验证：名称唯一性、税号唯一性、电话格式、邮箱格式等
  - 根据相对方类型动态显示/隐藏特定字段
- F007-06: 供应商收款信息管理（仅供应商类型）
  - 收款信息列表展示：开户银行、银行账号、是否默认账户、备注、操作
  - 新增收款信息功能
  - 收款信息字段：
    ```
    开户银行（必填）
    银行账号（必填）
    是否默认账户（每个供应商只能有一个默认账户）
    备注
    ```
  - 编辑和删除收款信息
  - 设置默认账户功能
  - 表头设置功能
- F007-07: 表头设置功能
  - 字段分类管理：
    - 系统字段：相对方编码、相对方名称、相对方类型、所属行业、联系人、联系电话、省份、城市、状态、创建时间等
    - 自定义字段：用户可自行定义的业务字段
  - 新增自定义字段：
    - 字段类型：文本、数字、日期、单选、多选、货币、布尔值
    - 字段名称：用户自定义字段显示名称
    - 字段属性：选项值（单选/多选类型）
  - 字段管理操作：
    - 修改字段：字段名称. 类型禁止修改
    - 删除字段：删除不再需要的自定义字段
    - 字段排序：调整字段在表单中的显示顺序
    - 字段启用：关闭或者启用
  - 设置持久化：表头设置结果保存到数据库，支持用户个性化配置
- F007-08: 导入导出功能
  - 支持下载Excel模板（区分客户模板和供应商模板，包含自定义字段）
  - 支持批量导入相对方数据（包含自定义字段数据）
  - 导入数据验证：字段格式、唯一性、必填项检查
  - 导入结果反馈：成功数量、失败数量、错误详情
  - 支持导出当前筛选结果为Excel文件（包含自定义字段）


**验收标准**:
- 客户和供应商数据完整整合
- 字段根据类型动态显示/隐藏
- 编码和类型字段创建后不可修改
- 供应商收款信息管理功能完整
- 表头设置功能完整，自定义字段在新建、编辑、详情中正确显示
- 状态管理功能正常
- 数据导入导出功能稳定，支持自定义字段
- 搜索和筛选响应时间 < 1秒

### 2.4 项目管理模块 (P1 - 高优先级)

#### 功能编号: F008
**功能名称**: 项目信息管理
**功能描述**: 项目基础信息和状态管理

**主要功能**:
- F008-01: 项目列表展示
  - 显示字段：项目编码、项目名称、项目类型、关联客户、项目状态、负责人、开始日期、结束日期、预算金额、创建时间
  - 项目状态标识：PLANNING（规划中）、EXECUTING（执行中）、COMPLETED（已完成）、SUSPENDED（暂停）、CANCELLED（已取消）
  - 支持分页和排序功能
  - 操作列：查看详情、编辑、删除、状态管理
  - 表头设置功能，支持自定义显示列
- F008-02: 高级搜索和过滤
  - 模糊搜索：支持项目名称、编码、负责人、客户名称等字段
  - 筛选条件：项目类型、项目状态、关联客户、负责人、时间范围
  - 每列表头支持独立筛选
  - 每列表头支持排序：正序、倒序
- F008-03: 新增项目表单
  - 基本信息字段：
    ```
    项目编码（系统自动生成，创建时允许修改，创建项目后不可修改）
    项目名称（必填）
    项目类型（支持多选，逗号分隔）：system_integration（系统集成）、software_development（软件开发）、consulting（咨询服务）、maintenance（运维服务）、training（培训服务）、other（其他）
    关联客户（必选，从相对方中选择客户类型）
    项目状态（默认PLANNING）：PLANNING（规划中）、EXECUTING（执行中）、COMPLETED（已完成）、SUSPENDED（暂停）、CANCELLED（已取消）
    负责人姓名
    负责人ID
    项目描述
    备注
    ```
  - 时间信息字段：
    ```
    开始日期
    结束日期
    实际开始日期
    实际结束日期
    ```
  - 财务信息字段：
    ```
    预算金额
    实际金额
    ```
  - 完成信息字段：
    ```
    完成时间（项目实际完成时间）
    完成备注（项目完成时的备注信息）
    ```
  - 自定义字段：支持在表头设置中定义的自定义字段
- F008-04: 项目详情查看（抽屉式）
- F008-05: 项目信息编辑
  - 禁止修改字段：项目编码（使用禁用样式显示）、
  - 可编辑字段：除编码外的所有字段，包括自定义字段
  - 实时保存：每个输入框的修改都是及时生效，无需保存、重置按钮
  - 字段验证：名称唯一性、日期逻辑性、金额格式等
  - 状态变更控制：某些状态变更需要确认操作
- F008-06: 表头设置功能
  - 字段分类管理：
    - 系统字段：项目编码、项目名称、项目类型、关联客户、项目状态、负责人、开始日期、结束日期、预算金额、创建时间等
    - 自定义字段：用户可自行定义的业务字段
  - 新增自定义字段：
    - 字段类型：文本、数字、日期、单选、多选、货币、布尔值
    - 字段名称：用户自定义字段显示名称
    - 字段属性：是否必填、默认值、选项值（单选/多选类型）
    - 字段分组：基本信息、时间信息、财务信息、业务信息等
  - 字段管理操作：
    - 修改字段：字段名称、属性、分组等
    - 删除字段：删除不再需要的自定义字段
    - 字段排序：调整字段在表单中的显示顺序
  - 显示控制：
    - 列表显示：选择在项目列表中显示的字段
    - 表单显示：选择在新建/编辑表单中显示的字段
    - 详情显示：选择在详情页面中显示的字段
  - 设置持久化：表头设置结果保存到数据库，支持用户个性化配置
- F008-07: 导入导出功能
  - 支持下载Excel模板（包含自定义字段）
  - 支持批量导入项目数据（包含自定义字段数据）
  - 导入数据验证：字段格式、唯一性、必填项检查、客户关联验证
  - 导入结果反馈：成功数量、失败数量、错误详情
  - 支持导出当前筛选结果为Excel文件（包含自定义字段）

**验收标准**:
- 项目信息完整，支持多种项目类型
- 与客户关联关系清晰，编码规则正确
- 状态更新及时，状态流转逻辑正确
- 关联合同管理功能完整
- 表头设置功能完整，自定义字段在新建、编辑、详情中正确显示
- 项目完成功能正常，时间和备注记录准确
- 数据导入导出功能稳定，支持自定义字段
- 搜索和筛选响应时间 < 1秒

### 2.5 编码规则

```
这个操作流程表明：
1.  **项目**是围绕**客户**展开的。
2.  **销售合同**是针对**客户项目**签订的。
3.  **采购合同**也是为了**支持（或服务于）某个项目**而签订的，因此它间接与客户相关联。

基于此理解，我们将简化编码结构，确保所有合同编码都能清晰地通过项目编码追溯到所属客户。

---

## **编码规则设计 (基于 `[客户]-[项目]-[合同]` 强关联)**

### **1. 客户编码 (Client Code)**

* **目的：** 唯一标识系统中的每个客户（包括企业客户和政府机构）。
* **规则：** `[前缀] + [两位年份] + [月日] + [流水号]`
* **格式说明：**
    * **前缀 (1位字符)：** `C` (企业客户), `G` (政府客户)。
    * **两位年份 (2位数字)：** `YY` (例如 `25` 代表 2025年)。
    * **月日 (4位数字)：** `MMDD` (例如 `0606` 代表 6月6日)。
    * **流水号 (3位数字)：** `NNN` (当天内递增序号，从 `001` 开始，不足三位补零)。
* **示例：**
    * `C250606001` (2025年6月6日创建的第1个企业客户)
    * `G250607002` (2025年6月7日创建的第2个政府客户)
* **总长度：10位**
* **生成方式：** 系统自动生成。

---

### **2. 供应商编码 (Vendor Code)**

* **目的：** 唯一标识系统中的每个供应商。
* **规则：** `[前缀] + [两位年份] + [月日] + [流水号]`
* **格式说明：**
    * **前缀 (1位字符)：** `V` (供应商)。
    * **两位年份 (2位数字)：** `YY`。
    * **月日 (4位数字)：** `MMDD`。
    * **流水号 (3位数字)：** `NNN` (当天内递增序号，从 `001` 开始，不足三位补零)。
* **示例：**
    * `V250606001` (2025年6月6日创建的第1个供应商)
* **总长度：10位**
* **生成方式：** 系统自动生成。

---

### **3. 项目编码 (Project Code)**

* **目的：** 唯一标识系统中的每个项目，并明确该项目是为哪个客户服务的。
* **规则：** `[项目前缀] + [全局项目流水号] + [分隔符] + [客户编码]`
* **格式说明：**
    * **项目前缀 (1位字符)：** `P` (项目)。
    * **全局项目流水号 (3位数字)：** `NNN` (系统内所有项目统一递增的序号，从 `001` 开始，不足三位补零。此流水号全局唯一，不按日期或客户重置)。
    * **分隔符 (1位字符)：** `-`。
    * **客户编码 (10位字符)：** 该项目所关联的**客户编码**。根据您的操作流程，所有项目都应关联一个客户。
* **示例：**
    * `P001-C250606001` (系统内第1个项目，为客户 `C250606001` 服务的)
    * `P002-G250607002` (系统内第2个项目，为政府客户 `G250607002` 服务的)
* **总长度：** 1 + 3 + 1 + 10 = **15位**
* **生成方式：** 系统自动生成。在创建项目时，必须选择一个客户，并将其编码嵌入。
* **重要说明：** 在此设计下，所有项目编码都将包含客户信息。如果存在纯粹的内部项目（不直接为外部客户服务），则需要为其创建或关联一个“内部客户”编码。

---

### **4. 合同编号 (Contract Number)**

* **目的：** 唯一标识系统中的每份合同，明确其销售/采购性质，并通过项目编码清晰地追溯到所属项目及其客户。
* **规则：** `[合同类型前缀] + [合同在项目下的流水号] + [分隔符] + [项目编码]`

* **分类型细化规则：**

    1.  **销售合同 (SAL)：**
        * **规则：** `SAL[合同在项目下的流水号]-[项目编码]`
        * **格式说明：**
            * **合同类型前缀 (3位字符)：** `SAL`。
            * **合同在项目下的流水号 (3位数字)：** `NNN` (在**同一特定项目下**，合同的递增序号，从 `001` 开始，不足三位补零。此流水号是**每个项目独立**的)。
            * **分隔符 (1位字符)：** `-`。
            * **项目编码 (15位字符)：** 完整嵌入该销售合同所属项目的编码。
        * **示例：** `SAL001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份销售合同)
        * **总长度：** 3 + 3 + 1 + 15 = **22位**

    2.  **采购合同 (PUR)：**
        * **规则：** `PUR[合同在项目下的流水号]-[项目编码]`
        * **格式说明：**
            * **合同类型前缀 (3位字符)：** `PUR`。
            * **合同在项目下的流水号 (3位数字)：** `NNN` (规则同销售合同)。
            * **分隔符 (1位字符)：** `-`。
            * **项目编码 (15位字符)：** 完整嵌入该采购合同所属项目的编码。
        * **说明：** 根据您的操作流程，采购合同是为了支持某个**客户项目**而签订的。因此，通过嵌入项目编码，该采购合同的编号中就**天然包含了客户信息**（因为项目编码中已包含客户ID）。
        * **示例：** `PUR001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份采购合同，客户信息 `C250606001` 已清晰可见)
        * **总长度：** 3 + 3 + 1 + 15 = **22位**

    3.  **其他类型合同 (OTH)：**
        * **规则：** `OTH[合同在项目下的流水号]-[项目编码]`
        * **格式说明：** 同销售合同。
        * **示例：** `OTH001-P001-C250606001` (为项目 `P001-C250606001` 签署的第1份其他类型合同)
        * **总长度：22位**

    4. **多方合同（MUL）：**

        * **规则：** `SAL[合同在项目下的流水号]-[项目编码]-MUL`
        * **规则说明 **
            * **合同类型前缀 (3位字符)：** `SAL`。
            * **合同在项目下的流水号 (3位数字)：** `NNN` (在**同一特定项目下**，合同的递增序号，从 `001` 开始，不足三位补零。此流水号是**每个项目独立**的)。
            * **分隔符 (1位字符)：** `-`。
            * **项目编码 (15位字符)：** 完整嵌入该销售合同所属项目的编码。
            * **MUL: ** 表示多方合同
        * **示例：** `PUR001-P001-C250606001-MUT` (为项目 `P001-C250606001` 签署的第1份多方合同)
---

### **这套方案的核心优势与实现考量：**

1.  **高度一致性与可追溯性：**
    * 所有层级（客户、供应商、项目、合同）的编码风格统一。
    * 从**合同编号**（例如 `SAL/PUR`）可直接判断业务类型。
    * 从**合同编号**可直接追溯到**项目ID**（例如 `P001-C250606001`）。
    * 从**项目ID**可直接追溯到**客户ID**（例如 `C250606001`）。
    * **解决了采购合同中体现客户信息的需求，且编码长度保持一致，避免了复杂度和过长的问题。**

2.  **唯一性保障：** 各层级的流水号和关联ID的组合确保了编码的全局唯一性。

3.  **简洁性：** 合同编码最大长度为22位，相较于之前33位的方案，更易于显示和管理。

4.  **实现逻辑：**
    * **项目创建：** 强制关联一个客户。
    * **合同创建：** 强制关联一个项目。
    * **流水号管理：**
        * 客户/供应商流水号：每日重置。
        * 项目全局流水号：全局自增。
        * 合同在项目下的流水号：每个项目独立维护自增。
        * 所有流水号的生成都必须确保**原子性**，以防止并发冲突。

这套编码规则完美契合了您 `[客户]-[项目]-[销售合同]-[供应商]-[采购合同]` 的操作流程，并提供了一套简洁、清晰、可追溯的编码体系。
```


### 2.5 通用模块

#### 表头设置：F009

**功能名称**: 表头设置管理
**功能描述**: 表头管理

- F009-01: 自定义字段创建
  - 用户输入字段显示名称（中文），如"所属税务局"
  - 选择字段类型：文本、数字、日期、单选、多选、货币、布尔值
  - 单选/多选类型支持配置选项值

- F008-09-02: 字段管理操作
  - 查看已创建的自定义字段列表
  - 修改字段名称
  - 禁止修改类型
  - 删除不再需要的自定义字段
  - 启用/禁用字段功能

- F008-09-03: 多模块支持
  - 相对方模块支持自定义字段
  - 合同模块支持自定义字段
  - 供应商模块支持自定义字段
  - 不同模块的自定义字段相互独立

- F008-09-04: 数据录入和显示
  - 新建/编辑表单中动态显示自定义字段
  - 列表页面动态显示自定义字段列
  - 详情页面显示自定义字段值

- F008-09-05: 数据导入导出
  - Excel导出包含自定义字段数据
  - Excel导入支持自定义字段数据
  - 模板下载包含自定义字段列

**使用场景示例**:
1. 用户在相对方管理页面点击"表头设置"
2. 用户输入列名"所属税务局"，选择列类型"文本"
3. 系统保存自定义字段配置
4. 用户在新建相对方时，表单中出现"所属税务局"输入框
5. 用户输入"深圳市南山区税务局"
6. 保存后，相对方列表中显示该自定义字段列和值

**验收标准**:
- 自定义字段创建、修改、删除功能正常
- 字段类型验证和数据存储正确
- 表单和列表动态渲染自定义字段
- 数据导入导出包含自定义字段
- 不同模块的自定义字段相互独立
- 字段名称自动生成规则正确且唯一

## 3. 非功能性需求

### 3.1 性能要求
- 页面加载时间 < 2秒
- 搜索响应时间 < 1秒
- 支持并发用户数 > 100

### 3.2 兼容性要求
- 支持主流浏览器：Chrome、Firefox、Safari、Edge
- 支持移动端响应式布局
- 支持屏幕分辨率：1024x768 及以上

### 3.3 安全性要求
- 数据本地存储加密
- 操作日志记录
- 权限控制机制

### 3.4 数据存储要求

#### 功能编号: F009
**功能名称**: 合同附件本地存储管理
**功能描述**: 合同附件必须存储在本地服务器磁盘，确保数据安全和可访问性

**主要功能**:
- F009-01: 本地磁盘存储架构
  - 所有合同附件必须存储在本地服务器磁盘
  - 禁止使用云存储或外部存储服务
  - 存储路径结构化管理，便于维护和备份
- F009-02: 合同文件夹管理
  - 每个合同创建独立的文件夹
  - 文件夹命名规则：使用合同编号作为文件夹名称
  - 该合同的所有附件必须存储在对应文件夹下
  - 文件夹路径示例：`/contract-files/PUR001-P001-C250704001/`
- F009-03: 文件存储规范
  - 保持原始文件名和扩展名
  - 文件名冲突时自动添加序号后缀
  - 支持多种文件格式：PDF、Word、Excel、图片等
  - 文件大小限制：单个文件不超过10MB
- F009-04: 数据安全保障
  - 即使系统崩溃，登录服务器仍可直接访问附件文件
  - 文件权限设置：仅系统用户可读写
  - 定期备份机制，防止数据丢失
  - 文件完整性校验，确保数据不被篡改

**验收标准**:
- 附件上传后立即存储到本地磁盘
- 文件夹结构清晰，按合同编号组织
- 系统故障时文件仍可通过服务器直接访问
- 文件存储路径与数据库记录保持一致

## 4. 验收标准

### 4.1 功能验收
- 所有功能点按规格实现
- 用户操作流程顺畅
- 数据展示准确完整

### 4.2 界面验收
- UI设计符合原型要求
- 响应式布局正常
- 交互体验良好

### 4.3 性能验收
- 满足性能指标要求
- 兼容性测试通过
- 稳定性测试通过

