# 项目管理自定义字段API使用指南

本文档介绍如何在项目管理模块中使用自定义字段功能。

## 目录

1. [创建项目自定义字段](#1-创建项目自定义字段)
2. [设置项目自定义字段启用状态](#2-设置项目自定义字段启用状态)
3. [新建项目时设置自定义字段值](#3-新建项目时设置自定义字段值)
4. [项目自定义字段在GET接口中的展示](#项目自定义字段在get接口中的展示)

## 1. 创建项目自定义字段

### API接口
```
POST /api/v1/custom-fields/
```

### 请求参数

```json
{
    "field_name": "字段名称",           // 必填，字符串，字段的显示名称（同一模块下不能重复）
    "field_type": "字段类型",          // 必填，字符串，见下方字段类型说明
    "target_model": "project",        // 必填，固定值为"project"
    "options": "选项值",              // 可选，选择类型字段必填，见下方说明
    "is_active": true,                // 可选，布尔值，是否启用，默认true
    "description": "字段描述",         // 可选，字符串，字段说明
    "default_value": "默认值"          // 可选，字符串，字段默认值
}
```

### 字段类型

- `text`: 文本字段
- `number`: 数字字段
- `currency`: 货币字段
- `date`: 日期字段
- `boolean`: 布尔字段
- `select`: 单选字段
- `multiselect`: 多选字段

### 选择类型字段的额外参数
对于 `select` 和 `multiselect` 类型，需要提供 `options` 参数，支持两种格式：

**格式1：字符串数组**
```json
{
    "field_name": "项目优先级",
    "field_type": "select",
    "target_model": "project",
    "options": ["高优先级", "中优先级", "低优先级"]
}
```

**格式2：逗号分隔字符串**
```json
{
    "field_name": "技术栈",
    "field_type": "multiselect",
    "target_model": "project",
    "options": "Java,Python,JavaScript,React,Vue"
}
```

系统会直接使用您提供的显示名称作为选项值进行存储。

### 重要说明

1. **字段名称唯一性**: 同一个 `target_model` 下，`field_name` 必须唯一
2. **字段状态**: 只有 `is_active=true` 的字段才会在API中生效
3. **选项格式**: 单选和多选字段支持数组和逗号分隔字符串两种格式

### 创建示例

#### 1. 创建文本字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "项目备注",
    "field_type": "text",
    "target_model": "project",
    "description": "项目相关备注信息"
  }'
```

#### 2. 创建单选字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "项目优先级",
    "field_type": "select",
    "target_model": "project",
    "options": ["高优先级", "中优先级", "低优先级"]
  }'
```

#### 3. 创建多选字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "技术栈",
    "field_type": "multiselect",
    "target_model": "project",
    "options": "Java,Python,JavaScript,React,Vue"
  }'
```

#### 4. 创建日期字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "里程碑日期",
    "field_type": "date",
    "target_model": "project",
    "description": "重要里程碑日期"
  }'
```

#### 5. 创建货币字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "额外预算",
    "field_type": "currency",
    "target_model": "project",
    "description": "项目额外预算（万元）"
  }'
```

## 2. 设置项目自定义字段启用状态

### 接口信息
- **URL**: `PATCH /api/v1/custom-fields/{id}/set_active_status/`
- **方法**: PATCH
- **认证**: 需要JWT Token

### 请求参数
```json
{
    "is_active": true  // 必填，布尔值，true=启用，false=禁用
}
```

### 使用示例

#### 禁用项目自定义字段
```bash
curl -X PATCH "http://localhost:8000/api/v1/custom-fields/{field_id}/set_active_status/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": false
  }'
```

#### 启用项目自定义字段
```bash
curl -X PATCH "http://localhost:8000/api/v1/custom-fields/{field_id}/set_active_status/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": true
  }'
```

### 响应示例
```json
{
    "code": 2000,
    "msg": "自定义字段禁用成功",
    "data": {
        "id": "uuid",
        "field_name": "项目优先级",
        "field_type": "select",
        "target_model": "project",
        "is_active": false,
        // ... 其他字段信息
    }
}
```

### 重要说明
1. **参数验证**: `is_active` 参数是必传的，且必须是布尔值
2. **状态影响**: 禁用的字段不会在项目API响应中显示，也不接受新的字段值设置
3. **数据保留**: 禁用字段不会删除已存在的项目字段值，重新启用后数据仍然存在

## 3. 新建项目时设置自定义字段值

### API接口
```
POST /api/v1/projects/
```

在创建项目时，通过 `custom_fields` 参数传递自定义字段的值：

```json
{
    "name": "项目名称",
    "type": "system_integration",
    "customer_id": "customer_uuid",
    "sales_manager_id": "manager_id",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    // ... 其他基础字段
    "custom_fields": [
        {
            "field_name": "字段名称",
            "value": "字段值"
        }
    ]
}
```

### 自定义字段值格式

#### 1. 文本字段 (text)
```json
{
    "field_name": "项目备注",
    "value": "这是一个重要项目"
}
```

#### 2. 数字字段 (number/currency)
```json
{
    "field_name": "额外预算",
    "value": "50"
}
```

#### 3. 日期字段 (date)
```json
{
    "field_name": "里程碑日期",
    "value": "2024-06-15"
}
```

#### 4. 布尔字段 (boolean)
```json
{
    "field_name": "是否关键项目",
    "value": "true"
}
```

#### 5. 单选字段 (select)
```json
{
    "field_name": "项目优先级",
    "value": "高优先级"  // 直接使用选项名称
}
```

#### 6. 多选字段 (multiselect)
```json
{
    "field_name": "技术栈",
    "value": "Java,Python,JavaScript"  // 多个选项名称用逗号分隔
}
```

### 完整创建示例

```bash
curl -X POST "http://localhost:8000/api/v1/projects/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ERP系统开发项目",
    "type": "system_integration",
    "customer_id": "customer-uuid-here",
    "sales_manager_id": "manager-id-here",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "budget": 1000000,
    "expected_profit_rate": 25,
    "custom_fields": [
        {
            "field_name": "项目备注",
            "value": "重要的ERP系统开发项目"
        },
        {
            "field_name": "项目优先级",
            "value": "高优先级"
        },
        {
            "field_name": "技术栈",
            "value": "Java,Python"
        },
        {
            "field_name": "里程碑日期",
            "value": "2024-06-15"
        },
        {
            "field_name": "额外预算",
            "value": "50"
        }
    ]
  }'
```

### 响应示例

```json
{
    "code": 2001,
    "data": {
        "id": "project-uuid",
        "code": "P001-C250730001",
        "name": "ERP系统开发项目",
        "type": "system_integration",
        // ... 其他基础字段
    },
    "msg": "项目创建成功"
}
```

### 注意事项

1. **字段名称**: `field_name` 必须是已创建的自定义字段的名称（同一模块下唯一）
2. **字段状态**: 只有 `is_active=true` 的自定义字段才会在API中显示和接受输入
3. **值格式**: 所有值都以字符串形式传递，系统会根据字段类型进行转换
4. **必填验证**: 如果自定义字段设置为必填，创建项目时必须提供对应的值
5. **多选分隔**: 多选字段的多个值用英文逗号分隔
6. **日期格式**: 日期字段使用 YYYY-MM-DD 格式
7. **布尔值**: 布尔字段使用 "true" 或 "false" 字符串

## 项目自定义字段在GET接口中的展示

### 自动展开到顶级字段

当项目有自定义字段值时，GET /projects/ 和 GET /projects/{id}/ 接口会自动将活跃的自定义字段展开到响应的顶级：

```json
{
    "code": 2000,
    "data": {
        "id": "project-uuid",
        "code": "P001-C250730001",
        "name": "ERP系统开发项目",
        "type": "system_integration",
        "status": "planning",
        "customer_code": "C250730001",
        "customer_name": "北京科技有限公司",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        // ... 其他基础字段

        // 自定义字段直接作为顶级字段
        "项目备注": "重要的ERP系统开发项目",
        "项目优先级": "高优先级",
        "技术栈": "Java,Python",
        "里程碑日期": "2024-06-15",
        "额外预算": "50"
    }
}
```

### 列表接口示例

```bash
curl "http://localhost:8000/api/v1/projects/?limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

响应：
```json
{
    "code": 2000,
    "data": {
        "total": 1,
        "page": 1,
        "limit": 10,
        "items": [
            {
                "id": "project-uuid",
                "code": "P001-C250730001",
                "name": "ERP系统开发项目",
                "type": "system_integration",
                "status": "planning",
                // ... 其他基础字段

                // 自定义字段展开
                "项目备注": "重要的ERP系统开发项目",
                "项目优先级": "高优先级",
                "技术栈": "Java,Python"
            }
        ]
    }
}
```

### 详情接口示例

```bash
curl "http://localhost:8000/api/v1/projects/{project_id}/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 字段过滤规则

1. **只显示活跃字段**: 只有 `is_active=true` 的自定义字段才会在响应中显示
2. **动态字段**: 如果禁用某个自定义字段，该字段会立即从API响应中消失
3. **无值处理**: 如果项目没有设置某个自定义字段的值，该字段不会出现在响应中

### 更新项目自定义字段

#### PUT 更新（完整更新）
```bash
curl -X PUT "http://localhost:8000/api/v1/projects/{project_id}/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的项目名称",
    "type": "system_integration",
    "customer_id": "customer-uuid",
    "sales_manager_id": "manager-id",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "custom_fields": [
        {
            "field_name": "项目优先级",
            "value": "中优先级"
        }
    ]
  }'
```

#### PATCH 更新（部分更新）
```bash
curl -X PATCH "http://localhost:8000/api/v1/projects/{project_id}/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "custom_fields": [
        {
            "field_name": "项目优先级",
            "value": "低优先级"
        },
        {
            "field_name": "项目备注",
            "value": "更新后的备注信息"
        }
    ]
  }'
```

## 常见问题

### Q: 如何查看项目模块可用的自定义字段？
A: 使用以下接口查询：
```bash
curl "http://localhost:8000/api/v1/custom-fields/?target_model=project" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Q: 自定义字段值的数据类型如何处理？
A: 所有自定义字段值都以字符串形式传递和存储，前端需要根据字段类型进行相应的显示和验证。

### Q: 如何删除项目的自定义字段值？
A: 在更新项目时，不传递对应的自定义字段即可保持原值不变。如需清空，传递空字符串作为值。

### Q: 自定义字段是否支持搜索和过滤？
A: 当前版本暂不支持基于自定义字段的搜索和过滤功能。
