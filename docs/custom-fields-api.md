# 自定义字段API使用指南

本文档介绍如何使用自定义字段API来创建字段定义和在相对方中设置字段值。

## 目录

1. [创建自定义字段](#1-创建自定义字段)
2. [设置自定义字段启用状态](#2-设置自定义字段启用状态)
3. [新建相对方时设置自定义字段值](#3-新建相对方时设置自定义字段值)
4. [自定义字段在GET接口中的展示](#自定义字段在get接口中的展示)

## 1. 创建自定义字段

### 接口信息
- **URL**: `POST /api/v1/custom-fields/`
- **方法**: POST
- **认证**: 需要JWT Token

### 请求参数说明

#### 基础字段参数
```json
{
    "field_name": "字段名称",           // 必填，字符串，字段的显示名称（同一模块下不能重复）
    "field_type": "字段类型",          // 必填，字符串，见下方字段类型说明
    "target_model": "目标模块",        // 必填，字符串，可选值：partner、project
    "options": "选项值",              // 可选，选择类型字段必填，见下方说明
    "is_active": true,                // 可选，布尔值，是否启用，默认true
    "description": "字段描述",         // 可选，字符串，字段说明
    "default_value": "默认值"          // 可选，字符串，字段默认值
}
```

#### 字段类型说明
- `text`: 文本字段
- `number`: 数字字段
- `date`: 日期字段
- `select`: 单选字段（需要提供选项）
- `multiselect`: 多选字段（需要提供选项）
- `currency`: 货币字段
- `boolean`: 布尔值字段

#### 选择类型字段的额外参数
对于 `select` 和 `multiselect` 类型，需要提供 `options` 参数，支持两种格式：

**格式1：字符串数组**
```json
{
    "field_name": "客户等级",
    "field_type": "select",
    "target_model": "partner",
    "options": ["A级客户", "B级客户", "C级客户"]
}
```

**格式2：逗号分隔字符串**
```json
{
    "field_name": "客户等级",
    "field_type": "select",
    "target_model": "partner",
    "options": "A级客户,B级客户,C级客户"
}
```

系统会直接使用您提供的显示名称作为选项值进行存储。

### 重要说明

1. **字段名称唯一性**: 同一个 `target_model` 下，`field_name` 必须唯一
2. **字段状态**: 只有 `is_active=true` 的字段才会在API中生效
3. **选项格式**: 单选和多选字段支持数组和逗号分隔字符串两种格式

### 创建示例

#### 1. 创建文本字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "客户备注",
    "field_type": "text",
    "target_model": "partner",
    "description": "客户的额外备注信息"
  }'
```

#### 2. 创建数字字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "年营业额",
    "field_type": "currency",
    "target_model": "partner",
    "description": "客户年营业额（万元）"
  }'
```

#### 3. 创建单选字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "客户等级",
    "field_type": "select",
    "target_model": "partner",
    "options": ["A级客户", "B级客户", "C级客户"]
  }'
```

#### 4. 创建多选字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "服务类型",
    "field_type": "multiselect",
    "target_model": "partner",
    "options": "咨询服务,开发服务,维护服务,培训服务"
  }'
```

#### 5. 创建日期字段
```bash
curl -X POST "http://localhost:8000/api/v1/custom-fields/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "field_name": "合作开始日期",
    "field_type": "date",
    "target_model": "partner",
    "description": "与客户开始合作的日期"
  }'
```

## 2. 设置自定义字段启用状态

### 接口信息
- **URL**: `PATCH /api/v1/custom-fields/{id}/set_active_status/`
- **方法**: PATCH
- **认证**: 需要JWT Token

### 请求参数
```json
{
    "is_active": true  // 必填，布尔值，true=启用，false=禁用
}
```

### 使用示例

#### 禁用自定义字段
```bash
curl -X PATCH "http://localhost:8000/api/v1/custom-fields/{field_id}/set_active_status/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": false
  }'
```

#### 启用自定义字段
```bash
curl -X PATCH "http://localhost:8000/api/v1/custom-fields/{field_id}/set_active_status/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": true
  }'
```

### 响应示例
```json
{
    "code": 2000,
    "msg": "自定义字段禁用成功",
    "data": {
        "id": "uuid",
        "field_name": "客户等级",
        "field_type": "select",
        "target_model": "partner",
        "is_active": false,
        // ... 其他字段信息
    }
}
```

### 重要说明
1. **参数验证**: `is_active` 参数是必传的，且必须是布尔值
2. **状态影响**: 禁用的字段不会在API响应中显示，也不接受新的字段值设置
3. **数据保留**: 禁用字段不会删除已存在的字段值，重新启用后数据仍然存在

## 3. 新建相对方时设置自定义字段值

### 接口信息
- **URL**: `POST /api/v1/partners/`
- **方法**: POST
- **认证**: 需要JWT Token

### 自定义字段值格式

在创建相对方时，通过 `custom_fields` 参数传递自定义字段的值：

```json
{
    "name": "相对方名称",
    "type": "C",
    "industry": "IT",
    // ... 其他基础字段
    "custom_fields": [
        {
            "field_name": "字段名称",
            "value": "字段值"
        }
    ]
}
```

### 不同字段类型的值格式

#### 1. 文本字段 (text)
```json
{
    "field_name": "客户备注",
    "value": "这是一个重要客户"
}
```

#### 2. 数字字段 (number/currency)
```json
{
    "field_name": "年营业额",
    "value": "5000"
}
```

#### 3. 日期字段 (date)
```json
{
    "field_name": "合作开始日期",
    "value": "2024-01-15"
}
```

#### 4. 布尔字段 (boolean)
```json
{
    "field_name": "是否VIP客户",
    "value": "true"
}
```

#### 5. 单选字段 (select)
```json
{
    "field_name": "客户等级",
    "value": "A级客户"  // 直接使用选项名称
}
```

#### 6. 多选字段 (multiselect)
```json
{
    "field_name": "服务类型",
    "value": "咨询服务,开发服务"  // 多个选项名称用逗号分隔
}
```

### 完整创建示例

```bash
curl -X POST "http://localhost:8000/api/v1/partners/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "北京科技有限公司",
    "tax_id": "91110000123456789X",
    "type": "C",
    "status": "active",
    "industry": "IT",
    "province_code": "110000",
    "city_code": "110100",
    "contact_person": "张经理",
    "contact_phone": "13800138000",
    "contact_email": "<EMAIL>",
    "owner_name": "李销售",
    "custom_fields": [
        {
            "field_name": "客户备注",
            "value": "重要合作伙伴，优先处理"
        },
        {
            "field_name": "年营业额",
            "value": "8000"
        },
        {
            "field_name": "客户等级",
            "value": "A级客户"
        },
        {
            "field_name": "服务类型",
            "value": "咨询服务,开发服务,维护服务"
        },
        {
            "field_name": "合作开始日期",
            "value": "2024-01-01"
        }
    ]
  }'
```

### 注意事项

1. **字段名称**: `field_name` 必须是已创建的自定义字段的名称（同一模块下唯一）
2. **字段状态**: 只有 `is_active=true` 的自定义字段才会在API中显示和接受输入
3. **值格式**: 所有值都以字符串形式传递，系统会根据字段类型进行转换
4. **必填验证**: 如果自定义字段设置为必填，创建相对方时必须提供对应的值
5. **多选分隔**: 多选字段的多个值用英文逗号分隔
6. **日期格式**: 日期字段使用 YYYY-MM-DD 格式
7. **布尔值**: 布尔字段使用 "true" 或 "false" 字符串

### 响应示例

成功创建后，响应中会包含自定义字段的值：

```json
{
    "code": 2001,
    "data": {
        "id": "uuid",
        "name": "北京科技有限公司",
        // ... 其他基础字段
        "custom_fields": {
            "客户备注": {
                "field_id": "uuid",
                "field_name": "客户备注",
                "field_type": "text",
                "value": "重要合作伙伴，优先处理"
            },
            "客户等级": {
                "field_id": "uuid",
                "field_name": "客户等级",
                "field_type": "select",
                "value": "A级客户"
            }
        }
    },
    "msg": "相对方创建成功"
}
```

## 自定义字段在GET接口中的展示

### 自动展开到顶级字段

当相对方有自定义字段值时，GET /partners/ 和 GET /partners/{id}/ 接口会自动将活跃的自定义字段展开到响应的顶级：

```json
{
    "code": 2000,
    "data": {
        "id": "uuid",
        "name": "相对方名称",
        "code": "C250730001",
        // ... 其他基础字段

        // 自定义字段直接作为顶级字段
        "成立日期": "2020-01-15",
        "企业规模": "large",
        "所属税务局": "深圳市福田区税务局"
    }
}
```

### 字段过滤规则

1. **只显示活跃字段**: 只有 `is_active=true` 的自定义字段才会在响应中显示
2. **动态字段**: 如果禁用某个自定义字段，该字段会立即从API响应中消失
3. **无值处理**: 如果相对方没有设置某个自定义字段的值，该字段不会出现在响应中
