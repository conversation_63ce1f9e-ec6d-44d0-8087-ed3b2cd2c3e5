# 文件管理系统改进说明

## 概述

根据 v0.2.0 需求规格说明书的要求，对文件管理系统进行了重大改进，主要包括：

1. **存储方式变更**：从 MinIO/RustFS 改为本地文件系统存储
2. **功能简化**：移除分片上传功能，只保留单文件上传
3. **业务逻辑简化**：移除文件上传后自动修改项目/合同状态的逻辑
4. **接口兼容性**：保持现有上传接口的请求参数格式不变

## 主要变更

### 1. 存储架构变更

#### 变更前
```
BaseStorage (抽象基类)
├── MinIOStorage (MinIO 实现)
└── RustFSStorage (RUSTFS 实现)
```

#### 变更后
```
LocalFileSystemStorage (本地文件系统存储)
```

### 2. 文件存储结构

按照需求文档要求，文件存储采用以下结构：

```
media/attachments/
├── contract-files/
│   └── {合同编号}/
│       └── {UUID文件名}
├── project-files/
│   └── {项目编号}/
│       └── {UUID文件名}
├── customer-files/
│   └── {客户编号}/
│       └── {UUID文件名}
└── other-files/
    └── {类型}/
        └── {UUID文件名}
```

### 3. 删除的功能

#### 分片上传相关
- 删除 `/api/v1/attachments/chunk_upload/` 接口
- 删除 `AttachmentChunkUploadSerializer` 序列化器
- 删除分片上传相关的测试用例
- 删除分片上传相关的文档

#### 自动状态更新逻辑
- 移除文件上传后自动修改合同状态的逻辑
- 移除文件上传后自动修改项目状态的逻辑
- 删除 `_check_and_update_contract_status` 方法

#### 外部存储依赖
- 移除 MinIO 相关代码和配置
- 移除 RustFS 相关代码和配置
- 删除相关文档：`RUSTFS_STORAGE.md`、`分片上传使用指南.md`

### 4. 保留的功能

#### 单文件上传
- 保持 `/api/v1/attachments/upload/` 接口不变
- 保持请求参数格式不变
- 保持响应格式不变

#### 文件下载
- 重新实现 `/api/v1/attachments/download/` 接口
- 支持本地文件系统的文件下载

#### 附件管理
- 保持附件列表、详情、删除等功能不变
- 保持附件分类和描述功能不变

## 技术实现

### 1. LocalFileSystemStorage 类

```python
class LocalFileSystemStorage(Storage):
    """本地文件系统存储"""
    
    def __init__(self):
        self.base_path = getattr(settings, 'LOCAL_FILE_STORAGE_PATH',
                                os.path.join(settings.BASE_DIR, 'media', 'attachments'))
    
    def _save(self, name, content):
        """保存文件到本地文件系统"""
        # 实现文件保存逻辑
    
    def delete(self, name):
        """删除文件"""
        # 实现文件删除逻辑
    
    def exists(self, name):
        """检查文件是否存在"""
        # 实现文件存在检查逻辑
    
    def url(self, name):
        """获取文件的访问URL"""
        # 返回下载接口的URL
```

### 2. AttachmentUploadManager 简化

```python
class AttachmentUploadManager:
    """附件上传管理器 - 本地文件系统版本"""
    
    def generate_file_path(self, content_type, original_filename, object_id):
        """生成文件存储路径"""
        # 根据内容类型和对象ID生成路径
    
    def save_file(self, file_obj, file_path):
        """保存文件"""
        # 调用存储后端保存文件
    
    def calculate_md5(self, file_obj):
        """计算文件MD5值"""
        # 计算文件MD5哈希值
```

### 3. 文件下载实现

```python
@action(detail=False, methods=['get'])
def download(self, request):
    """下载附件"""
    file_path = request.GET.get('file_path')
    # 验证文件存在性
    # 读取文件并返回HTTP响应
```

## 配置变更

### 新增配置项

```python
# 本地文件存储路径
LOCAL_FILE_STORAGE_PATH = os.path.join(BASE_DIR, 'media', 'attachments')
```

### 移除的配置项

```python
# MinIO 配置（已移除）
MINIO_ENDPOINT = ...
MINIO_ACCESS_KEY = ...
MINIO_SECRET_KEY = ...
MINIO_BUCKET_NAME = ...

# RustFS 配置（已移除）
RUSTFS_ENDPOINT = ...
RUSTFS_ACCESS_KEY = ...
RUSTFS_SECRET_KEY = ...
RUSTFS_BUCKET_NAME = ...

# 存储后端选择（已移除）
STORAGE_BACKEND = ...
```

## 兼容性说明

### 前端兼容性
- 上传接口的请求参数格式保持不变
- 响应数据格式保持不变
- 下载URL格式有所变化，但功能保持一致

### 数据库兼容性
- 附件表结构保持不变
- 现有附件记录保持不变
- 文件路径格式有所调整

## 测试验证

### 单元测试
- 更新了附件相关的单元测试
- 删除了分片上传相关的测试用例
- 添加了本地文件存储的测试

### 功能测试
- 验证单文件上传功能正常
- 验证文件下载功能正常
- 验证分片上传接口已移除
- 验证业务状态不再自动更新

## 部署注意事项

1. **文件存储目录**：确保 `media/attachments` 目录有适当的读写权限
2. **现有文件迁移**：如果之前使用了 MinIO/RustFS，需要将文件迁移到本地存储
3. **备份策略**：建立本地文件的备份机制
4. **磁盘空间**：监控本地磁盘空间使用情况

## 总结

本次改进成功实现了需求文档中的所有要求：

✅ 存储方式从 MinIO/RustFS 改为本地文件系统  
✅ 移除分片上传功能  
✅ 保持上传接口兼容性  
✅ 移除业务状态自动更新逻辑  
✅ 简化文件管理架构  

系统现在更加简洁、易于维护，同时保持了与前端的完全兼容性。
