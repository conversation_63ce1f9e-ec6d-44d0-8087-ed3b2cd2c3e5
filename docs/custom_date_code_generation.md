# 自定义日期编码生成功能

## 功能概述

为了支持历史数据导入，系统现在支持在创建客户和供应商时手动指定编码生成日期。这样可以确保导入的历史数据使用正确的历史日期生成编码，而不是当前日期。

## 支持的资源

- **客户 (Customer)**: 支持自定义编码日期
- **供应商 (Supplier)**: 支持自定义编码日期
- **项目 (Project)**: 自动继承客户编码，无需额外配置
- **合同 (Contract)**: 自动继承项目编码，无需额外配置

## API 使用方法

### 客户创建 API

**POST** `/api/customers/`

#### 请求参数

在原有的客户创建参数基础上，新增可选参数：

```json
{
  "name": "历史客户公司",
  "type": "C",
  "industry": "IT",
  "contact_person": "张三",
  "phone": "13800138001",
  "code_date": "2023-01-01"  // 可选：编码生成日期，格式：YYYY-MM-DD
}
```

#### 参数说明

- `code_date` (可选): 编码生成日期
  - 格式：`YYYY-MM-DD`
  - 如果不提供，则使用当前日期
  - 主要用于历史数据导入

#### 响应示例

```json
{
  "code": 20001,
  "msg": "客户创建成功",
  "data": {
    "id": "uuid",
    "code": "C230101001",  // 使用2023年1月1日生成的编码
    "name": "历史客户公司",
    // ... 其他字段
  }
}
```

### 供应商创建 API

**POST** `/api/suppliers/`

#### 请求参数

```json
{
  "name": "历史供应商公司",
  "type": "C",
  "status": "ACTIVE",
  "industry": "IT",
  "contact_person": "李四",
  "phone": "13800138002",
  "owner_name": "负责人",
  "code_date": "2023-06-15"  // 可选：编码生成日期
}
```

#### 响应示例

```json
{
  "code": 20001,
  "msg": "供应商创建成功",
  "data": {
    "id": "uuid",
    "code": "V230615001",  // 使用2023年6月15日生成的编码
    "name": "历史供应商公司",
    // ... 其他字段
  }
}
```

## 编码生成规则

### 客户编码
- 格式：`{类型前缀}{年月日}{3位序号}`
- 示例：
  - `C230101001` - 2023年1月1日创建的第1个企业客户
  - `G230615002` - 2023年6月15日创建的第2个政府客户

### 供应商编码
- 格式：`V{年月日}{3位序号}`
- 示例：
  - `V230101001` - 2023年1月1日创建的第1个供应商
  - `V230615002` - 2023年6月15日创建的第2个供应商

### 项目编码
- 格式：`P{全局序号}-{客户编码}`
- 示例：`P001-C230101001`
- 自动继承客户编码，无需手动指定日期

### 合同编码
- 格式：`{类型前缀}{项目内序号}-{项目编码}`
- 示例：`SAL001-P001-C230101001`
- 自动继承项目编码，无需手动指定日期

## 使用场景

### 1. 正常业务操作
不提供 `code_date` 参数，系统自动使用当前日期生成编码：

```bash
curl -X POST http://localhost:8000/api/customers/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "新客户公司",
    "type": "C",
    "industry": "IT",
    "contact_person": "张三",
    "phone": "13800138001"
  }'
```

### 2. 历史数据导入
提供 `code_date` 参数，使用指定的历史日期生成编码：

```bash
curl -X POST http://localhost:8000/api/customers/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "历史客户公司",
    "type": "C",
    "industry": "IT",
    "contact_person": "张三",
    "phone": "13800138001",
    "code_date": "2023-01-01"
  }'
```

## 注意事项

1. **日期格式**: 必须使用 `YYYY-MM-DD` 格式
2. **序号唯一性**: 同一天内的序号会自动递增，确保编码唯一
3. **向后兼容**: 不影响现有的API调用，原有功能保持不变
4. **级联关系**: 项目和合同会自动使用正确的上级编码，无需额外配置
5. **错误处理**: 如果日期格式错误，会返回相应的错误信息

## 测试验证

系统包含完整的测试用例，验证以下场景：
- 使用自定义日期创建客户和供应商
- 同一天创建多个记录时序号正确递增
- 不提供日期时使用当前日期
- 项目和合同正确继承上级编码
- 无效日期格式的错误处理

运行测试：
```bash
python test/test_custom_date_code_generation.py
```

## 实现细节

### 模型层修改
- 修改 `Customer.save()` 和 `Supplier.save()` 方法
- 支持 `code_date` 参数传递
- 兼容字符串和日期对象类型

### 序列化器修改
- 添加 `code_date` 字段到创建序列化器
- 在 `create()` 方法中处理日期参数传递

### 数据库影响
- 无需修改数据库结构
- 完全向后兼容现有数据
